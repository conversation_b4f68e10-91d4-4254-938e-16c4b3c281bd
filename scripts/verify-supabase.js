const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function verifyTables() {
  console.log('Verifying Supabase tables...');
  
  const requiredTables = [
    'api_keys',
    'security_events',
    'rate_limits',
    'key_hwid_history'
  ];

  for (const table of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.error(`❌ Table ${table} is missing or not accessible:`, error.message);
      } else {
        console.log(`✅ Table ${table} exists`);
      }
    } catch (err) {
      console.error(`❌ Error checking table ${table}:`, err.message);
    }
  }
}

async function verifyFunctions() {
  console.log('\nVerifying database functions...');
  
  const requiredFunctions = [
    'generate_api_key',
    'validate_api_key',
    'check_rate_limit',
    'handle_new_key',
    'handle_key_validation'
  ];

  for (const func of requiredFunctions) {
    try {
      // Test each function with a simple query
      const { data, error } = await supabase.rpc('pg_get_functiondef', {
        proname: func
      });
      
      if (error) {
        console.error(`❌ Function ${func} is missing:`, error.message);
      } else {
        console.log(`✅ Function ${func} exists`);
      }
    } catch (err) {
      console.error(`❌ Error checking function ${func}:`, err.message);
    }
  }
}

async function verifyRLS() {
  console.log('\nVerifying Row Level Security...');
  
  const tablesToCheck = [
    'api_keys',
    'security_events',
    'rate_limits',
    'key_hwid_history'
  ];

  for (const table of tablesToCheck) {
    try {
      const { data, error } = await supabase
        .rpc('pg_tables', { 
          schemaname: 'public',
          tablename: table
        });
      
      if (error || !data || data.length === 0) {
        console.error(`❌ Could not verify RLS for ${table}: Table not found`);
        continue;
      }
      
      const { data: rls, error: rlsError } = await supabase
        .rpc('pg_policies', { 
          schemaname: 'public',
          tablename: table
        });
      
      if (rlsError) {
        console.error(`❌ Error checking RLS for ${table}:`, rlsError.message);
      } else if (!rls || rls.length === 0) {
        console.error(`❌ No RLS policies found for ${table}`);
      } else {
        console.log(`✅ RLS is properly configured for ${table} (${rls.length} policies)`);
      }
    } catch (err) {
      console.error(`❌ Error verifying RLS for ${table}:`, err.message);
    }
  }
}

async function main() {
  console.log('Starting Supabase verification...');
  console.log('==============================');
  
  try {
    // Verify connection
    const { data, error } = await supabase.from('api_keys').select('*').limit(1);
    if (error) throw error;
    
    console.log('✅ Successfully connected to Supabase');
    
    // Run verifications
    await verifyTables();
    await verifyFunctions();
    await verifyRLS();
    
    console.log('\n✅ Verification complete!');
  } catch (error) {
    console.error('❌ Error connecting to Supabase:', error.message);
    console.log('\nPlease ensure you have: ');
    console.log('1. Created a .env file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
    console.log('2. Run the SQL migration in your Supabase dashboard');
    console.log('3. Enabled Row Level Security on all tables');
    process.exit(1);
  }
}

main();
