# Security Configuration Guide

## Overview
This guide provides recommended security configurations for the key system implementation.

## Critical Security Settings

### 1. Environment Variables
```bash
# Generate strong random salts (32+ characters)
KEY_SALT=$(openssl rand -hex 32)
HWID_SALT=$(openssl rand -hex 32)
JWT_SECRET=$(openssl rand -hex 64)
SESSION_SECRET=$(openssl rand -hex 32)

# Generate secure admin API keys
ADMIN_API_KEYS=$(openssl rand -hex 32),$(openssl rand -hex 32)
```

### 2. Rate Limiting Configuration

#### Production Settings
```env
# Strict rate limiting for production
RATE_LIMIT_WINDOW_MS=900000      # 15 minutes
RATE_LIMIT_MAX_REQUESTS=50       # 50 requests per 15 min

AUTH_RATE_LIMIT_WINDOW_MS=3600000 # 1 hour
AUTH_RATE_LIMIT_MAX_REQUESTS=5    # 5 auth attempts per hour

KEY_GEN_RATE_LIMIT_WINDOW_MS=300000 # 5 minutes
KEY_GEN_RATE_LIMIT_MAX_REQUESTS=2   # 2 keys per 5 min
```

#### Development Settings
```env
# Relaxed for development
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX_REQUESTS=50
KEY_GEN_RATE_LIMIT_MAX_REQUESTS=10
```

### 3. CORS Configuration

#### Production
```env
CORS_ORIGIN=https://yourdomain.com
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE
```

#### Development
```env
CORS_ORIGIN=http://localhost:3000,http://localhost:5173
CORS_CREDENTIALS=true
```

### 4. Key Expiration Settings

#### Recommended Values
```env
# Public keys expire in 24 hours
PUBLIC_KEY_EXPIRY_HOURS=24

# Admin keys expire in 30 days
ADMIN_KEY_EXPIRY_DAYS=30

# Session timeout (1 hour)
SESSION_TIMEOUT=3600000
```

## Security Headers

### Content Security Policy
```javascript
// In security-middleware.js
contentSecurityPolicy: {
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "https://www.google.com", "https://www.gstatic.com"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", "data:", "blob:"],
    connectSrc: ["'self'", "https://*.supabase.co"],
    fontSrc: ["'self'", "data:"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["https://www.google.com"],
  },
}
```

### Additional Headers
```javascript
// Security headers for production
"Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload"
"X-Content-Type-Options": "nosniff"
"X-Frame-Options": "DENY"
"X-XSS-Protection": "1; mode=block"
"Referrer-Policy": "strict-origin-when-cross-origin"
```

## Database Security

### Row Level Security (RLS)
```sql
-- Enable RLS on all tables
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_profiles ENABLE ROW LEVEL SECURITY;

-- Admin access policy
CREATE POLICY "Admin access" ON api_keys
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'admin' OR
    auth.jwt() ->> 'user_id' = admin_id::text
  );
```

### Encryption at Rest
```sql
-- Use encrypted columns for sensitive data
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Store encrypted HWID
UPDATE api_keys SET hwid = crypt(hwid, gen_salt('bf', 12));
```

## Monitoring & Alerting

### Security Events to Monitor
1. Failed authentication attempts
2. Rate limit violations
3. HWID manipulation attempts
4. Unusual key generation patterns
5. Admin privilege escalations

### Alert Thresholds
```env
# Alert after 10 failed auth attempts in 5 minutes
ALERT_FAILED_AUTH_THRESHOLD=10
ALERT_FAILED_AUTH_WINDOW=300000

# Alert after 100 rate limit violations in 1 hour
ALERT_RATE_LIMIT_THRESHOLD=100
ALERT_RATE_LIMIT_WINDOW=3600000
```

## Backup & Recovery

### Database Backups
```bash
# Daily encrypted backups
pg_dump --no-password --clean --create \
  --format=custom --compress=9 \
  "$DATABASE_URL" | \
  gpg --cipher-algo AES256 --compress-algo 1 \
  --symmetric --output "backup-$(date +%Y%m%d).sql.gpg"
```

### Key Rotation Schedule
- Admin API keys: Every 90 days
- JWT secrets: Every 180 days
- Encryption salts: Every 365 days
- Database passwords: Every 90 days

## Incident Response

### Security Breach Checklist
1. [ ] Rotate all API keys immediately
2. [ ] Invalidate all active sessions
3. [ ] Enable enhanced logging
4. [ ] Review access logs for 30 days prior
5. [ ] Notify affected users
6. [ ] Update security measures
7. [ ] Document lessons learned

### Emergency Contacts
```env
SECURITY_TEAM_EMAIL=<EMAIL>
INCIDENT_RESPONSE_PHONE=+1-xxx-xxx-xxxx
```
