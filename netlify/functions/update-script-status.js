const { createApiHandler, supabase } = require('./utils/api-handler');

module.exports.handler = createApiHandler({
  requireAuth: true,
  async handler(event) {
    try {
      const { id } = event.pathParameters;
      const { is_active } = JSON.parse(event.body);
      
      // Update the script status in the database
      const { data, error } = await supabase
        .from('scripts')
        .update({ is_active })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      
      return {
        statusCode: 200,
        body: JSON.stringify(data)
      };
    } catch (error) {
      console.error('Error updating script status:', error);
      return {
        statusCode: error.statusCode || 500,
        body: JSON.stringify({ error: error.message || 'Internal server error' })
      };
    }
  }
});
