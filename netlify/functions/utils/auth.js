const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Rate limiting configuration
const RATE_LIMIT = {
  WINDOW_MS: 60 * 1000, // 1 minute
  MAX_REQUESTS: 100
};

// Track rate limits by HWID
const rateLimitCache = new Map();

// Helper to log auth events
const logAuthEvent = async (event) => {
  const { data, error } = await supabase.from('auth_events').insert([event]);
  if (error) {
    console.error('Error logging auth event:', error);
  }
};

/**
 * Middleware to authenticate API requests using API key
 */
const authenticateApiKey = async (req, res) => {
  try {
    // Get API key from headers
    const apiKey = req.headers['x-api-key'] || req.query.api_key;
    
    if (!apiKey) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'API key is required' })
      };
    }

    // Validate API key format
    if (typeof apiKey !== 'string' || !/^[a-f0-9]{64}$/i.test(apiKey)) {
      return {
        statusCode: 403,
        body: JSON.stringify({ error: 'Invalid API key format' })
      };
    }

    // Check if API key exists and is active
    const { data: keyData, error: keyError } = await supabase
      .from('api_keys')
      .select('*, admin_profile(*)')
      .eq('key_hash', hashApiKey(apiKey))
      .eq('is_active', true)
      .single();

    if (keyError || !keyData) {
      console.error('Invalid API key attempt:', { error: keyError });
      return {
        statusCode: 403,
        body: JSON.stringify({ error: 'Invalid or inactive API key' })
      };
    }

    // Check if key is expired
    if (new Date(keyData.expires_at) < new Date()) {
      // Auto-deactivate expired keys
      await supabase
        .from('api_keys')
        .update({ is_active: false })
        .eq('id', keyData.id);
      
      return {
        statusCode: 403,
        body: JSON.stringify({ error: 'API key has expired' })
      };
    }

    // Get HWID and user agent from headers
    const clientHwid = req.headers['x-client-hwid'] || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';
    
    // Apply rate limiting by HWID
    const now = Date.now();
    const windowStart = now - RATE_LIMIT.WINDOW_MS;
    
    // Clean up old entries
    for (const [hwid, timestamps] of rateLimitCache.entries()) {
      const validTimestamps = timestamps.filter(ts => ts > windowStart);
      if (validTimestamps.length > 0) {
        rateLimitCache.set(hwid, validTimestamps);
      } else {
        rateLimitCache.delete(hwid);
      }
    }
    
    // Check rate limit
    const requestTimestamps = rateLimitCache.get(clientHwid) || [];
    if (requestTimestamps.length >= RATE_LIMIT.MAX_REQUESTS) {
      // Log rate limit exceeded
      await logAuthEvent({
        event_type: 'rate_limit_exceeded',
        user_id: keyData.admin_id,
        metadata: {
          key_id: keyData.id,
          key_name: keyData.name,
          request_count: requestTimestamps.length,
          path: req.path,
          method: req.httpMethod
        },
        hwid: clientHwid
      });
      
      return {
        statusCode: 429,
        body: JSON.stringify({ 
          error: 'Too many requests',
          retry_after: Math.ceil((requestTimestamps[0] - windowStart) / 1000)
        })
      };
    }
    
    // Update rate limit cache
    rateLimitCache.set(clientHwid, [...requestTimestamps, now]);
    
    // Log the API access
    await logAuthEvent({
      event_type: 'api_access',
      user_id: keyData.admin_id,
      metadata: {
        key_id: keyData.id,
        key_name: keyData.name,
        path: req.path,
        method: req.httpMethod,
        params: req.queryStringParameters
      },
      hwid: clientHwid
    });

    // Update last used timestamp with HWID
    await supabase
      .from('api_keys')
      .update({ 
        last_used_at: new Date().toISOString(),
        last_used_hwid: clientHwid,
        last_used_user_agent: userAgent,
        use_count: (keyData.use_count || 0) + 1
      })
      .eq('id', keyData.id);

    // Attach admin and key info to request context
    return {
      admin: keyData.admin_profile,
      apiKey: {
        id: keyData.id,
        name: keyData.name,
        scopes: keyData.scopes || [],
        permissions: keyData.permissions || []
      },
      clientInfo: {
        ip: clientIp,
        userAgent,
        hwid: req.headers['x-client-hwid']
      }
    };

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error during authentication' })
    };
  }
};

/**
 * Hash API key for secure storage
 */
function hashApiKey(key) {
  if (!key) return null;
  
  // Use a constant-time comparison to prevent timing attacks
  const hash = crypto
    .createHash('sha256')
    .update(key + process.env.API_KEY_SALT)
    .digest('hex');
    
  return hash;
}

/**
 * Generate a new secure API key
 */
function generateApiKey() {
  // Generate a cryptographically secure random key
  const key = `sk_${crypto.randomBytes(32).toString('hex')}`;
  const hash = hashApiKey(key);
  
  // Generate a key fingerprint for display purposes
  const fingerprint = crypto
    .createHash('sha256')
    .update(key)
    .digest('hex')
    .slice(0, 16);
    
  return { 
    key, 
    hash,
    fingerprint: `sk_${fingerprint}`
  };
}

/**
 * Check if admin has required permissions
 */
const hasPermission = (context, requiredPermission) => {
  if (!context || !context.apiKey) return false;
  
  // If no permissions required, allow access
  if (!requiredPermission) return true;
  
  // Check if the API key has the required permission
  return context.apiKey.permissions.includes(requiredPermission) ||
         context.apiKey.permissions.includes('*'); // Wildcard for full access
};

// Clean up rate limit cache periodically
setInterval(() => {
  const now = Date.now();
  const windowStart = now - (RATE_LIMIT.WINDOW_MS * 2); // Clean up entries older than 2 windows
  
  for (const [hwid, timestamps] of rateLimitCache.entries()) {
    const validTimestamps = timestamps.filter(ts => ts > windowStart);
    if (validTimestamps.length > 0) {
      rateLimitCache.set(hwid, validTimestamps);
    } else {
      rateLimitCache.delete(hwid);
    }
  }
}, RATE_LIMIT.WINDOW_MS * 5);

module.exports = {
  authenticateApiKey,
  generateApiKey,
  hashApiKey,
  hasPermission,
  logAuthEvent
};
