const { createClient } = require('@supabase/supabase-js');
const { authenticateApiKey, hasPermission } = require('./auth');

// Initialize Supabase client with service role
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Create an API handler with common functionality
 * @param {Object} options - Handler options
 * @param {Array} options.requiredPermissions - Array of required permissions
 * @param {Function} options.handler - The actual request handler
 * @param {boolean} options.requireAuth - Whether authentication is required (default: true)
 * @returns {Function} The wrapped handler function
 */
function createApiHandler({
  requiredPermissions = [],
  handler,
  requireAuth = true
}) {
  return async (event, context) => {
    // Default CORS headers
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Content-Type': 'application/json'
    };

    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers,
        body: ''
      };
    }

    try {
      // Get client info for logging
      const clientIp = event.headers['x-forwarded-for'] || event.connection.remoteAddress;
      const userAgent = event.headers['user-agent'] || '';
      const hwid = event.headers['x-client-hwid'] || '';

      // Parse request body if present
      let body = {};
      if (event.body) {
        try {
          body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
        } catch (error) {
          console.error('Error parsing request body:', error);
          return {
            statusCode: 400,
            headers,
            body: JSON.stringify({ error: 'Invalid JSON in request body' })
          };
        }
      }

      // Authenticate the request if required
      let auth = {};
      if (requireAuth) {
        const authResult = await authenticateApiKey(event, context);
        if (authResult.statusCode) {
          return { ...authResult, headers };
        }
        auth = authResult;

        // Check permissions if required
        if (requiredPermissions.length > 0) {
          const hasAllPermissions = requiredPermissions.every(permission => 
            hasPermission(auth, permission)
          );
          
          if (!hasAllPermissions) {
            console.warn('Permission denied:', { 
              requiredPermissions,
              hasPermissions: auth.apiKey?.permissions || []
            });
            
            return {
              statusCode: 403,
              headers,
              body: JSON.stringify({ 
                error: 'Insufficient permissions',
                required: requiredPermissions
              })
            };
          }
        }
      }

      // Parse query parameters
      const query = event.queryStringParameters || {};
      
      // Call the actual handler
      const result = await handler({
        event,
        context,
        body,
        query,
        params: event.pathParameters || {},
        headers: event.headers,
        auth,
        supabase,
        clientInfo: {
          ip: clientIp,
          userAgent,
          hwid
        }
      });

      // Ensure the response has the correct format
      const response = {
        statusCode: result.statusCode || 200,
        headers: { ...headers, ...(result.headers || {}) },
        body: typeof result.body === 'string' ? result.body : JSON.stringify(result.body || {})
      };

      return response;

    } catch (error) {
      console.error('API handler error:', error);
      
      // Don't leak internal errors in production
      const errorMessage = process.env.NODE_ENV === 'development' 
        ? error.message 
        : 'An unexpected error occurred';
      
      return {
        statusCode: error.statusCode || 500,
        headers,
        body: JSON.stringify({ 
          error: errorMessage,
          ...(process.env.NODE_ENV === 'development' ? { stack: error.stack } : {})
        })
      };
    }
  };
}

/**
 * Helper to parse and validate request body against a schema
 */
function validateBody(schema) {
  return (body) => {
    const { error, value } = schema.validate(body, { abortEarly: false });
    if (error) {
      const validationError = new Error('Validation error');
      validationError.statusCode = 400;
      validationError.details = error.details.map(d => ({
        message: d.message,
        path: d.path,
        type: d.type
      }));
      throw validationError;
    }
    return value;
  };
}

module.exports = {
  createApiHandler,
  validateBody,
  supabase
};
