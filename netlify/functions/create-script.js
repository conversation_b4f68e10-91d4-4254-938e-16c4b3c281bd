const { create<PERSON><PERSON><PERSON><PERSON><PERSON>, supabase } = require('./utils/api-handler');

module.exports.handler = createApiHandler({
  requireAuth: true,
  async handler(event) {
    try {
      console.log('Headers:', JSON.stringify(event.headers, null, 2));
      console.log('Auth Header:', event.headers.authorization || 'No Authorization header');
      
      const scriptData = JSON.parse(event.body);
      console.log('Script data:', JSON.stringify(scriptData, null, 2));
      
      // Insert the new script into the database
      const { data, error } = await supabase
        .from('scripts')
        .insert(scriptData)
        .select()
        .single();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }
      
      return {
        statusCode: 201,
        body: JSON.stringify(data)
      };
    } catch (error) {
      console.error('Error in create-script handler:', {
        message: error.message,
        stack: error.stack,
        code: error.code,
        details: error.details,
        hint: error.hint,
        error: error.toString()
      });
      
      return {
        statusCode: error.statusCode || 500,
        body: JSON.stringify({ 
          error: 'Failed to create script',
          details: error.message,
          code: error.code
        })
      };
    }
  }
});
