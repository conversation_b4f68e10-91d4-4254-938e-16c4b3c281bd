const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Helper to log security events
const logAdminEvent = async (eventType, keyHash, ipAddress, userAgent, metadata = {}) => {
  await supabase.from('admin_security_events').insert({
    event_type: eventType,
    key_hash_used: keyHash,
    ip_address: ipAddress,
    user_agent: userAgent,
    metadata: metadata
  });
};

exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  if (event.httpMethod !== 'POST') {
    return { statusCode: 405, headers, body: JSON.stringify({ error: 'Method not allowed' }) };
  }

  const ipAddress = event.headers['x-forwarded-for'] || event.headers['client-ip'] || 'unknown';
  const userAgent = event.headers['user-agent'] || 'unknown';

  try {
    const { admin_key } = JSON.parse(event.body || '{}');
    
    if (!admin_key) {
      return { statusCode: 400, headers, body: JSON.stringify({ error: 'Admin key is required' }) };
    }

    // Validate the admin key using the database function
    const { data: isValid, error: rpcError } = await supabase.rpc('validate_admin_key', {
      p_key_hash: admin_key
    });

    if (rpcError) throw rpcError;

    if (!isValid) {
      await logAdminEvent('login_failed', admin_key, ipAddress, userAgent, { reason: 'Invalid or expired key' });
      return { statusCode: 401, headers, body: JSON.stringify({ error: 'Invalid or expired admin key' }) };
    }

    // Since the key is valid, we can assume an admin context.
    // For simplicity, we'll create a generic session token.
    // In a real app, you might fetch a user profile from auth.users here.
    const adminUserId = 'authenticated_admin'; // Placeholder

    await logAdminEvent('login_success', admin_key, ipAddress, userAgent, { userId: adminUserId });

    // Generate a JWT session token
    const sessionToken = jwt.sign(
      {
        sub: adminUserId,
        role: 'admin',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (60 * 60 * 8) // 8-hour session
      },
      process.env.JWT_SECRET,
      { algorithm: 'HS256' }
    );

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Admin login successful',
        token: sessionToken
      })
    };

  } catch (error) {
    console.error('Admin authentication error:', error);
    await logAdminEvent('login_error', null, ipAddress, userAgent, { error: error.message });
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    };
  }
};
