const { authenticateAdmin } = require('../utils/auth');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, x-admin-key',
    'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Authenticate the admin request
    const authResult = await authenticateAdmin(event);
    if (authResult.statusCode) return { ...authResult, headers };
    
    const { adminUser } = authResult;

    // Handle different HTTP methods
    switch (event.httpMethod) {
      case 'GET':
        return handleGetApiKeys(event, adminUser, headers);
      case 'POST':
        return handleCreateApiKey(event, adminUser, headers);
      case 'DELETE':
        return handleRevokeApiKey(event, adminUser, headers);
      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('API Key management error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    };
  }
};

// Get all API keys for the admin
async function handleGetApiKeys(event, adminUser, headers) {
  const { data: apiKeys, error } = await supabase
    .from('api_keys')
    .select('id, user_id, service_type, is_active, uses, max_uses, expires_at, created_at, last_used_at, revoked_at, metadata')
    .order('created_at', { ascending: false });

  if (error) throw error;

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(apiKeys)
  };
}

// Create a new API key using the database function
async function handleCreateApiKey(event, adminUser, headers) {
  const {
    service_type,
    max_uses = 1000,
    hwid = null,
    metadata = {}
  } = JSON.parse(event.body || '{}');

  // Validate input
  if (!service_type || !['service_a', 'service_b'].includes(service_type)) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ 
        error: "Invalid 'service_type'. Must be one of: service_a, service_b" 
      })
    };
  }

  // Call the database function to create the key
  const { data: newKey, error } = await supabase.rpc('create_api_key', {
    p_user_id: adminUser.id,
    p_service_type: service_type,
    p_max_uses: max_uses,
    p_hwid: hwid,
    p_metadata: { ...metadata, created_by: adminUser.email }
  });

  if (error) {
    console.error('Error creating API key:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to create API key', details: error.message })
    };
  }

  // The RPC function returns the raw key. This is the only time it's exposed.
  return {
    statusCode: 201,
    headers,
    body: JSON.stringify({
      message: 'API Key created successfully. Store it securely.',
      apiKey: newKey
    })
  };
}

// Revoke an API key
async function handleRevokeApiKey(event, adminUser, headers) {
  const { id } = event.queryStringParameters || {};
  
  if (!id) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'API key ID is required' })
    };
  }

  // Fetch the key to ensure it exists before logging
  const { data: apiKey, error: fetchError } = await supabase
    .from('api_keys')
    .select('id, service_type')
    .eq('id', id)
    .single();

  if (fetchError || !apiKey) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'API key not found' })
    };
  }

  // Update the key to be revoked
  const { error: updateError } = await supabase
    .from('api_keys')
    .update({ 
      is_active: false,
      revoked_at: new Date().toISOString()
    })
    .eq('id', id);

  if (updateError) {
    console.error('Error revoking API key:', updateError);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to revoke API key', details: updateError.message })
    };
  }

  // Log the revocation event
  await supabase.from('security_events').insert({
    event_type: 'key_revoked',
    user_id: adminUser.id,
    key_id: id,
    ip_address: event.headers['x-forwarded-for'] || event.headers['client-ip'],
    user_agent: event.headers['user-agent'],
    metadata: {
      reason: 'Revoked by admin',
      admin_email: adminUser.email
    }
  });

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ 
      success: true,
      message: 'API key has been revoked and can no longer be used.'
    })
  };
}
