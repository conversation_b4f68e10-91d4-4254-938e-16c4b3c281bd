const { createA<PERSON><PERSON><PERSON><PERSON>, supabase } = require('../utils/api-handler');

// Get site settings
const getSiteSettings = async () => {
  const { data, error } = await supabase.from('site_settings').select('*').single();
  if (error) {
    console.error('Error fetching site settings:', error);
    return { statusCode: 500, body: { error: 'Failed to fetch site settings' } };
  }
  return { statusCode: 200, body: data };
};

// Update site settings
const updateSiteSettings = async ({ body }) => {
  const { data, error } = await supabase
    .from('site_settings')
    .update(body)
    .eq('id', 1) // Assuming a single row for settings
    .select()
    .single();

  if (error) {
    console.error('Error updating site settings:', error);
    return { statusCode: 500, body: { error: 'Failed to update site settings' } };
  }

  // Log the update event
  await supabase.rpc('log_activity', {
    event_type: 'site_settings_updated',
    details: { updated_fields: Object.keys(body) }
  });

  return { statusCode: 200, body: data };
};

// Main handler
exports.handler = createApiHandler({
  requireAuth: true,
  async handler(ctx) {
    switch (ctx.event.httpMethod) {
      case 'GET':
        return getSiteSettings(ctx);
      case 'PUT':
        // Add permission check for updating settings
        if (!ctx.auth.permissions.includes('manage:settings')) {
          return { statusCode: 403, body: { error: 'Permission denied' } };
        }
        return updateSiteSettings(ctx);
      default:
        return { statusCode: 405, body: { error: 'Method Not Allowed' } };
    }
  }
});
