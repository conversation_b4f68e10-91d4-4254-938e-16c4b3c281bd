const { createApi<PERSON><PERSON><PERSON>, supabase } = require('../utils/api-handler');

// Get all scripts
const getScripts = async () => {
  const { data, error } = await supabase.from('scripts').select('*');
  if (error) {
    return { statusCode: 500, body: { error: 'Failed to fetch scripts' } };
  }
  return { statusCode: 200, body: data };
};

// Create a new script
const createScript = async ({ body }) => {
  const { data, error } = await supabase.from('scripts').insert(body).select().single();
  if (error) {
    return { statusCode: 500, body: { error: 'Failed to create script' } };
  }
  return { statusCode: 201, body: data };
};

// Update a script
const updateScript = async ({ params, body }) => {
  const { id } = params;
  const { data, error } = await supabase.from('scripts').update(body).eq('id', id).select().single();
  if (error) {
    return { statusCode: 500, body: { error: 'Failed to update script' } };
  }
  return { statusCode: 200, body: data };
};

// Delete a script
const deleteScript = async ({ params }) => {
  const { id } = params;
  const { error } = await supabase.from('scripts').delete().eq('id', id);
  if (error) {
    return { statusCode: 500, body: { error: 'Failed to delete script' } };
  }
  return { statusCode: 204, body: null };
};

// Main handler
exports.handler = createApiHandler({
  requireAuth: true,
  async handler(ctx) {
    const { httpMethod, pathParameters } = ctx.event;
    const id = pathParameters ? pathParameters.id : null;

    if (id) {
      switch (httpMethod) {
        case 'PUT':
          return updateScript({ params: { id }, ...ctx });
        case 'DELETE':
          return deleteScript({ params: { id }, ...ctx });
        default:
          return { statusCode: 405, body: { error: 'Method Not Allowed' } };
      }
    } else {
      switch (httpMethod) {
        case 'GET':
          return getScripts(ctx);
        case 'POST':
          return createScript(ctx);
        default:
          return { statusCode: 405, body: { error: 'Method Not Allowed' } };
      }
    }
  }
});
