const { createApiHandler, supabase } = require('../utils/api-handler');

// Get activity logs
const getActivityLogs = async ({ query }) => {
  const { page = 1, limit = 20, event_type } = query;
  const from = (page - 1) * limit;
  const to = from + limit - 1;

  let queryBuilder = supabase.from('activity_log').select('*', { count: 'exact' }).order('timestamp', { ascending: false }).range(from, to);

  if (event_type) {
    queryBuilder = queryBuilder.eq('event_type', event_type);
  }

  const { data, error, count } = await queryBuilder;

  if (error) {
    return { statusCode: 500, body: { error: 'Failed to fetch activity logs' } };
  }

  return { statusCode: 200, body: { data, count } };
};

// Main handler
exports.handler = createApiHandler({
  requireAuth: true,
  handler: getActivityLogs
});
