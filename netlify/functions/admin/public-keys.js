const { create<PERSON><PERSON><PERSON><PERSON><PERSON>, supabase } = require('../utils/api-handler');
const { generateApiKey, hashApiKey } = require('../utils/auth');

// Generate a new public key (24hr expiry, no HWID stored)
const generatePublicKey = async ({ body, auth }) => {
  const { userId, serviceType, metadata } = body;

  if (!userId || !serviceType) {
    return { statusCode: 400, body: { error: 'userId and serviceType are required' } };
  }

  const { key, hash } = generateApiKey();
  const now = new Date();
  const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString();

  const { data, error } = await supabase
    .from('public_keys')
    .insert({
      user_id: userId,
      key_hash: hash,
      service_type: serviceType,
      metadata,
      created_by: auth.admin.id,
      created_at: now.toISOString(),
      expires_at: expiresAt,
      is_active: true
    })
    .select()
    .single();

  if (error) {
    console.error('Error generating public key:', error);
    return { statusCode: 500, body: { error: 'Failed to generate key' } };
  }

  return { statusCode: 201, body: { ...data, key, expires_at: expiresAt } };
};

// Validate a public key (no HWID, check expiry)
const validatePublicKey = async ({ body }) => {
  const { key } = body;

  if (!key) {
    return { statusCode: 400, body: { error: 'Key is required' } };
  }

  const key_hash = hashApiKey(key);
  const { data, error } = await supabase
    .from('public_keys')
    .select('*')
    .eq('key_hash', key_hash)
    .eq('is_active', true)
    .single();

  if (error || !data) {
    return { statusCode: 200, body: { isValid: false, reason: 'Key not found or inactive' } };
  }

  const now = new Date();
  if (new Date(data.expires_at) < now) {
    // Optionally auto-deactivate
    await supabase.from('public_keys').update({ is_active: false }).eq('id', data.id);
    return { statusCode: 200, body: { isValid: false, reason: 'Key expired' } };
  }

  return { statusCode: 200, body: { isValid: true } };
};

// Fetch a key by ID (for UI, only if not expired, only for creator)
const getPublicKeyById = async ({ params, auth }) => {
  const { id } = params;
  if (!id) {
    return { statusCode: 400, body: { error: 'Key ID is required' } };
  }
  const { data, error } = await supabase
    .from('public_keys')
    .select('*')
    .eq('id', id)
    .single();
  if (error || !data) {
    return { statusCode: 404, body: { error: 'Key not found' } };
  }
  // Only allow creator to fetch
  if (data.created_by !== auth.admin.id) {
    return { statusCode: 403, body: { error: 'Permission denied' } };
  }
  // Check expiry
  const now = new Date();
  if (new Date(data.expires_at) < now) {
    return { statusCode: 410, body: { error: 'Key expired' } };
  }
  return { statusCode: 200, body: { key: data.key, expires_at: data.expires_at } };
};

// Main handler
exports.handler = createApiHandler({
  async handler(ctx) {
    const { path, httpMethod, pathParameters } = ctx.event;

    if (path.endsWith('/generate')) {
      return createApiHandler({ requireAuth: true, handler: generatePublicKey })(ctx.event, ctx.context);
    }

    if (path.endsWith('/validate')) {
      return createApiHandler({ requireAuth: false, handler: validatePublicKey })(ctx.event, ctx.context);
    }

    // GET /admin/public-keys/:id
    if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
      return createApiHandler({ requireAuth: true, handler: getPublicKeyById })(ctx.event, ctx.context);
    }

    return { statusCode: 404, body: { error: 'Not Found' } };
  }
});
