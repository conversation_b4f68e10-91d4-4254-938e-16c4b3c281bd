{"name": "netlify-functions", "version": "1.0.0", "description": "Serverless functions for the admin dashboard", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "crypto": "^1.0.1", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "nanoid": "^4.0.2", "node-fetch": "^2.6.12"}, "engines": {"node": ">=18.0.0"}, "keywords": ["netlify", "serverless", "supabase", "api"], "author": "", "license": "MIT"}