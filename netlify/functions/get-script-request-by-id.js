const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Helper to convert object keys to camelCase
const toCamelCase = (s) => s.replace(/([-_][a-z])/ig, ($1) => {
  return $1.toUpperCase()
    .replace('-', '')
    .replace('_', '');
});

const keysToCamelCase = (o) => {
  if (Array.isArray(o)) {
    return o.map(v => keysToCamelCase(v));
  } else if (o !== null && o.constructor === Object) {
    return Object.keys(o).reduce((acc, key) => {
      acc[toCamelCase(key)] = keysToCamelCase(o[key]);
      return acc;
    }, {});
  }
  return o;
};

exports.handler = async (event) => {
  if (event.httpMethod !== 'GET') {
    return { statusCode: 405, body: 'Method Not Allowed' };
  }

  try {
    const { id } = event.queryStringParameters;
    if (!id) {
      return { statusCode: 400, body: JSON.stringify({ error: 'Request ID is required' }) };
    }

    const { data, error } = await supabase
      .from('script_requests')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // PostgREST error for 'not found'
        return { statusCode: 404, body: JSON.stringify({ error: 'Request not found' }) };
      }
      console.error('Error fetching script request:', error);
      throw new Error('Failed to fetch request from the database.');
    }

    if (!data) {
        return { statusCode: 404, body: JSON.stringify({ error: 'Request not found' }) };
    }

    return {
      statusCode: 200,
      body: JSON.stringify(keysToCamelCase(data)),
    };

  } catch (error) {
    console.error('Get script request error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message || 'An internal server error occurred.' }),
    };
  }
};
