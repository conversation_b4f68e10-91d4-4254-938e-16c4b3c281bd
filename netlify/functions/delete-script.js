const { createApiHandler, supabase } = require('./utils/api-handler');

module.exports.handler = createApiHandler({
  requireAuth: true,
  async handler(event) {
    try {
      const { id } = event.pathParameters;
      
      // Delete the script from the database
      const { error } = await supabase
        .from('scripts')
        .delete()
        .eq('id', id);

      if (error) throw error;
      
      return {
        statusCode: 204,
        body: JSON.stringify({ message: '<PERSON>rip<PERSON> deleted successfully' })
      };
    } catch (error) {
      console.error('Error deleting script:', error);
      return {
        statusCode: error.statusCode || 500,
        body: JSON.stringify({ error: error.message || 'Internal server error' })
      };
    }
  }
});
