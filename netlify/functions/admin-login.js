const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

exports.handler = async (event) => {
  if (event.httpMethod !== 'POST') {
    return { statusCode: 405, body: 'Method Not Allowed' };
  }

  try {
    const { username, password } = JSON.parse(event.body);
    if (!username || !password) {
      return { statusCode: 400, body: JSON.stringify({ error: 'Username and password are required' }) };
    }

    // 1. Validate the admin credentials
    const { data: validationResult, error: validationError } = await supabase.rpc('validate_admin_password', {
      p_username: username,
      p_password: password
    }).single();

    if (validationError) {
      console.error('Error validating admin password:', validationError);
      throw new Error('Error during password validation.');
    }

    if (!validationResult || !validationResult.is_valid) {
      return { statusCode: 401, body: JSON.stringify({ error: 'Invalid username or password' }) };
    }

    // 2. On success, return a session token (or simple success message)
    // This token can be used for subsequent authenticated requests.
    const sessionToken = `admin_session_${Buffer.from(JSON.stringify({ id: validationResult.admin_id, user: username })).toString('base64')}`;

    return {
      statusCode: 200,
      body: JSON.stringify({ 
        message: 'Login successful',
        token: sessionToken,
        admin: {
          id: validationResult.admin_id,
          username: username
        }
      }),
    };

  } catch (error) {
    console.error('Admin login error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message || 'An internal server error occurred.' }),
    };
  }
};
