const { createApiHandler, supabase } = require('./utils/api-handler');

module.exports.handler = createApiHandler({
  requireAuth: true,
  async handler(event) {
    try {
      const { id } = event.pathParameters;
      const updates = JSON.parse(event.body);
      
      // Update the script in the database
      const { data, error } = await supabase
        .from('scripts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      
      return {
        statusCode: 200,
        body: JSON.stringify(data)
      };
    } catch (error) {
      console.error('Error updating script:', error);
      return {
        statusCode: error.statusCode || 500,
        body: JSON.stringify({ error: error.message || 'Internal server error' })
      };
    }
  }
});
