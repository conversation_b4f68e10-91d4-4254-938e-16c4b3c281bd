const { createApiHandler, supabase } = require('./utils/api-handler');

// Get all active scripts for the public
const getScripts = async () => {
  try {
    console.log('Fetching scripts from database...');
    const { data, error, status } = await supabase
      .from('scripts')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', { error, status });
      return { 
        statusCode: status || 500, 
        body: { 
          error: 'Failed to fetch scripts',
          details: error.message 
        } 
      };
    }
    
    console.log(`Successfully fetched ${data.length} scripts`);
    return { 
      statusCode: 200, 
      body: data 
    };
  } catch (error) {
    console.error('Unexpected error in getScripts:', error);
    return {
      statusCode: 500,
      body: { 
        error: 'An unexpected error occurred',
        details: error.message 
      }
    };
  }
};

// Main handler
exports.handler = createApiHandler({
  requireAuth: false, // Public endpoint
  async handler(ctx) {
    const { httpMethod } = ctx.event;

    if (httpMethod === 'GET') {
      return getScripts(ctx);
    }
    
    return { statusCode: 405, body: { error: 'Method Not Allowed' } };
  }
});
