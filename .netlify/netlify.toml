functionsDirectory = "/Users/<USER>/Documents/Website_React/netlify/functions"
functionsDirectoryOrigin = "config-v1"
headersOrigin = "config"
redirectsOrigin = "config"
plugins = []

[build]
publish = "/Users/<USER>/Documents/Website_React/dist"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"
functions = "/Users/<USER>/Documents/Website_React/netlify/functions"

[build.environment]
JWT_EXPIRES_IN = "7d"
RATE_LIMIT_WINDOW_MS = "900000"
RATE_LIMIT_MAX_REQUESTS = "100"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[functions]

[functions."*"]
included_files = [".env.*"]
external_node_modules = ["@supabase/supabase-js", "joi", "jsonwebtoken"]
node_bundler = "esbuild"

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
X-XSS-Protection = "1; mode=block"
Referrer-Policy = "strict-origin-when-cross-origin"
Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://*.supabase.co; frame-src 'self' https://*.supabase.co;"

[[headers]]
for = "/api/*"

[headers.values]
X-RateLimit-Limit = "100"
X-RateLimit-Remaining = "99"
X-RateLimit-Reset = "900"

[[redirects]]
from = "/api/*"
to = "/.netlify/functions/:splat"
status = 200.0
force = true

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]