{"functions": [{"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/admin-login.js", "name": "admin-login", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/admin-login.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/create-script.js", "name": "create-script", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/create-script.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/delete-script.js", "name": "delete-script", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/delete-script.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/get-script-request-by-id.js", "name": "get-script-request-by-id", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/get-script-request-by-id.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/get-script-requests.js", "name": "get-script-requests", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/get-script-requests.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/get-scripts.js", "name": "get-scripts", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/get-scripts.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/submit-request.js", "name": "submit-request", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/submit-request.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/update-request-status.js", "name": "update-request-status", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/update-request-status.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/update-script-status.js", "name": "update-script-status", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/update-script-status.zip", "runtime": "js"}, {"bundler": "esbuild", "buildData": {"runtimeAPIVersion": 1}, "mainFile": "/Users/<USER>/Documents/Website_React/netlify/functions/update-script.js", "name": "update-script", "priority": 10, "path": "/Users/<USER>/Documents/Website_React/.netlify/functions/update-script.zip", "runtime": "js"}], "system": {"arch": "arm64", "platform": "darwin"}, "timestamp": 1753546919081, "version": 1}