{"version": 3, "sources": ["../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/admin-login.js"], "sourceRoot": "/var/folders/hk/qs6yfsln2cx1tnsx6mfl43w00000gn/T/tmp-18157-TFGAoj5264bb", "sourcesContent": ["const { createClient } = require('@supabase/supabase-js');\n\n// Initialize Supabase client\nconst supabase = createClient(\n  process.env.SUPABASE_URL,\n  process.env.SUPABASE_SERVICE_ROLE_KEY\n);\n\nexports.handler = async (event) => {\n  if (event.httpMethod !== 'POST') {\n    return { statusCode: 405, body: 'Method Not Allowed' };\n  }\n\n  try {\n    const { username, password } = JSON.parse(event.body);\n    if (!username || !password) {\n      return { statusCode: 400, body: JSON.stringify({ error: 'Username and password are required' }) };\n    }\n\n    // 1. Validate the admin credentials\n    const { data: validationResult, error: validationError } = await supabase.rpc('validate_admin_password', {\n      p_username: username,\n      p_password: password\n    }).single();\n\n    if (validationError) {\n      console.error('Error validating admin password:', validationError);\n      throw new Error('Error during password validation.');\n    }\n\n    if (!validationResult || !validationResult.is_valid) {\n      return { statusCode: 401, body: JSON.stringify({ error: 'Invalid username or password' }) };\n    }\n\n    // 2. On success, return a session token (or simple success message)\n    // This token can be used for subsequent authenticated requests.\n    const sessionToken = `admin_session_${Buffer.from(JSON.stringify({ id: validationResult.admin_id, user: username })).toString('base64')}`;\n\n    return {\n      statusCode: 200,\n      body: JSON.stringify({ \n        message: 'Login successful',\n        token: sessionToken,\n        admin: {\n          id: validationResult.admin_id,\n          username: username\n        }\n      }),\n    };\n\n  } catch (error) {\n    console.error('Admin login error:', error);\n    return {\n      statusCode: 500,\n      body: JSON.stringify({ error: error.message || 'An internal server error occurred.' }),\n    };\n  }\n};\n"], "mappings": ";AAAA,IAAM,EAAE,aAAa,IAAI,QAAQ,uBAAuB;AAGxD,IAAM,WAAW;AAAA,EACf,QAAQ,IAAI;AAAA,EACZ,QAAQ,IAAI;AACd;AAEA,QAAQ,UAAU,OAAO,UAAU;AACjC,MAAI,MAAM,eAAe,QAAQ;AAC/B,WAAO,EAAE,YAAY,KAAK,MAAM,qBAAqB;AAAA,EACvD;AAEA,MAAI;AACF,UAAM,EAAE,UAAU,SAAS,IAAI,KAAK,MAAM,MAAM,IAAI;AACpD,QAAI,CAAC,YAAY,CAAC,UAAU;AAC1B,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,qCAAqC,CAAC,EAAE;AAAA,IAClG;AAGA,UAAM,EAAE,MAAM,kBAAkB,OAAO,gBAAgB,IAAI,MAAM,SAAS,IAAI,2BAA2B;AAAA,MACvG,YAAY;AAAA,MACZ,YAAY;AAAA,IACd,CAAC,EAAE,OAAO;AAEV,QAAI,iBAAiB;AACnB,cAAQ,MAAM,oCAAoC,eAAe;AACjE,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACrD;AAEA,QAAI,CAAC,oBAAoB,CAAC,iBAAiB,UAAU;AACnD,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,+BAA+B,CAAC,EAAE;AAAA,IAC5F;AAIA,UAAM,eAAe,iBAAiB,OAAO,KAAK,KAAK,UAAU,EAAE,IAAI,iBAAiB,UAAU,MAAM,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,CAAC;AAEvI,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU;AAAA,QACnB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,OAAO;AAAA,UACL,IAAI,iBAAiB;AAAA,UACrB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,sBAAsB,KAAK;AACzC,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU,EAAE,OAAO,MAAM,WAAW,qCAAqC,CAAC;AAAA,IACvF;AAAA,EACF;AACF;", "names": []}