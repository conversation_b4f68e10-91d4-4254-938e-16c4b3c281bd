var __getOwnPropNames = Object.getOwnPropertyNames;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};

// netlify/functions/utils/auth.js
var require_auth = __commonJS({
  "netlify/functions/utils/auth.js"(exports2, module2) {
    var { createClient } = require("@supabase/supabase-js");
    var crypto = require("crypto");
    var supabase2 = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    var RATE_LIMIT = {
      WINDOW_MS: 60 * 1e3,
      // 1 minute
      MAX_REQUESTS: 100
    };
    var rateLimitCache = /* @__PURE__ */ new Map();
    var logAuthEvent = async (event) => {
      const { data, error } = await supabase2.from("auth_events").insert([event]);
      if (error) {
        console.error("Error logging auth event:", error);
      }
    };
    var authenticateApiKey = async (req, res) => {
      try {
        const apiKey = req.headers["x-api-key"] || req.query.api_key;
        if (!apiKey) {
          return {
            statusCode: 401,
            body: JSON.stringify({ error: "API key is required" })
          };
        }
        if (typeof apiKey !== "string" || !/^[a-f0-9]{64}$/i.test(apiKey)) {
          return {
            statusCode: 403,
            body: JSON.stringify({ error: "Invalid API key format" })
          };
        }
        const { data: keyData, error: keyError } = await supabase2.from("api_keys").select("*, admin_profile(*)").eq("key_hash", hashApiKey(apiKey)).eq("is_active", true).single();
        if (keyError || !keyData) {
          console.error("Invalid API key attempt:", { error: keyError });
          return {
            statusCode: 403,
            body: JSON.stringify({ error: "Invalid or inactive API key" })
          };
        }
        if (new Date(keyData.expires_at) < /* @__PURE__ */ new Date()) {
          await supabase2.from("api_keys").update({ is_active: false }).eq("id", keyData.id);
          return {
            statusCode: 403,
            body: JSON.stringify({ error: "API key has expired" })
          };
        }
        const clientHwid = req.headers["x-client-hwid"] || "unknown";
        const userAgent = req.headers["user-agent"] || "unknown";
        const now = Date.now();
        const windowStart = now - RATE_LIMIT.WINDOW_MS;
        for (const [hwid, timestamps] of rateLimitCache.entries()) {
          const validTimestamps = timestamps.filter((ts) => ts > windowStart);
          if (validTimestamps.length > 0) {
            rateLimitCache.set(hwid, validTimestamps);
          } else {
            rateLimitCache.delete(hwid);
          }
        }
        const requestTimestamps = rateLimitCache.get(clientHwid) || [];
        if (requestTimestamps.length >= RATE_LIMIT.MAX_REQUESTS) {
          await logAuthEvent({
            event_type: "rate_limit_exceeded",
            user_id: keyData.admin_id,
            metadata: {
              key_id: keyData.id,
              key_name: keyData.name,
              request_count: requestTimestamps.length,
              path: req.path,
              method: req.httpMethod
            },
            hwid: clientHwid
          });
          return {
            statusCode: 429,
            body: JSON.stringify({
              error: "Too many requests",
              retry_after: Math.ceil((requestTimestamps[0] - windowStart) / 1e3)
            })
          };
        }
        rateLimitCache.set(clientHwid, [...requestTimestamps, now]);
        await logAuthEvent({
          event_type: "api_access",
          user_id: keyData.admin_id,
          metadata: {
            key_id: keyData.id,
            key_name: keyData.name,
            path: req.path,
            method: req.httpMethod,
            params: req.queryStringParameters
          },
          hwid: clientHwid
        });
        await supabase2.from("api_keys").update({
          last_used_at: (/* @__PURE__ */ new Date()).toISOString(),
          last_used_hwid: clientHwid,
          last_used_user_agent: userAgent,
          use_count: (keyData.use_count || 0) + 1
        }).eq("id", keyData.id);
        return {
          admin: keyData.admin_profile,
          apiKey: {
            id: keyData.id,
            name: keyData.name,
            scopes: keyData.scopes || [],
            permissions: keyData.permissions || []
          },
          clientInfo: {
            ip: clientIp,
            userAgent,
            hwid: req.headers["x-client-hwid"]
          }
        };
      } catch (error) {
        console.error("Authentication error:", error);
        return {
          statusCode: 500,
          body: JSON.stringify({ error: "Internal server error during authentication" })
        };
      }
    };
    function hashApiKey(key) {
      if (!key) return null;
      const hash = crypto.createHash("sha256").update(key + process.env.API_KEY_SALT).digest("hex");
      return hash;
    }
    function generateApiKey() {
      const key = `sk_${crypto.randomBytes(32).toString("hex")}`;
      const hash = hashApiKey(key);
      const fingerprint = crypto.createHash("sha256").update(key).digest("hex").slice(0, 16);
      return {
        key,
        hash,
        fingerprint: `sk_${fingerprint}`
      };
    }
    var hasPermission = (context, requiredPermission) => {
      if (!context || !context.apiKey) return false;
      if (!requiredPermission) return true;
      return context.apiKey.permissions.includes(requiredPermission) || context.apiKey.permissions.includes("*");
    };
    setInterval(() => {
      const now = Date.now();
      const windowStart = now - RATE_LIMIT.WINDOW_MS * 2;
      for (const [hwid, timestamps] of rateLimitCache.entries()) {
        const validTimestamps = timestamps.filter((ts) => ts > windowStart);
        if (validTimestamps.length > 0) {
          rateLimitCache.set(hwid, validTimestamps);
        } else {
          rateLimitCache.delete(hwid);
        }
      }
    }, RATE_LIMIT.WINDOW_MS * 5);
    module2.exports = {
      authenticateApiKey,
      generateApiKey,
      hashApiKey,
      hasPermission,
      logAuthEvent
    };
  }
});

// netlify/functions/utils/api-handler.js
var require_api_handler = __commonJS({
  "netlify/functions/utils/api-handler.js"(exports2, module2) {
    var { createClient } = require("@supabase/supabase-js");
    var { authenticateApiKey, hasPermission } = require_auth();
    var supabase2 = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    function createApiHandler2({
      requiredPermissions = [],
      handler,
      requireAuth = true
    }) {
      return async (event, context) => {
        const headers = {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type, Authorization, x-api-key",
          "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
          "Content-Type": "application/json"
        };
        if (event.httpMethod === "OPTIONS") {
          return {
            statusCode: 200,
            headers,
            body: ""
          };
        }
        try {
          const clientIp2 = event.headers["x-forwarded-for"] || event.connection.remoteAddress;
          const userAgent = event.headers["user-agent"] || "";
          const hwid = event.headers["x-client-hwid"] || "";
          let body = {};
          if (event.body) {
            try {
              body = typeof event.body === "string" ? JSON.parse(event.body) : event.body;
            } catch (error) {
              console.error("Error parsing request body:", error);
              return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: "Invalid JSON in request body" })
              };
            }
          }
          let auth = {};
          if (requireAuth) {
            const authResult = await authenticateApiKey(event, context);
            if (authResult.statusCode) {
              return { ...authResult, headers };
            }
            auth = authResult;
            if (requiredPermissions.length > 0) {
              const hasAllPermissions = requiredPermissions.every(
                (permission) => hasPermission(auth, permission)
              );
              if (!hasAllPermissions) {
                console.warn("Permission denied:", {
                  requiredPermissions,
                  hasPermissions: auth.apiKey?.permissions || []
                });
                return {
                  statusCode: 403,
                  headers,
                  body: JSON.stringify({
                    error: "Insufficient permissions",
                    required: requiredPermissions
                  })
                };
              }
            }
          }
          const query = event.queryStringParameters || {};
          const result = await handler({
            event,
            context,
            body,
            query,
            params: event.pathParameters || {},
            headers: event.headers,
            auth,
            supabase: supabase2,
            clientInfo: {
              ip: clientIp2,
              userAgent,
              hwid
            }
          });
          const response = {
            statusCode: result.statusCode || 200,
            headers: { ...headers, ...result.headers || {} },
            body: typeof result.body === "string" ? result.body : JSON.stringify(result.body || {})
          };
          return response;
        } catch (error) {
          console.error("API handler error:", error);
          const errorMessage = process.env.NODE_ENV === "development" ? error.message : "An unexpected error occurred";
          return {
            statusCode: error.statusCode || 500,
            headers,
            body: JSON.stringify({
              error: errorMessage,
              ...process.env.NODE_ENV === "development" ? { stack: error.stack } : {}
            })
          };
        }
      };
    }
    function validateBody(schema) {
      return (body) => {
        const { error, value } = schema.validate(body, { abortEarly: false });
        if (error) {
          const validationError = new Error("Validation error");
          validationError.statusCode = 400;
          validationError.details = error.details.map((d) => ({
            message: d.message,
            path: d.path,
            type: d.type
          }));
          throw validationError;
        }
        return value;
      };
    }
    module2.exports = {
      createApiHandler: createApiHandler2,
      validateBody,
      supabase: supabase2
    };
  }
});

// netlify/functions/update-script.js
var { createApiHandler, supabase } = require_api_handler();
module.exports.handler = createApiHandler({
  requireAuth: true,
  async handler(event) {
    try {
      const { id } = event.pathParameters;
      const updates = JSON.parse(event.body);
      const { data, error } = await supabase.from("scripts").update(updates).eq("id", id).select().single();
      if (error) throw error;
      return {
        statusCode: 200,
        body: JSON.stringify(data)
      };
    } catch (error) {
      console.error("Error updating script:", error);
      return {
        statusCode: error.statusCode || 500,
        body: JSON.stringify({ error: error.message || "Internal server error" })
      };
    }
  }
});
//# sourceMappingURL=update-script.js.map
