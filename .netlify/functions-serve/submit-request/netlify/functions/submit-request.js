// netlify/functions/submit-request.js
var { createClient } = require("@supabase/supabase-js");
var supabaseUrl = process.env.SUPABASE_URL;
var supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
var supabase = createClient(supabaseUrl, supabaseKey);
exports.handler = async (event) => {
  if (event.httpMethod !== "POST") {
    return { statusCode: 405, body: "Method Not Allowed" };
  }
  try {
    const { script_name, requested_by, service, notes } = JSON.parse(event.body);
    if (!script_name || !requested_by || !service) {
      return { statusCode: 400, body: JSON.stringify({ error: "Missing required fields." }) };
    }
    const { data, error } = await supabase.from("script_requests").insert([
      {
        script_name,
        requested_by,
        // Should be a user identifier, e.g., email or a generated ID
        service,
        notes,
        status: "pending"
        // Default status
      }
    ]).select().single();
    if (error) {
      console.error("Error inserting script request:", error);
      throw new Error("Failed to submit request to the database.");
    }
    const { error: logError } = await supabase.from("script_request_logs").insert({
      request_id: data.id,
      action: "created",
      details: {
        script_name: data.script_name,
        requested_by: data.requested_by
      }
    });
    if (logError) {
      console.error("Error logging script request creation:", logError);
    }
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Request submitted successfully!",
        requestId: data.id
      })
    };
  } catch (error) {
    console.error("Submit request error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message || "An internal server error occurred." })
    };
  }
};
//# sourceMappingURL=submit-request.js.map
