{"version": 3, "sources": ["../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/submit-request.js"], "sourceRoot": "/var/folders/hk/qs6yfsln2cx1tnsx6mfl43w00000gn/T/tmp-18157-7T3gZApXi7Ot", "sourcesContent": ["const { createClient } = require('@supabase/supabase-js');\n\nconst supabaseUrl = process.env.SUPABASE_URL;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = createClient(supabaseUrl, supabaseKey);\n\nexports.handler = async (event) => {\n  if (event.httpMethod !== 'POST') {\n    return { statusCode: 405, body: 'Method Not Allowed' };\n  }\n\n  try {\n    const { script_name, requested_by, service, notes } = JSON.parse(event.body);\n\n    if (!script_name || !requested_by || !service) {\n      return { statusCode: 400, body: JSON.stringify({ error: 'Missing required fields.' }) };\n    }\n\n    const { data, error } = await supabase\n      .from('script_requests')\n      .insert([\n        {\n          script_name,\n          requested_by, // Should be a user identifier, e.g., email or a generated ID\n          service,\n          notes,\n          status: 'pending' // Default status\n        }\n      ])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error inserting script request:', error);\n      throw new Error('Failed to submit request to the database.');\n    }\n\n    // Log the creation event\n    const { error: logError } = await supabase\n      .from('script_request_logs')\n      .insert({\n        request_id: data.id,\n        action: 'created',\n        details: {\n          script_name: data.script_name,\n          requested_by: data.requested_by\n        }\n      });\n\n    if (logError) {\n      console.error('Error logging script request creation:', logError);\n      // Don't fail the whole request, but log the error\n    }\n\n    return {\n      statusCode: 200,\n      body: JSON.stringify({ \n        message: 'Request submitted successfully!',\n        requestId: data.id\n      }),\n    };\n\n  } catch (error) {\n    console.error('Submit request error:', error);\n    return {\n      statusCode: 500,\n      body: JSON.stringify({ error: error.message || 'An internal server error occurred.' }),\n    };\n  }\n};\n"], "mappings": ";AAAA,IAAM,EAAE,aAAa,IAAI,QAAQ,uBAAuB;AAExD,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,WAAW,aAAa,aAAa,WAAW;AAEtD,QAAQ,UAAU,OAAO,UAAU;AACjC,MAAI,MAAM,eAAe,QAAQ;AAC/B,WAAO,EAAE,YAAY,KAAK,MAAM,qBAAqB;AAAA,EACvD;AAEA,MAAI;AACF,UAAM,EAAE,aAAa,cAAc,SAAS,MAAM,IAAI,KAAK,MAAM,MAAM,IAAI;AAE3E,QAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,SAAS;AAC7C,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,2BAA2B,CAAC,EAAE;AAAA,IACxF;AAEA,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,SAC3B,KAAK,iBAAiB,EACtB,OAAO;AAAA,MACN;AAAA,QACE;AAAA,QACA;AAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA,QAAQ;AAAA;AAAA,MACV;AAAA,IACF,CAAC,EACA,OAAO,EACP,OAAO;AAEV,QAAI,OAAO;AACT,cAAQ,MAAM,mCAAmC,KAAK;AACtD,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AAGA,UAAM,EAAE,OAAO,SAAS,IAAI,MAAM,SAC/B,KAAK,qBAAqB,EAC1B,OAAO;AAAA,MACN,YAAY,KAAK;AAAA,MACjB,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,aAAa,KAAK;AAAA,QAClB,cAAc,KAAK;AAAA,MACrB;AAAA,IACF,CAAC;AAEH,QAAI,UAAU;AACZ,cAAQ,MAAM,0CAA0C,QAAQ;AAAA,IAElE;AAEA,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU;AAAA,QACnB,SAAS;AAAA,QACT,WAAW,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU,EAAE,OAAO,MAAM,WAAW,qCAAqC,CAAC;AAAA,IACvF;AAAA,EACF;AACF;", "names": []}