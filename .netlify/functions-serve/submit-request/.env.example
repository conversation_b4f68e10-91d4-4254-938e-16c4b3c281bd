# ===========================================================================
# Required Environment Variables
# ===========================================================================

# Application Configuration
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:3000/api
PORT=3000

# Frontend Configuration
VITE_APP_NAME="Project L"
VITE_DEFAULT_THEME=dark
VITE_RECAPTCHA_SITE_KEY=6LcQkVIrAAAAAABDI908Dq5Jj0SAON3SRqqqP9sj  # Required for CAPTCHA verification

# Supabase Configuration (Required for all environments)
VITE_SUPABASE_URL=https://trallxbxsbrbdnvfnwkj.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRyYWxseGJ4c2JyYmRudmZud2tqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MDIyNTcsImV4cCI6MjA2NDM3ODI1N30.2snjXXdNIxzuUjiAFSyUP4wC7uOkm2tNIRqR3dng0cY
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRyYWxseGJ4c2JyYmRudmZud2tqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODgwMjI1NywiZXhwIjoyMDY0Mzc4MjU3fQ.RC-qTRpaIBZeGO4kLuBYf9s0XQ5qQ1DuKnMYa8VH3tY  # Server-side only

# JWT Configuration (Required for authentication)
JWT_SECRET=6205b3cb633b5ee70ea14f0beeebe06b4ced0c112c270cff40ee6d57a5ba9742 # Generate with: openssl rand -base64 32
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
JWT_COOKIE_NAME=__MadaraSecure  # Add Secure and HttpOnly flags
JWT_COOKIE_DOMAIN=https://checkingbefore.netlify.app/
JWT_COOKIE_PATH=/
JWT_COOKIE_SAME_SITE=Strict
JWT_COOKIE_SECURE=true
JWT_COOKIE_HTTP_ONLY=true

# API Security (Required for production)
API_KEY_SALT=10e5a08f8e1884594701a25e6531df7f76671555f6ce0a324fb266cc75394d73  # Generate with: openssl rand -hex 32
ENCRYPTION_KEY=64a6d5981437d63de8a0c8b50756b54860250ed848a5eec93fac52075ef36f82  # Generate with: openssl rand -hex 32
SESSION_SECRET=64a6d5981437d63de8a0c8b50756b54860250ed848a5eec93fac52075ef36f82  # Generate with: openssl rand -hex 32
CSRF_SECRET=a648e776eebfe5955eabc62384fc6fa24055cf9e3a1fbc4bfa89b6cd988e835f  # Generate with: openssl rand -hex 32

# Rate Limiting (Brute Force Protection)
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # Max requests per window
AUTH_RATE_LIMIT_WINDOW_MS=3600000  # 1 hour for auth endpoints
AUTH_RATE_LIMIT_MAX_REQUESTS=10  # Stricter limits for auth

# Security Headers
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' https://*.supabase.co;"
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff
X_XSS_PROTECTION=1; mode=block
REFERRER_POLICY=strict-origin-when-cross-origin
PERMISSIONS_POLICY=geolocation=(), microphone=(), camera=()

# CORS Configuration (Restrict to your domain)
CORS_ORIGIN=https://checkingbefore.netlify.app/
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With,x-api-key
CORS_CREDENTIALS=true

# Security Headers (Advanced)
EXPECT_CT_MAX_AGE=86400  # 1 day in seconds
STRICT_TRANSPORT_SECURITY=max-age=31536000; includeSubDomains; preload
X_DOWNLOAD_OPTIONS=noopen

# Key System Configuration
KEY_EXPIRY_DAYS=30  # Default key expiry in days
KEY_MAX_USES=1000    # Maximum uses per key (0 for unlimited)
KEY_PREFIX=PL_      # Prefix for generated keys
KEY_LENGTH=32       # Length of generated keys

# Rate Limiting for Key System
KEY_GENERATION_LIMIT=5     # Max key generations per hour per IP
KEY_VALIDATION_LIMIT=60    # Max key validations per hour per IP
HWID_RESET_LIMIT=3         # Max HWID resets per key per 24h

# Security Features
ENABLE_HWID_LOCKING=true    # Enable HWID locking for keys
REQUIRE_CAPTCHA=true        # Require CAPTCHA for key generation
LOG_SECURITY_EVENTS=true    # Log security events to database

# API Security
API_KEY_HEADER=x-api-key    # Header for API key authentication
ADMIN_API_KEYS=admin_key_1,admin_key_2  # Comma-separated list of admin API keys
X_PERMITTED_CROSS_DOMAIN_POLICIES=none
X_DNS_PREFETCH_CONTROL=off

# Security Features
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=true
ENABLE_CSP=true
ENABLE_HSTS=true
ENABLE_XSS_PROTECTION=true
ENABLE_CONTENT_TYPE_OPTIONS=true
ENABLE_FRAME_OPTIONS=true
ENABLE_REFERRER_POLICY=true
ENABLE_PERMISSIONS_POLICY=true

# Logging
LOG_LEVEL=info  # error, warn, info, debug, trace
LOG_FORMAT=json  # json, simple
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=30d

# Session Security
SESSION_NAME=__Secure-session  # Add Secure and HttpOnly flags
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=Strict
SESSION_DOMAIN=https://checkingbefore.netlify.app/
SESSION_PATH=/
SESSION_MAX_AGE=86400  # 1 day in seconds

# ===========================================================================
# Optional Environment Variables
# ===========================================================================

# Application Settings
VITE_APP_NAME=ProjectMadara
VITE_APP_ENV=development  # development, staging, production
VITE_APP_VERSION=1.0.0



# CORS Configuration (Required if using custom domains)
ALLOWED_ORIGINS=https://checkingbefore.netlify.app/

# Feature Flags (Enable/disable features)
ENABLE_PUBLIC_API=true
ENABLE_KEY_ROTATION=true


# ===========================================================================
# Database Configuration (Advanced)
# ===========================================================================
# These are automatically set by Supabase but can be overridden if needed
# DATABASE_URL=postgresql://user:password@host:port/dbname
# POOL_SIZE=20
# CONNECTION_TIMEOUT=5000

# ===========================================================================