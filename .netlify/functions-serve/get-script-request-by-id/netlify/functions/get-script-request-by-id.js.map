{"version": 3, "sources": ["../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/get-script-request-by-id.js"], "sourceRoot": "/var/folders/hk/qs6yfsln2cx1tnsx6mfl43w00000gn/T/tmp-18157-w0YJH4Uy8b3p", "sourcesContent": ["const { createClient } = require('@supabase/supabase-js');\n\nconst supabaseUrl = process.env.SUPABASE_URL;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = createClient(supabaseUrl, supabaseKey);\n\n// Helper to convert object keys to camelCase\nconst toCamelCase = (s) => s.replace(/([-_][a-z])/ig, ($1) => {\n  return $1.toUpperCase()\n    .replace('-', '')\n    .replace('_', '');\n});\n\nconst keysToCamelCase = (o) => {\n  if (Array.isArray(o)) {\n    return o.map(v => keysToCamelCase(v));\n  } else if (o !== null && o.constructor === Object) {\n    return Object.keys(o).reduce((acc, key) => {\n      acc[toCamelCase(key)] = keysToCamelCase(o[key]);\n      return acc;\n    }, {});\n  }\n  return o;\n};\n\nexports.handler = async (event) => {\n  if (event.httpMethod !== 'GET') {\n    return { statusCode: 405, body: 'Method Not Allowed' };\n  }\n\n  try {\n    const { id } = event.queryStringParameters;\n    if (!id) {\n      return { statusCode: 400, body: JSON.stringify({ error: 'Request ID is required' }) };\n    }\n\n    const { data, error } = await supabase\n      .from('script_requests')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      if (error.code === 'PGRST116') { // PostgREST error for 'not found'\n        return { statusCode: 404, body: JSON.stringify({ error: 'Request not found' }) };\n      }\n      console.error('Error fetching script request:', error);\n      throw new Error('Failed to fetch request from the database.');\n    }\n\n    if (!data) {\n        return { statusCode: 404, body: JSON.stringify({ error: 'Request not found' }) };\n    }\n\n    return {\n      statusCode: 200,\n      body: JSON.stringify(keysToCamelCase(data)),\n    };\n\n  } catch (error) {\n    console.error('Get script request error:', error);\n    return {\n      statusCode: 500,\n      body: JSON.stringify({ error: error.message || 'An internal server error occurred.' }),\n    };\n  }\n};\n"], "mappings": ";AAAA,IAAM,EAAE,aAAa,IAAI,QAAQ,uBAAuB;AAExD,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,WAAW,aAAa,aAAa,WAAW;AAGtD,IAAM,cAAc,CAAC,MAAM,EAAE,QAAQ,iBAAiB,CAAC,OAAO;AAC5D,SAAO,GAAG,YAAY,EACnB,QAAQ,KAAK,EAAE,EACf,QAAQ,KAAK,EAAE;AACpB,CAAC;AAED,IAAM,kBAAkB,CAAC,MAAM;AAC7B,MAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,WAAO,EAAE,IAAI,OAAK,gBAAgB,CAAC,CAAC;AAAA,EACtC,WAAW,MAAM,QAAQ,EAAE,gBAAgB,QAAQ;AACjD,WAAO,OAAO,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,QAAQ;AACzC,UAAI,YAAY,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC;AAC9C,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,SAAO;AACT;AAEA,QAAQ,UAAU,OAAO,UAAU;AACjC,MAAI,MAAM,eAAe,OAAO;AAC9B,WAAO,EAAE,YAAY,KAAK,MAAM,qBAAqB;AAAA,EACvD;AAEA,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,MAAM;AACrB,QAAI,CAAC,IAAI;AACP,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,yBAAyB,CAAC,EAAE;AAAA,IACtF;AAEA,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,SAC3B,KAAK,iBAAiB,EACtB,OAAO,GAAG,EACV,GAAG,MAAM,EAAE,EACX,OAAO;AAEV,QAAI,OAAO;AACT,UAAI,MAAM,SAAS,YAAY;AAC7B,eAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,oBAAoB,CAAC,EAAE;AAAA,MACjF;AACA,cAAQ,MAAM,kCAAkC,KAAK;AACrD,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAC9D;AAEA,QAAI,CAAC,MAAM;AACP,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,oBAAoB,CAAC,EAAE;AAAA,IACnF;AAEA,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU,gBAAgB,IAAI,CAAC;AAAA,IAC5C;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,6BAA6B,KAAK;AAChD,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU,EAAE,OAAO,MAAM,WAAW,qCAAqC,CAAC;AAAA,IACvF;AAAA,EACF;AACF;", "names": []}