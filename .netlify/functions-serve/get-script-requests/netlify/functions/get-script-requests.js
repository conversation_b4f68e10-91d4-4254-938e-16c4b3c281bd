// netlify/functions/get-script-requests.js
var { createClient } = require("@supabase/supabase-js");
var supabaseUrl = process.env.SUPABASE_URL;
var supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
var supabase = createClient(supabaseUrl, supabaseKey);
exports.handler = async (event) => {
  if (event.httpMethod !== "GET") {
    return { statusCode: 405, body: "Method Not Allowed" };
  }
  try {
    const { data, error } = await supabase.from("script_requests").select("*").order("created_at", { ascending: false });
    if (error) {
      console.error("Error fetching script requests:", error);
      throw new Error("Failed to fetch script requests.");
    }
    return {
      statusCode: 200,
      body: JSON.stringify(data)
    };
  } catch (error) {
    console.error("Get script requests error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message || "An internal server error occurred." })
    };
  }
};
//# sourceMappingURL=get-script-requests.js.map
