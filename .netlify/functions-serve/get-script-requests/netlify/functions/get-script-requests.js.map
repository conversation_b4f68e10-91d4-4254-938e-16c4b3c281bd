{"version": 3, "sources": ["../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/get-script-requests.js"], "sourceRoot": "/var/folders/hk/qs6yfsln2cx1tnsx6mfl43w00000gn/T/tmp-18157-HL5V2LzciE6D", "sourcesContent": ["const { createClient } = require('@supabase/supabase-js');\n\nconst supabaseUrl = process.env.SUPABASE_URL;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = createClient(supabaseUrl, supabaseKey);\n\nexports.handler = async (event) => {\n  // For now, we'll allow GET requests without strict authentication\n  // In a real-world scenario, you'd want to protect this endpoint\n  if (event.httpMethod !== 'GET') {\n    return { statusCode: 405, body: 'Method Not Allowed' };\n  }\n\n  try {\n    const { data, error } = await supabase\n      .from('script_requests')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching script requests:', error);\n      throw new Error('Failed to fetch script requests.');\n    }\n\n    return {\n      statusCode: 200,\n      body: JSON.stringify(data),\n    };\n\n  } catch (error) {\n    console.error('Get script requests error:', error);\n    return {\n      statusCode: 500,\n      body: JSON.stringify({ error: error.message || 'An internal server error occurred.' }),\n    };\n  }\n};\n"], "mappings": ";AAAA,IAAM,EAAE,aAAa,IAAI,QAAQ,uBAAuB;AAExD,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,WAAW,aAAa,aAAa,WAAW;AAEtD,QAAQ,UAAU,OAAO,UAAU;AAGjC,MAAI,MAAM,eAAe,OAAO;AAC9B,WAAO,EAAE,YAAY,KAAK,MAAM,qBAAqB;AAAA,EACvD;AAEA,MAAI;AACF,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,SAC3B,KAAK,iBAAiB,EACtB,OAAO,GAAG,EACV,MAAM,cAAc,EAAE,WAAW,MAAM,CAAC;AAE3C,QAAI,OAAO;AACT,cAAQ,MAAM,mCAAmC,KAAK;AACtD,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AAEA,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU,IAAI;AAAA,IAC3B;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,8BAA8B,KAAK;AACjD,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU,EAAE,OAAO,MAAM,WAAW,qCAAqC,CAAC;AAAA,IACvF;AAAA,EACF;AACF;", "names": []}