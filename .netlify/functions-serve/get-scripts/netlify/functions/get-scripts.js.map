{"version": 3, "sources": ["../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/utils/auth.js", "../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/utils/api-handler.js", "../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/get-scripts.js"], "sourceRoot": "/var/folders/hk/qs6yfsln2cx1tnsx6mfl43w00000gn/T/tmp-18157-BZWWEfmhwr2o", "sourcesContent": ["const { createClient } = require('@supabase/supabase-js');\nconst crypto = require('crypto');\n\n// Initialize Supabase client\nconst supabase = createClient(\n  process.env.SUPABASE_URL,\n  process.env.SUPABASE_SERVICE_ROLE_KEY,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n);\n\n// Rate limiting configuration\nconst RATE_LIMIT = {\n  WINDOW_MS: 60 * 1000, // 1 minute\n  MAX_REQUESTS: 100\n};\n\n// Track rate limits by HWID\nconst rateLimitCache = new Map();\n\n// Helper to log auth events\nconst logAuthEvent = async (event) => {\n  const { data, error } = await supabase.from('auth_events').insert([event]);\n  if (error) {\n    console.error('Error logging auth event:', error);\n  }\n};\n\n/**\n * Middleware to authenticate API requests using API key\n */\nconst authenticateApiKey = async (req, res) => {\n  try {\n    // Get API key from headers\n    const apiKey = req.headers['x-api-key'] || req.query.api_key;\n    \n    if (!apiKey) {\n      return {\n        statusCode: 401,\n        body: JSON.stringify({ error: 'API key is required' })\n      };\n    }\n\n    // Validate API key format\n    if (typeof apiKey !== 'string' || !/^[a-f0-9]{64}$/i.test(apiKey)) {\n      return {\n        statusCode: 403,\n        body: JSON.stringify({ error: 'Invalid API key format' })\n      };\n    }\n\n    // Check if API key exists and is active\n    const { data: keyData, error: keyError } = await supabase\n      .from('api_keys')\n      .select('*, admin_profile(*)')\n      .eq('key_hash', hashApiKey(apiKey))\n      .eq('is_active', true)\n      .single();\n\n    if (keyError || !keyData) {\n      console.error('Invalid API key attempt:', { error: keyError });\n      return {\n        statusCode: 403,\n        body: JSON.stringify({ error: 'Invalid or inactive API key' })\n      };\n    }\n\n    // Check if key is expired\n    if (new Date(keyData.expires_at) < new Date()) {\n      // Auto-deactivate expired keys\n      await supabase\n        .from('api_keys')\n        .update({ is_active: false })\n        .eq('id', keyData.id);\n      \n      return {\n        statusCode: 403,\n        body: JSON.stringify({ error: 'API key has expired' })\n      };\n    }\n\n    // Get HWID and user agent from headers\n    const clientHwid = req.headers['x-client-hwid'] || 'unknown';\n    const userAgent = req.headers['user-agent'] || 'unknown';\n    \n    // Apply rate limiting by HWID\n    const now = Date.now();\n    const windowStart = now - RATE_LIMIT.WINDOW_MS;\n    \n    // Clean up old entries\n    for (const [hwid, timestamps] of rateLimitCache.entries()) {\n      const validTimestamps = timestamps.filter(ts => ts > windowStart);\n      if (validTimestamps.length > 0) {\n        rateLimitCache.set(hwid, validTimestamps);\n      } else {\n        rateLimitCache.delete(hwid);\n      }\n    }\n    \n    // Check rate limit\n    const requestTimestamps = rateLimitCache.get(clientHwid) || [];\n    if (requestTimestamps.length >= RATE_LIMIT.MAX_REQUESTS) {\n      // Log rate limit exceeded\n      await logAuthEvent({\n        event_type: 'rate_limit_exceeded',\n        user_id: keyData.admin_id,\n        metadata: {\n          key_id: keyData.id,\n          key_name: keyData.name,\n          request_count: requestTimestamps.length,\n          path: req.path,\n          method: req.httpMethod\n        },\n        hwid: clientHwid\n      });\n      \n      return {\n        statusCode: 429,\n        body: JSON.stringify({ \n          error: 'Too many requests',\n          retry_after: Math.ceil((requestTimestamps[0] - windowStart) / 1000)\n        })\n      };\n    }\n    \n    // Update rate limit cache\n    rateLimitCache.set(clientHwid, [...requestTimestamps, now]);\n    \n    // Log the API access\n    await logAuthEvent({\n      event_type: 'api_access',\n      user_id: keyData.admin_id,\n      metadata: {\n        key_id: keyData.id,\n        key_name: keyData.name,\n        path: req.path,\n        method: req.httpMethod,\n        params: req.queryStringParameters\n      },\n      hwid: clientHwid\n    });\n\n    // Update last used timestamp with HWID\n    await supabase\n      .from('api_keys')\n      .update({ \n        last_used_at: new Date().toISOString(),\n        last_used_hwid: clientHwid,\n        last_used_user_agent: userAgent,\n        use_count: (keyData.use_count || 0) + 1\n      })\n      .eq('id', keyData.id);\n\n    // Attach admin and key info to request context\n    return {\n      admin: keyData.admin_profile,\n      apiKey: {\n        id: keyData.id,\n        name: keyData.name,\n        scopes: keyData.scopes || [],\n        permissions: keyData.permissions || []\n      },\n      clientInfo: {\n        ip: clientIp,\n        userAgent,\n        hwid: req.headers['x-client-hwid']\n      }\n    };\n\n  } catch (error) {\n    console.error('Authentication error:', error);\n    return {\n      statusCode: 500,\n      body: JSON.stringify({ error: 'Internal server error during authentication' })\n    };\n  }\n};\n\n/**\n * Hash API key for secure storage\n */\nfunction hashApiKey(key) {\n  if (!key) return null;\n  \n  // Use a constant-time comparison to prevent timing attacks\n  const hash = crypto\n    .createHash('sha256')\n    .update(key + process.env.API_KEY_SALT)\n    .digest('hex');\n    \n  return hash;\n}\n\n/**\n * Generate a new secure API key\n */\nfunction generateApiKey() {\n  // Generate a cryptographically secure random key\n  const key = `sk_${crypto.randomBytes(32).toString('hex')}`;\n  const hash = hashApiKey(key);\n  \n  // Generate a key fingerprint for display purposes\n  const fingerprint = crypto\n    .createHash('sha256')\n    .update(key)\n    .digest('hex')\n    .slice(0, 16);\n    \n  return { \n    key, \n    hash,\n    fingerprint: `sk_${fingerprint}`\n  };\n}\n\n/**\n * Check if admin has required permissions\n */\nconst hasPermission = (context, requiredPermission) => {\n  if (!context || !context.apiKey) return false;\n  \n  // If no permissions required, allow access\n  if (!requiredPermission) return true;\n  \n  // Check if the API key has the required permission\n  return context.apiKey.permissions.includes(requiredPermission) ||\n         context.apiKey.permissions.includes('*'); // Wildcard for full access\n};\n\n// Clean up rate limit cache periodically\nsetInterval(() => {\n  const now = Date.now();\n  const windowStart = now - (RATE_LIMIT.WINDOW_MS * 2); // Clean up entries older than 2 windows\n  \n  for (const [hwid, timestamps] of rateLimitCache.entries()) {\n    const validTimestamps = timestamps.filter(ts => ts > windowStart);\n    if (validTimestamps.length > 0) {\n      rateLimitCache.set(hwid, validTimestamps);\n    } else {\n      rateLimitCache.delete(hwid);\n    }\n  }\n}, RATE_LIMIT.WINDOW_MS * 5);\n\nmodule.exports = {\n  authenticateApiKey,\n  generateApiKey,\n  hashApiKey,\n  hasPermission,\n  logAuthEvent\n};\n", "const { createClient } = require('@supabase/supabase-js');\nconst { authenticateApiKey, hasPermission } = require('./auth');\n\n// Initialize Supabase client with service role\nconst supabase = createClient(\n  process.env.SUPABASE_URL,\n  process.env.SUPABASE_SERVICE_ROLE_KEY\n);\n\n/**\n * Create an API handler with common functionality\n * @param {Object} options - Handler options\n * @param {Array} options.requiredPermissions - Array of required permissions\n * @param {Function} options.handler - The actual request handler\n * @param {boolean} options.requireAuth - Whether authentication is required (default: true)\n * @returns {Function} The wrapped handler function\n */\nfunction createApiHandler({\n  requiredPermissions = [],\n  handler,\n  requireAuth = true\n}) {\n  return async (event, context) => {\n    // Default CORS headers\n    const headers = {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-api-key',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n      'Content-Type': 'application/json'\n    };\n\n    // Handle CORS preflight\n    if (event.httpMethod === 'OPTIONS') {\n      return {\n        statusCode: 200,\n        headers,\n        body: ''\n      };\n    }\n\n    try {\n      // Get client info for logging\n      const clientIp = event.headers['x-forwarded-for'] || event.connection.remoteAddress;\n      const userAgent = event.headers['user-agent'] || '';\n      const hwid = event.headers['x-client-hwid'] || '';\n\n      // Parse request body if present\n      let body = {};\n      if (event.body) {\n        try {\n          body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;\n        } catch (error) {\n          console.error('Error parsing request body:', error);\n          return {\n            statusCode: 400,\n            headers,\n            body: JSON.stringify({ error: 'Invalid JSON in request body' })\n          };\n        }\n      }\n\n      // Authenticate the request if required\n      let auth = {};\n      if (requireAuth) {\n        const authResult = await authenticateApiKey(event, context);\n        if (authResult.statusCode) {\n          return { ...authResult, headers };\n        }\n        auth = authResult;\n\n        // Check permissions if required\n        if (requiredPermissions.length > 0) {\n          const hasAllPermissions = requiredPermissions.every(permission => \n            hasPermission(auth, permission)\n          );\n          \n          if (!hasAllPermissions) {\n            console.warn('Permission denied:', { \n              requiredPermissions,\n              hasPermissions: auth.apiKey?.permissions || []\n            });\n            \n            return {\n              statusCode: 403,\n              headers,\n              body: JSON.stringify({ \n                error: 'Insufficient permissions',\n                required: requiredPermissions\n              })\n            };\n          }\n        }\n      }\n\n      // Parse query parameters\n      const query = event.queryStringParameters || {};\n      \n      // Call the actual handler\n      const result = await handler({\n        event,\n        context,\n        body,\n        query,\n        params: event.pathParameters || {},\n        headers: event.headers,\n        auth,\n        supabase,\n        clientInfo: {\n          ip: clientIp,\n          userAgent,\n          hwid\n        }\n      });\n\n      // Ensure the response has the correct format\n      const response = {\n        statusCode: result.statusCode || 200,\n        headers: { ...headers, ...(result.headers || {}) },\n        body: typeof result.body === 'string' ? result.body : JSON.stringify(result.body || {})\n      };\n\n      return response;\n\n    } catch (error) {\n      console.error('API handler error:', error);\n      \n      // Don't leak internal errors in production\n      const errorMessage = process.env.NODE_ENV === 'development' \n        ? error.message \n        : 'An unexpected error occurred';\n      \n      return {\n        statusCode: error.statusCode || 500,\n        headers,\n        body: JSON.stringify({ \n          error: errorMessage,\n          ...(process.env.NODE_ENV === 'development' ? { stack: error.stack } : {})\n        })\n      };\n    }\n  };\n}\n\n/**\n * Helper to parse and validate request body against a schema\n */\nfunction validateBody(schema) {\n  return (body) => {\n    const { error, value } = schema.validate(body, { abortEarly: false });\n    if (error) {\n      const validationError = new Error('Validation error');\n      validationError.statusCode = 400;\n      validationError.details = error.details.map(d => ({\n        message: d.message,\n        path: d.path,\n        type: d.type\n      }));\n      throw validationError;\n    }\n    return value;\n  };\n}\n\nmodule.exports = {\n  createApiHandler,\n  validateBody,\n  supabase\n};\n", "const { createApiHandler, supabase } = require('./utils/api-handler');\n\n// Get all active scripts for the public\nconst getScripts = async () => {\n  try {\n    console.log('Fetching scripts from database...');\n    const { data, error, status } = await supabase\n      .from('scripts')\n      .select('*')\n      .eq('is_active', true)\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Database error:', { error, status });\n      return { \n        statusCode: status || 500, \n        body: { \n          error: 'Failed to fetch scripts',\n          details: error.message \n        } \n      };\n    }\n    \n    console.log(`Successfully fetched ${data.length} scripts`);\n    return { \n      statusCode: 200, \n      body: data \n    };\n  } catch (error) {\n    console.error('Unexpected error in getScripts:', error);\n    return {\n      statusCode: 500,\n      body: { \n        error: 'An unexpected error occurred',\n        details: error.message \n      }\n    };\n  }\n};\n\n// Main handler\nexports.handler = createApiHandler({\n  requireAuth: false, // Public endpoint\n  async handler(ctx) {\n    const { httpMethod } = ctx.event;\n\n    if (httpMethod === 'GET') {\n      return getScripts(ctx);\n    }\n    \n    return { statusCode: 405, body: { error: 'Method Not Allowed' } };\n  }\n});\n"], "mappings": ";;;;;;AAAA;AAAA,oCAAAA,UAAAC,SAAA;AAAA,QAAM,EAAE,aAAa,IAAI,QAAQ,uBAAuB;AACxD,QAAM,SAAS,QAAQ,QAAQ;AAG/B,QAAMC,YAAW;AAAA,MACf,QAAQ,IAAI;AAAA,MACZ,QAAQ,IAAI;AAAA,MACZ;AAAA,QACE,MAAM;AAAA,UACJ,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAGA,QAAM,aAAa;AAAA,MACjB,WAAW,KAAK;AAAA;AAAA,MAChB,cAAc;AAAA,IAChB;AAGA,QAAM,iBAAiB,oBAAI,IAAI;AAG/B,QAAM,eAAe,OAAO,UAAU;AACpC,YAAM,EAAE,MAAM,MAAM,IAAI,MAAMA,UAAS,KAAK,aAAa,EAAE,OAAO,CAAC,KAAK,CAAC;AACzE,UAAI,OAAO;AACT,gBAAQ,MAAM,6BAA6B,KAAK;AAAA,MAClD;AAAA,IACF;AAKA,QAAM,qBAAqB,OAAO,KAAK,QAAQ;AAC7C,UAAI;AAEF,cAAM,SAAS,IAAI,QAAQ,WAAW,KAAK,IAAI,MAAM;AAErD,YAAI,CAAC,QAAQ;AACX,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,MAAM,KAAK,UAAU,EAAE,OAAO,sBAAsB,CAAC;AAAA,UACvD;AAAA,QACF;AAGA,YAAI,OAAO,WAAW,YAAY,CAAC,kBAAkB,KAAK,MAAM,GAAG;AACjE,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,MAAM,KAAK,UAAU,EAAE,OAAO,yBAAyB,CAAC;AAAA,UAC1D;AAAA,QACF;AAGA,cAAM,EAAE,MAAM,SAAS,OAAO,SAAS,IAAI,MAAMA,UAC9C,KAAK,UAAU,EACf,OAAO,qBAAqB,EAC5B,GAAG,YAAY,WAAW,MAAM,CAAC,EACjC,GAAG,aAAa,IAAI,EACpB,OAAO;AAEV,YAAI,YAAY,CAAC,SAAS;AACxB,kBAAQ,MAAM,4BAA4B,EAAE,OAAO,SAAS,CAAC;AAC7D,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,MAAM,KAAK,UAAU,EAAE,OAAO,8BAA8B,CAAC;AAAA,UAC/D;AAAA,QACF;AAGA,YAAI,IAAI,KAAK,QAAQ,UAAU,IAAI,oBAAI,KAAK,GAAG;AAE7C,gBAAMA,UACH,KAAK,UAAU,EACf,OAAO,EAAE,WAAW,MAAM,CAAC,EAC3B,GAAG,MAAM,QAAQ,EAAE;AAEtB,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,MAAM,KAAK,UAAU,EAAE,OAAO,sBAAsB,CAAC;AAAA,UACvD;AAAA,QACF;AAGA,cAAM,aAAa,IAAI,QAAQ,eAAe,KAAK;AACnD,cAAM,YAAY,IAAI,QAAQ,YAAY,KAAK;AAG/C,cAAM,MAAM,KAAK,IAAI;AACrB,cAAM,cAAc,MAAM,WAAW;AAGrC,mBAAW,CAAC,MAAM,UAAU,KAAK,eAAe,QAAQ,GAAG;AACzD,gBAAM,kBAAkB,WAAW,OAAO,QAAM,KAAK,WAAW;AAChE,cAAI,gBAAgB,SAAS,GAAG;AAC9B,2BAAe,IAAI,MAAM,eAAe;AAAA,UAC1C,OAAO;AACL,2BAAe,OAAO,IAAI;AAAA,UAC5B;AAAA,QACF;AAGA,cAAM,oBAAoB,eAAe,IAAI,UAAU,KAAK,CAAC;AAC7D,YAAI,kBAAkB,UAAU,WAAW,cAAc;AAEvD,gBAAM,aAAa;AAAA,YACjB,YAAY;AAAA,YACZ,SAAS,QAAQ;AAAA,YACjB,UAAU;AAAA,cACR,QAAQ,QAAQ;AAAA,cAChB,UAAU,QAAQ;AAAA,cAClB,eAAe,kBAAkB;AAAA,cACjC,MAAM,IAAI;AAAA,cACV,QAAQ,IAAI;AAAA,YACd;AAAA,YACA,MAAM;AAAA,UACR,CAAC;AAED,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ,MAAM,KAAK,UAAU;AAAA,cACnB,OAAO;AAAA,cACP,aAAa,KAAK,MAAM,kBAAkB,CAAC,IAAI,eAAe,GAAI;AAAA,YACpE,CAAC;AAAA,UACH;AAAA,QACF;AAGA,uBAAe,IAAI,YAAY,CAAC,GAAG,mBAAmB,GAAG,CAAC;AAG1D,cAAM,aAAa;AAAA,UACjB,YAAY;AAAA,UACZ,SAAS,QAAQ;AAAA,UACjB,UAAU;AAAA,YACR,QAAQ,QAAQ;AAAA,YAChB,UAAU,QAAQ;AAAA,YAClB,MAAM,IAAI;AAAA,YACV,QAAQ,IAAI;AAAA,YACZ,QAAQ,IAAI;AAAA,UACd;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAGD,cAAMA,UACH,KAAK,UAAU,EACf,OAAO;AAAA,UACN,eAAc,oBAAI,KAAK,GAAE,YAAY;AAAA,UACrC,gBAAgB;AAAA,UAChB,sBAAsB;AAAA,UACtB,YAAY,QAAQ,aAAa,KAAK;AAAA,QACxC,CAAC,EACA,GAAG,MAAM,QAAQ,EAAE;AAGtB,eAAO;AAAA,UACL,OAAO,QAAQ;AAAA,UACf,QAAQ;AAAA,YACN,IAAI,QAAQ;AAAA,YACZ,MAAM,QAAQ;AAAA,YACd,QAAQ,QAAQ,UAAU,CAAC;AAAA,YAC3B,aAAa,QAAQ,eAAe,CAAC;AAAA,UACvC;AAAA,UACA,YAAY;AAAA,YACV,IAAI;AAAA,YACJ;AAAA,YACA,MAAM,IAAI,QAAQ,eAAe;AAAA,UACnC;AAAA,QACF;AAAA,MAEF,SAAS,OAAO;AACd,gBAAQ,MAAM,yBAAyB,KAAK;AAC5C,eAAO;AAAA,UACL,YAAY;AAAA,UACZ,MAAM,KAAK,UAAU,EAAE,OAAO,8CAA8C,CAAC;AAAA,QAC/E;AAAA,MACF;AAAA,IACF;AAKA,aAAS,WAAW,KAAK;AACvB,UAAI,CAAC,IAAK,QAAO;AAGjB,YAAM,OAAO,OACV,WAAW,QAAQ,EACnB,OAAO,MAAM,QAAQ,IAAI,YAAY,EACrC,OAAO,KAAK;AAEf,aAAO;AAAA,IACT;AAKA,aAAS,iBAAiB;AAExB,YAAM,MAAM,MAAM,OAAO,YAAY,EAAE,EAAE,SAAS,KAAK,CAAC;AACxD,YAAM,OAAO,WAAW,GAAG;AAG3B,YAAM,cAAc,OACjB,WAAW,QAAQ,EACnB,OAAO,GAAG,EACV,OAAO,KAAK,EACZ,MAAM,GAAG,EAAE;AAEd,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,aAAa,MAAM,WAAW;AAAA,MAChC;AAAA,IACF;AAKA,QAAM,gBAAgB,CAAC,SAAS,uBAAuB;AACrD,UAAI,CAAC,WAAW,CAAC,QAAQ,OAAQ,QAAO;AAGxC,UAAI,CAAC,mBAAoB,QAAO;AAGhC,aAAO,QAAQ,OAAO,YAAY,SAAS,kBAAkB,KACtD,QAAQ,OAAO,YAAY,SAAS,GAAG;AAAA,IAChD;AAGA,gBAAY,MAAM;AAChB,YAAM,MAAM,KAAK,IAAI;AACrB,YAAM,cAAc,MAAO,WAAW,YAAY;AAElD,iBAAW,CAAC,MAAM,UAAU,KAAK,eAAe,QAAQ,GAAG;AACzD,cAAM,kBAAkB,WAAW,OAAO,QAAM,KAAK,WAAW;AAChE,YAAI,gBAAgB,SAAS,GAAG;AAC9B,yBAAe,IAAI,MAAM,eAAe;AAAA,QAC1C,OAAO;AACL,yBAAe,OAAO,IAAI;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,GAAG,WAAW,YAAY,CAAC;AAE3B,IAAAD,QAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC9PA;AAAA,2CAAAE,UAAAC,SAAA;AAAA,QAAM,EAAE,aAAa,IAAI,QAAQ,uBAAuB;AACxD,QAAM,EAAE,oBAAoB,cAAc,IAAI;AAG9C,QAAMC,YAAW;AAAA,MACf,QAAQ,IAAI;AAAA,MACZ,QAAQ,IAAI;AAAA,IACd;AAUA,aAASC,kBAAiB;AAAA,MACxB,sBAAsB,CAAC;AAAA,MACvB;AAAA,MACA,cAAc;AAAA,IAChB,GAAG;AACD,aAAO,OAAO,OAAO,YAAY;AAE/B,cAAM,UAAU;AAAA,UACd,+BAA+B;AAAA,UAC/B,gCAAgC;AAAA,UAChC,gCAAgC;AAAA,UAChC,gBAAgB;AAAA,QAClB;AAGA,YAAI,MAAM,eAAe,WAAW;AAClC,iBAAO;AAAA,YACL,YAAY;AAAA,YACZ;AAAA,YACA,MAAM;AAAA,UACR;AAAA,QACF;AAEA,YAAI;AAEF,gBAAMC,YAAW,MAAM,QAAQ,iBAAiB,KAAK,MAAM,WAAW;AACtE,gBAAM,YAAY,MAAM,QAAQ,YAAY,KAAK;AACjD,gBAAM,OAAO,MAAM,QAAQ,eAAe,KAAK;AAG/C,cAAI,OAAO,CAAC;AACZ,cAAI,MAAM,MAAM;AACd,gBAAI;AACF,qBAAO,OAAO,MAAM,SAAS,WAAW,KAAK,MAAM,MAAM,IAAI,IAAI,MAAM;AAAA,YACzE,SAAS,OAAO;AACd,sBAAQ,MAAM,+BAA+B,KAAK;AAClD,qBAAO;AAAA,gBACL,YAAY;AAAA,gBACZ;AAAA,gBACA,MAAM,KAAK,UAAU,EAAE,OAAO,+BAA+B,CAAC;AAAA,cAChE;AAAA,YACF;AAAA,UACF;AAGA,cAAI,OAAO,CAAC;AACZ,cAAI,aAAa;AACf,kBAAM,aAAa,MAAM,mBAAmB,OAAO,OAAO;AAC1D,gBAAI,WAAW,YAAY;AACzB,qBAAO,EAAE,GAAG,YAAY,QAAQ;AAAA,YAClC;AACA,mBAAO;AAGP,gBAAI,oBAAoB,SAAS,GAAG;AAClC,oBAAM,oBAAoB,oBAAoB;AAAA,gBAAM,gBAClD,cAAc,MAAM,UAAU;AAAA,cAChC;AAEA,kBAAI,CAAC,mBAAmB;AACtB,wBAAQ,KAAK,sBAAsB;AAAA,kBACjC;AAAA,kBACA,gBAAgB,KAAK,QAAQ,eAAe,CAAC;AAAA,gBAC/C,CAAC;AAED,uBAAO;AAAA,kBACL,YAAY;AAAA,kBACZ;AAAA,kBACA,MAAM,KAAK,UAAU;AAAA,oBACnB,OAAO;AAAA,oBACP,UAAU;AAAA,kBACZ,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAGA,gBAAM,QAAQ,MAAM,yBAAyB,CAAC;AAG9C,gBAAM,SAAS,MAAM,QAAQ;AAAA,YAC3B;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA,QAAQ,MAAM,kBAAkB,CAAC;AAAA,YACjC,SAAS,MAAM;AAAA,YACf;AAAA,YACA,UAAAF;AAAA,YACA,YAAY;AAAA,cACV,IAAIE;AAAA,cACJ;AAAA,cACA;AAAA,YACF;AAAA,UACF,CAAC;AAGD,gBAAM,WAAW;AAAA,YACf,YAAY,OAAO,cAAc;AAAA,YACjC,SAAS,EAAE,GAAG,SAAS,GAAI,OAAO,WAAW,CAAC,EAAG;AAAA,YACjD,MAAM,OAAO,OAAO,SAAS,WAAW,OAAO,OAAO,KAAK,UAAU,OAAO,QAAQ,CAAC,CAAC;AAAA,UACxF;AAEA,iBAAO;AAAA,QAET,SAAS,OAAO;AACd,kBAAQ,MAAM,sBAAsB,KAAK;AAGzC,gBAAM,eAAe,QAAQ,IAAI,aAAa,gBAC1C,MAAM,UACN;AAEJ,iBAAO;AAAA,YACL,YAAY,MAAM,cAAc;AAAA,YAChC;AAAA,YACA,MAAM,KAAK,UAAU;AAAA,cACnB,OAAO;AAAA,cACP,GAAI,QAAQ,IAAI,aAAa,gBAAgB,EAAE,OAAO,MAAM,MAAM,IAAI,CAAC;AAAA,YACzE,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAKA,aAAS,aAAa,QAAQ;AAC5B,aAAO,CAAC,SAAS;AACf,cAAM,EAAE,OAAO,MAAM,IAAI,OAAO,SAAS,MAAM,EAAE,YAAY,MAAM,CAAC;AACpE,YAAI,OAAO;AACT,gBAAM,kBAAkB,IAAI,MAAM,kBAAkB;AACpD,0BAAgB,aAAa;AAC7B,0BAAgB,UAAU,MAAM,QAAQ,IAAI,QAAM;AAAA,YAChD,SAAS,EAAE;AAAA,YACX,MAAM,EAAE;AAAA,YACR,MAAM,EAAE;AAAA,UACV,EAAE;AACF,gBAAM;AAAA,QACR;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,IAAAH,QAAO,UAAU;AAAA,MACf,kBAAAE;AAAA,MACA;AAAA,MACA,UAAAD;AAAA,IACF;AAAA;AAAA;;;ACvKA,IAAM,EAAE,kBAAkB,SAAS,IAAI;AAGvC,IAAM,aAAa,YAAY;AAC7B,MAAI;AACF,YAAQ,IAAI,mCAAmC;AAC/C,UAAM,EAAE,MAAM,OAAO,OAAO,IAAI,MAAM,SACnC,KAAK,SAAS,EACd,OAAO,GAAG,EACV,GAAG,aAAa,IAAI,EACpB,MAAM,cAAc,EAAE,WAAW,MAAM,CAAC;AAE3C,QAAI,OAAO;AACT,cAAQ,MAAM,mBAAmB,EAAE,OAAO,OAAO,CAAC;AAClD,aAAO;AAAA,QACL,YAAY,UAAU;AAAA,QACtB,MAAM;AAAA,UACJ,OAAO;AAAA,UACP,SAAS,MAAM;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAEA,YAAQ,IAAI,wBAAwB,KAAK,MAAM,UAAU;AACzD,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,mCAAmC,KAAK;AACtD,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,SAAS,MAAM;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;AAGA,QAAQ,UAAU,iBAAiB;AAAA,EACjC,aAAa;AAAA;AAAA,EACb,MAAM,QAAQ,KAAK;AACjB,UAAM,EAAE,WAAW,IAAI,IAAI;AAE3B,QAAI,eAAe,OAAO;AACxB,aAAO,WAAW,GAAG;AAAA,IACvB;AAEA,WAAO,EAAE,YAAY,KAAK,MAAM,EAAE,OAAO,qBAAqB,EAAE;AAAA,EAClE;AACF,CAAC;", "names": ["exports", "module", "supabase", "exports", "module", "supabase", "createApiHandler", "clientIp"]}