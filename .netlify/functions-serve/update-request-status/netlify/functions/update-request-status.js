// netlify/functions/update-request-status.js
var { createClient } = require("@supabase/supabase-js");
var supabaseUrl = process.env.SUPABASE_URL;
var supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
var supabase = createClient(supabaseUrl, supabaseKey);
exports.handler = async (event) => {
  if (event.httpMethod !== "POST") {
    return { statusCode: 405, body: "Method Not Allowed" };
  }
  try {
    const { requestId, status } = JSON.parse(event.body);
    if (!requestId || !status) {
      return { statusCode: 400, body: JSON.stringify({ error: "Missing requestId or status." }) };
    }
    const allowedStatus = ["pending", "approved", "denied", "completed"];
    if (!allowedStatus.includes(status)) {
      return { statusCode: 400, body: JSON.stringify({ error: "Invalid status provided." }) };
    }
    const { data, error } = await supabase.from("script_requests").update({ status }).eq("id", requestId).select().single();
    if (error) {
      console.error("Error updating request status:", error);
      return { statusCode: 500, body: JSON.stringify({ error: "Failed to update request status" }) };
    }
    const { error: logError } = await supabase.from("script_request_logs").insert({
      request_id: requestId,
      action: "status_updated",
      details: { new_status: status }
      // TODO: Add admin_user_id once authentication is added to this endpoint
    });
    if (logError) {
      console.error("Error logging status update:", logError);
    }
    return { statusCode: 200, body: JSON.stringify({ message: "Status updated successfully", data }) };
  } catch (error) {
    console.error("Update request status error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message || "An internal server error occurred." })
    };
  }
};
//# sourceMappingURL=update-request-status.js.map
