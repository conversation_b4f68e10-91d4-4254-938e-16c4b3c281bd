{"version": 3, "sources": ["../../../../../../Users/<USER>/Documents/Website_React/netlify/functions/update-request-status.js"], "sourceRoot": "/var/folders/hk/qs6yfsln2cx1tnsx6mfl43w00000gn/T/tmp-18157-ugmvApG6tXup", "sourcesContent": ["const { createClient } = require('@supabase/supabase-js');\n\nconst supabaseUrl = process.env.SUPABASE_URL;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabase = createClient(supabaseUrl, supabaseKey);\n\nexports.handler = async (event) => {\n  // TODO: Add admin authentication to protect this endpoint\n  if (event.httpMethod !== 'POST') {\n    return { statusCode: 405, body: 'Method Not Allowed' };\n  }\n\n  try {\n    const { requestId, status } = JSON.parse(event.body);\n\n    if (!requestId || !status) {\n      return { statusCode: 400, body: JSON.stringify({ error: 'Missing requestId or status.' }) };\n    }\n\n    const allowedStatus = ['pending', 'approved', 'denied', 'completed'];\n    if (!allowedStatus.includes(status)) {\n      return { statusCode: 400, body: JSON.stringify({ error: 'Invalid status provided.' }) };\n    }\n\n    const { data, error } = await supabase\n      .from('script_requests')\n      .update({ status })\n      .eq('id', requestId)\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error updating request status:', error);\n      return { statusCode: 500, body: JSON.stringify({ error: 'Failed to update request status' }) };\n    }\n\n    // Log the status update event\n    const { error: logError } = await supabase\n      .from('script_request_logs')\n      .insert({\n        request_id: requestId,\n        action: 'status_updated',\n        details: { new_status: status }\n        // TODO: Add admin_user_id once authentication is added to this endpoint\n      });\n\n    if (logError) {\n      console.error('Error logging status update:', logError);\n      // Don't fail the request, but log the error\n    }\n\n    return { statusCode: 200, body: JSON.stringify({ message: 'Status updated successfully', data }) };\n\n  } catch (error) {\n    console.error('Update request status error:', error);\n    return {\n      statusCode: 500,\n      body: JSON.stringify({ error: error.message || 'An internal server error occurred.' }),\n    };\n  }\n};\n"], "mappings": ";AAAA,IAAM,EAAE,aAAa,IAAI,QAAQ,uBAAuB;AAExD,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,cAAc,QAAQ,IAAI;AAChC,IAAM,WAAW,aAAa,aAAa,WAAW;AAEtD,QAAQ,UAAU,OAAO,UAAU;AAEjC,MAAI,MAAM,eAAe,QAAQ;AAC/B,WAAO,EAAE,YAAY,KAAK,MAAM,qBAAqB;AAAA,EACvD;AAEA,MAAI;AACF,UAAM,EAAE,WAAW,OAAO,IAAI,KAAK,MAAM,MAAM,IAAI;AAEnD,QAAI,CAAC,aAAa,CAAC,QAAQ;AACzB,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,+BAA+B,CAAC,EAAE;AAAA,IAC5F;AAEA,UAAM,gBAAgB,CAAC,WAAW,YAAY,UAAU,WAAW;AACnE,QAAI,CAAC,cAAc,SAAS,MAAM,GAAG;AACnC,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,2BAA2B,CAAC,EAAE;AAAA,IACxF;AAEA,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,SAC3B,KAAK,iBAAiB,EACtB,OAAO,EAAE,OAAO,CAAC,EACjB,GAAG,MAAM,SAAS,EAClB,OAAO,EACP,OAAO;AAEV,QAAI,OAAO;AACT,cAAQ,MAAM,kCAAkC,KAAK;AACrD,aAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,OAAO,kCAAkC,CAAC,EAAE;AAAA,IAC/F;AAGA,UAAM,EAAE,OAAO,SAAS,IAAI,MAAM,SAC/B,KAAK,qBAAqB,EAC1B,OAAO;AAAA,MACN,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,SAAS,EAAE,YAAY,OAAO;AAAA;AAAA,IAEhC,CAAC;AAEH,QAAI,UAAU;AACZ,cAAQ,MAAM,gCAAgC,QAAQ;AAAA,IAExD;AAEA,WAAO,EAAE,YAAY,KAAK,MAAM,KAAK,UAAU,EAAE,SAAS,+BAA+B,KAAK,CAAC,EAAE;AAAA,EAEnG,SAAS,OAAO;AACd,YAAQ,MAAM,gCAAgC,KAAK;AACnD,WAAO;AAAA,MACL,YAAY;AAAA,MACZ,MAAM,KAAK,UAAU,EAAE,OAAO,MAAM,WAAW,qCAAqC,CAAC;AAAA,IACvF;AAAA,EACF;AACF;", "names": []}