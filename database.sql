-- ========================================================================
-- COMPLETE DATABASE SCHEMA WITH ENHANCED SECURITY
-- This includes script system + hardened key system with anti-bypass measures
-- ========================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ========================================================================
-- SCRIPT SYSTEM TABLES
-- ========================================================================

-- Create the 'scripts' table with enhanced fields
CREATE TABLE IF NOT EXISTS public.scripts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    content TEXT, -- Script content for copying
    file_url TEXT,
    category TEXT DEFAULT 'other' CHECK (category IN ('game', 'utility', 'other')),
    tags JSONB DEFAULT '[]', -- Store tags as JSON array
    executor TEXT, -- Supported executors
    version TEXT DEFAULT '1.0.0',
    views INTEGER DEFAULT 0, -- View count
    rating DECIMAL(3,2) DEFAULT 0.00 CHECK (rating >= 0 AND rating <= 5), -- Average rating
    rating_count INTEGER DEFAULT 0, -- Number of ratings
    uploaded_by TEXT, -- store username instead of UUID
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true -- Whether script is active/published
);

-- Create the 'script_ratings' table for user ratings
CREATE TABLE IF NOT EXISTS public.script_ratings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    script_id UUID REFERENCES public.scripts(id) ON DELETE CASCADE,
    user_name TEXT NOT NULL, -- Store username instead of UUID
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(script_id, user_name) -- One rating per user per script
);

-- Create the 'script_views' table for tracking views
CREATE TABLE IF NOT EXISTS public.script_views (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    script_id UUID REFERENCES public.scripts(id) ON DELETE CASCADE,
    user_name TEXT, -- Can be null for anonymous views
    ip_address TEXT,
    viewed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create the 'script_update_logs' table for update history
CREATE TABLE IF NOT EXISTS public.script_update_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    script_id UUID REFERENCES public.scripts(id) ON DELETE CASCADE,
    version TEXT NOT NULL,
    changes TEXT NOT NULL, -- Description of changes
    updated_by TEXT NOT NULL, -- Admin username
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create the 'script_requests' table
CREATE TABLE IF NOT EXISTS public.script_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_name TEXT NOT NULL, -- store username instead of UUID
    script_name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'denied', 'working')),
    requested_at TIMESTAMPTZ DEFAULT NOW(),
    reviewed_by TEXT, -- store admin username
    reviewed_at TIMESTAMPTZ,
    admin_notes TEXT
);

-- ========================================================================
-- ENHANCED KEY SYSTEM TABLES
-- ========================================================================

-- Lootlabs campaigns (replacing Linkvertise)
CREATE TABLE IF NOT EXISTS lootlabs_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    tier_id INTEGER NOT NULL CHECK (tier_id >= 1 AND tier_id <= 3), -- 1=MCPEDL Safe, 2=Gaming Offers, 3=Profit Max
    number_of_tasks INTEGER NOT NULL CHECK (number_of_tasks >= 1 AND number_of_tasks <= 5), -- Number of tasks per session
    theme INTEGER DEFAULT 3 CHECK (theme >= 1 AND theme <= 5), -- 1=Classic, 2=Sims, 3=Minecraft, 4=GTA, 5=Space
    return_url TEXT NOT NULL, -- Base URL for return after completion
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced license keys table with security fields
CREATE TABLE IF NOT EXISTS license_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_code VARCHAR(255) UNIQUE NOT NULL, -- MADARA-XXXX-XXXX-XXXX format
    campaign_id UUID REFERENCES lootlabs_campaigns(id),
    ip_address INET,
    user_agent TEXT,
    fingerprint_hash VARCHAR(255), -- Device fingerprint
    roblox_hwid VARCHAR(255), -- Roblox Hardware ID
    is_active BOOLEAN DEFAULT true,
    is_revoked BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    bypass_attempts INTEGER DEFAULT 0,
    created_by_admin BOOLEAN DEFAULT false,
    -- Enhanced security fields
    security_data JSONB DEFAULT '{}', -- VM detection, behavior data, etc.
    session_id TEXT, -- Link to key_sessions
    last_heartbeat TIMESTAMP WITH TIME ZONE, -- For heartbeat validation
    active_sessions INTEGER DEFAULT 0 -- Concurrent session tracking
);

-- HWID bindings
CREATE TABLE IF NOT EXISTS hwid_bindings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_id UUID REFERENCES license_keys(id) ON DELETE CASCADE,
    hwid_hash VARCHAR(255) NOT NULL,
    roblox_username VARCHAR(255),
    bound_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Enhanced key usage logs with admin tracking
CREATE TABLE IF NOT EXISTS key_usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_id UUID REFERENCES license_keys(id) ON DELETE CASCADE,
    hwid_hash VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    action VARCHAR(50) NOT NULL, -- 'validate', 'revoke', 'expire', 'bypass_attempt', etc.
    success BOOLEAN NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Enhanced tracking
    admin_username TEXT, -- For admin actions
    details TEXT -- Additional context
);

-- Session tracking for Linkvertise steps
CREATE TABLE IF NOT EXISTS key_sessions (
    session_id TEXT PRIMARY KEY,
    step1 BOOLEAN NOT NULL DEFAULT false,
    step2 BOOLEAN NOT NULL DEFAULT false,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    -- Enhanced session tracking
    ip_address INET,
    user_agent TEXT,
    campaign_id UUID REFERENCES lootlabs_campaigns(id), -- Add campaign_id for verification
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Token replay prevention
CREATE TABLE IF NOT EXISTS used_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token VARCHAR(64) UNIQUE NOT NULL,
    session_id TEXT NOT NULL,
    step INTEGER NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User behavior analysis
CREATE TABLE IF NOT EXISTS user_behavior_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_id UUID REFERENCES license_keys(id) ON DELETE CASCADE,
    session_duration INTEGER,
    mouse_movement_count INTEGER,
    click_count INTEGER,
    typing_speed_wpm INTEGER,
    focus_loss_count INTEGER,
    anomaly_score DECIMAL(3,2),
    suspicious_flags JSONB DEFAULT '{}',
    behavior_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================================================
-- SECURITY MANAGEMENT TABLES
-- ========================================================================

-- IP ban management
CREATE TABLE IF NOT EXISTS ip_bans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ip_address INET NOT NULL,
    reason TEXT NOT NULL,
    violation_type TEXT NOT NULL, -- 'console_access', 'code_injection', 'script_tampering', etc.
    ban_duration_minutes INTEGER NOT NULL DEFAULT 60, -- Duration in minutes
    banned_by TEXT NOT NULL, -- Admin username or 'system'
    banned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Always set for temporary bans
    is_active BOOLEAN DEFAULT true,
    violation_count INTEGER DEFAULT 1, -- Track repeat offenses for escalation
    notes TEXT,
    UNIQUE(ip_address, violation_type) -- Allow multiple ban types per IP
);

-- Security events log
CREATE TABLE IF NOT EXISTS security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL, -- 'ip_ban', 'key_revoke', 'suspicious_activity', etc.
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    ip_address INET,
    user_agent TEXT,
    fingerprint_hash VARCHAR(255),
    key_id UUID REFERENCES license_keys(id),
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    handled_by TEXT, -- Admin username who handled the event
    handled_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'false_positive'))
);

-- Security settings configuration
CREATE TABLE IF NOT EXISTS security_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_by TEXT NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin security actions log
CREATE TABLE IF NOT EXISTS admin_security_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_username TEXT NOT NULL,
    action_type VARCHAR(100) NOT NULL, -- 'ip_ban', 'ip_unban', 'key_revoke', 'security_setting_change'
    target_identifier TEXT, -- IP, key code, etc.
    details JSONB DEFAULT '{}',
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET -- IP of admin performing action
);

-- Admin users table for authentication and roles
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'ml_security')),
    permissions JSONB DEFAULT '{}', -- Additional permissions for fine-grained access control
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- ========================================================================
-- INDEXES FOR PERFORMANCE
-- ========================================================================

-- Script system indexes
CREATE INDEX IF NOT EXISTS idx_scripts_category ON public.scripts(category);
CREATE INDEX IF NOT EXISTS idx_scripts_created_at ON public.scripts(created_at);
CREATE INDEX IF NOT EXISTS idx_script_ratings_script_id ON public.script_ratings(script_id);
CREATE INDEX IF NOT EXISTS idx_script_views_script_id ON public.script_views(script_id);

-- Key system indexes
CREATE INDEX IF NOT EXISTS idx_license_keys_key_code ON license_keys(key_code);
CREATE INDEX IF NOT EXISTS idx_license_keys_expires_at ON license_keys(expires_at);
CREATE INDEX IF NOT EXISTS idx_license_keys_is_active ON license_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_license_keys_session_id ON license_keys(session_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_fingerprint_hash ON license_keys(fingerprint_hash);
CREATE INDEX IF NOT EXISTS idx_license_keys_created_at ON license_keys(created_at);

CREATE INDEX IF NOT EXISTS idx_hwid_bindings_key_id ON hwid_bindings(key_id);
CREATE INDEX IF NOT EXISTS idx_hwid_bindings_hwid_hash ON hwid_bindings(hwid_hash);

CREATE INDEX IF NOT EXISTS idx_key_usage_logs_key_id ON key_usage_logs(key_id);
CREATE INDEX IF NOT EXISTS idx_key_usage_logs_created_at ON key_usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_key_usage_logs_action ON key_usage_logs(action);

CREATE INDEX IF NOT EXISTS idx_key_sessions_session_id ON key_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_key_sessions_expires_at ON key_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_used_tokens_token ON used_tokens(token);
CREATE INDEX IF NOT EXISTS idx_used_tokens_created_at ON used_tokens(created_at);

CREATE INDEX IF NOT EXISTS idx_behavior_profiles_key_id ON user_behavior_profiles(key_id);
CREATE INDEX IF NOT EXISTS idx_behavior_profiles_anomaly_score ON user_behavior_profiles(anomaly_score);

-- Security indexes
CREATE INDEX IF NOT EXISTS idx_ip_bans_ip_address ON ip_bans(ip_address);
CREATE INDEX IF NOT EXISTS idx_ip_bans_is_active ON ip_bans(is_active);
CREATE INDEX IF NOT EXISTS idx_ip_bans_expires_at ON ip_bans(expires_at);

CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_status ON security_events(status);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON security_events(ip_address);

CREATE INDEX IF NOT EXISTS idx_admin_security_actions_admin_username ON admin_security_actions(admin_username);
CREATE INDEX IF NOT EXISTS idx_admin_security_actions_action_type ON admin_security_actions(action_type);
CREATE INDEX IF NOT EXISTS idx_admin_security_actions_performed_at ON admin_security_actions(performed_at);

-- ========================================================================
-- ROW LEVEL SECURITY (RLS) SETUP
-- ========================================================================

-- Enable RLS for all tables
ALTER TABLE public.scripts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.script_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.script_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.script_update_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.script_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE lootlabs_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE license_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE hwid_bindings ENABLE ROW LEVEL SECURITY;
ALTER TABLE key_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE key_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE used_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_behavior_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE ip_bans ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_security_actions ENABLE ROW LEVEL SECURITY;

-- ========================================================================
-- RLS POLICIES - SCRIPT SYSTEM
-- ========================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for all users" ON public.scripts;
DROP POLICY IF EXISTS "Enable insert for admin users" ON public.scripts;
DROP POLICY IF EXISTS "Enable update for admin users" ON public.scripts;
DROP POLICY IF EXISTS "Enable delete for admin users" ON public.scripts;

CREATE POLICY "Enable read access for all users" ON public.scripts FOR SELECT USING (is_active = true);
CREATE POLICY "Enable insert for all" ON public.scripts FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all" ON public.scripts FOR UPDATE USING (true);
CREATE POLICY "Enable delete for all" ON public.scripts FOR DELETE USING (true);

-- Script ratings policies
DROP POLICY IF EXISTS "Enable read access for all users" ON public.script_ratings;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.script_ratings;
DROP POLICY IF EXISTS "Enable update for users" ON public.script_ratings;
DROP POLICY IF EXISTS "Enable delete for users" ON public.script_ratings;

CREATE POLICY "Enable read access for all users" ON public.script_ratings FOR SELECT USING (true);
CREATE POLICY "Enable insert for all" ON public.script_ratings FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all" ON public.script_ratings FOR UPDATE USING (true);
CREATE POLICY "Enable delete for all" ON public.script_ratings FOR DELETE USING (true);

-- Script views policies
DROP POLICY IF EXISTS "Enable read access for all users" ON public.script_views;
DROP POLICY IF EXISTS "Enable insert for all users" ON public.script_views;

CREATE POLICY "Enable read access for all users" ON public.script_views FOR SELECT USING (true);
CREATE POLICY "Enable insert for all" ON public.script_views FOR INSERT WITH CHECK (true);

-- Script update logs policies
DROP POLICY IF EXISTS "Enable read access for all users" ON public.script_update_logs;
DROP POLICY IF EXISTS "Enable insert for admin users" ON public.script_update_logs;
DROP POLICY IF EXISTS "Enable update for admin users" ON public.script_update_logs;
DROP POLICY IF EXISTS "Enable delete for admin users" ON public.script_update_logs;

CREATE POLICY "Enable read access for all users" ON public.script_update_logs FOR SELECT USING (true);
CREATE POLICY "Enable insert for all" ON public.script_update_logs FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all" ON public.script_update_logs FOR UPDATE USING (true);
CREATE POLICY "Enable delete for all" ON public.script_update_logs FOR DELETE USING (true);

-- Script requests policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON public.script_requests;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.script_requests;
DROP POLICY IF EXISTS "Enable update for admin users" ON public.script_requests;
DROP POLICY IF EXISTS "Enable delete for admin users" ON public.script_requests;

CREATE POLICY "Enable read access for all users" ON public.script_requests FOR SELECT USING (true);
CREATE POLICY "Enable insert for all" ON public.script_requests FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for all" ON public.script_requests FOR UPDATE USING (true);
CREATE POLICY "Enable delete for all" ON public.script_requests FOR DELETE USING (true);

-- ========================================================================
-- RLS POLICIES - KEY SYSTEM
-- ========================================================================

-- Campaign policies (public read, admin manage)
CREATE POLICY "Public can read active campaigns" ON lootlabs_campaigns
    FOR SELECT USING (is_active = true);
CREATE POLICY "System can manage campaigns" ON lootlabs_campaigns
    FOR ALL USING (true);

-- License keys policies
CREATE POLICY "Public can create keys" ON license_keys
    FOR INSERT WITH CHECK (true);
CREATE POLICY "Public can read own keys" ON license_keys
    FOR SELECT USING (true);
CREATE POLICY "System can manage all keys" ON license_keys
    FOR ALL USING (true);

-- HWID bindings policies
CREATE POLICY "Public can create hwid bindings" ON hwid_bindings
    FOR INSERT WITH CHECK (true);
CREATE POLICY "Public can read hwid bindings" ON hwid_bindings
    FOR SELECT USING (true);
CREATE POLICY "System can manage hwid bindings" ON hwid_bindings
    FOR ALL USING (true);

-- Usage logs policies
CREATE POLICY "System can manage usage logs" ON key_usage_logs
    FOR ALL USING (true);

-- Session policies
CREATE POLICY "System can manage sessions" ON key_sessions
    FOR ALL USING (true);

-- Token policies
CREATE POLICY "System can manage used tokens" ON used_tokens
    FOR ALL USING (true);

-- Behavior profiles policies
CREATE POLICY "System can create behavior profiles" ON user_behavior_profiles
    FOR INSERT WITH CHECK (true);
CREATE POLICY "System can read behavior profiles" ON user_behavior_profiles
    FOR SELECT USING (true);

-- ========================================================================
-- RLS POLICIES - SECURITY MANAGEMENT
-- ========================================================================

-- IP bans policies
CREATE POLICY "Admin can manage ip_bans" ON ip_bans FOR ALL USING (true);

-- Security events policies
CREATE POLICY "Admin can manage security_events" ON security_events FOR ALL USING (true);

-- Security settings policies
CREATE POLICY "Admin can manage security_settings" ON security_settings FOR ALL USING (true);

-- Admin security actions policies
CREATE POLICY "Admin can manage admin_security_actions" ON admin_security_actions FOR ALL USING (true);



-- ========================================================================
-- FUNCTIONS AND TRIGGERS
-- ========================================================================

-- Function to update 'updated_at' column automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at trigger to relevant tables
DROP TRIGGER IF EXISTS update_scripts_updated_at ON public.scripts;
CREATE TRIGGER update_scripts_updated_at
    BEFORE UPDATE ON public.scripts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lootlabs_campaigns_updated_at
    BEFORE UPDATE ON lootlabs_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update script rating when a new rating is added
CREATE OR REPLACE FUNCTION update_script_rating()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the script's average rating and rating count
    UPDATE public.scripts 
    SET 
        rating = (
            SELECT COALESCE(AVG(rating), 0)
            FROM public.script_ratings 
            WHERE script_id = COALESCE(NEW.script_id, OLD.script_id)
        ),
        rating_count = (
            SELECT COUNT(*)
            FROM public.script_ratings 
            WHERE script_id = COALESCE(NEW.script_id, OLD.script_id)
        )
    WHERE id = COALESCE(NEW.script_id, OLD.script_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for rating updates
DROP TRIGGER IF EXISTS update_rating_on_insert ON public.script_ratings;
DROP TRIGGER IF EXISTS update_rating_on_update ON public.script_ratings;
DROP TRIGGER IF EXISTS update_rating_on_delete ON public.script_ratings;

CREATE TRIGGER update_rating_on_insert
    AFTER INSERT ON public.script_ratings
    FOR EACH ROW
    EXECUTE FUNCTION update_script_rating();

CREATE TRIGGER update_rating_on_update
    AFTER UPDATE ON public.script_ratings
    FOR EACH ROW
    EXECUTE FUNCTION update_script_rating();

CREATE TRIGGER update_rating_on_delete
    AFTER DELETE ON public.script_ratings
    FOR EACH ROW
    EXECUTE FUNCTION update_script_rating();

-- Function to increment script views
CREATE OR REPLACE FUNCTION increment_script_views()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.scripts 
    SET views = views + 1
    WHERE id = NEW.script_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for view tracking
DROP TRIGGER IF EXISTS increment_views_on_insert ON public.script_views;
CREATE TRIGGER increment_views_on_insert
    AFTER INSERT ON public.script_views
    FOR EACH ROW
    EXECUTE FUNCTION increment_script_views();

-- Enhanced key generation function
DROP FUNCTION IF EXISTS public.generate_key_code();
DROP FUNCTION IF EXISTS generate_key_code();
CREATE OR REPLACE FUNCTION public.generate_key_code()
RETURNS VARCHAR(255) AS $$
DECLARE
    v_key_code VARCHAR(255);
    attempts INTEGER := 0;
    max_attempts INTEGER := 10;
BEGIN
    LOOP
        -- Generate MADARA-XXXX-XXXX-XXXX format
        v_key_code := 'MADARA-' || 
                   upper(substring(md5(random()::text) from 1 for 4)) || '-' ||
                   upper(substring(md5(random()::text) from 1 for 4)) || '-' ||
                   upper(substring(md5(random()::text) from 1 for 4));
        
        IF NOT EXISTS (SELECT 1 FROM public.license_keys WHERE public.license_keys.key_code = v_key_code) THEN
            RETURN v_key_code;
        END IF;
        
        attempts := attempts + 1;
        IF attempts >= max_attempts THEN
            RAISE EXCEPTION 'Failed to generate unique key code after % attempts', max_attempts;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired keys
CREATE OR REPLACE FUNCTION cleanup_expired_keys()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    UPDATE license_keys 
    SET is_active = false 
    WHERE expires_at < NOW() AND is_active = true;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old tokens (older than 1 hour)
CREATE OR REPLACE FUNCTION cleanup_old_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM used_tokens 
    WHERE created_at < NOW() - INTERVAL '1 hour';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old sessions (older than 1 hour)
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM key_sessions 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;









-- ========================================================================
-- INITIAL DATA
-- ========================================================================

-- ========================================================================
-- MIGRATION: LINKVERTISE TO LOOTLABS
-- ========================================================================

-- Drop old Linkvertise table if it exists (after backing up data if needed)
DROP TABLE IF EXISTS linkvertise_campaigns CASCADE;
DROP TABLE IF EXISTS linkvertise_sessions CASCADE;

-- Enhanced campaign configuration with new features (must be before INSERT)
ALTER TABLE lootlabs_campaigns ADD COLUMN IF NOT EXISTS thumbnail TEXT; -- Optional thumbnail URL
ALTER TABLE lootlabs_campaigns ADD COLUMN IF NOT EXISTS description TEXT; -- Campaign description
ALTER TABLE lootlabs_campaigns ADD COLUMN IF NOT EXISTS target_audience JSONB DEFAULT '{}'; -- Target audience configuration
ALTER TABLE lootlabs_campaigns ADD COLUMN IF NOT EXISTS performance_data JSONB DEFAULT '{}'; -- Performance metrics cache
ALTER TABLE lootlabs_campaigns ADD COLUMN IF NOT EXISTS advanced_config JSONB DEFAULT '{}'; -- Advanced configuration options

-- Dynamic campaign rules for intelligent selection
CREATE TABLE IF NOT EXISTS lootlabs_campaign_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES lootlabs_campaigns(id) ON DELETE CASCADE,
    rule_type VARCHAR(50) NOT NULL, -- 'location', 'device', 'time', 'user_history', etc.
    rule_config JSONB NOT NULL, -- Rule configuration (conditions, actions, etc.)
    priority INTEGER DEFAULT 0, -- Higher priority rules are evaluated first
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default Lootlabs campaign with enhanced features
INSERT INTO lootlabs_campaigns (name, tier_id, number_of_tasks, theme, return_url, description, target_audience, advanced_config)
VALUES (
    'Project Madara',
    3, -- Profit Maximization tier
    2, -- 2 tasks per session (optimized)
    3, -- Minecraft theme
    'https://checkingbefore.netlify.app/get-key?step=1&session=',
    'Enhanced Project Madara key generation with dynamic optimization and advanced security',
    '{"primary": "gaming", "age_range": "13-35", "interests": ["roblox", "gaming", "scripts"]}',
    '{"encryption_enabled": true, "dynamic_tier": true, "behavior_tracking": true, "security_level": "high"}'
)
ON CONFLICT DO NOTHING;

-- Insert default campaign rules for dynamic selection
INSERT INTO lootlabs_campaign_rules (campaign_id, rule_type, rule_config, priority)
SELECT
    id as campaign_id,
    'location_tier' as rule_type,
    '{"high_value_regions": ["US", "CA", "GB", "AU", "DE"], "preferred_tier": 3, "fallback_tier": 2}' as rule_config,
    100 as priority
FROM lootlabs_campaigns
WHERE name = 'Project Madara'
ON CONFLICT DO NOTHING;

INSERT INTO lootlabs_campaign_rules (campaign_id, rule_type, rule_config, priority)
SELECT
    id as campaign_id,
    'device_optimization' as rule_type,
    '{"mobile_tier": 2, "desktop_tier": 3, "tablet_tier": 2, "mobile_tasks": 1, "desktop_tasks": 2, "tablet_tasks": 1}' as rule_config,
    90 as priority
FROM lootlabs_campaigns
WHERE name = 'Project Madara'
ON CONFLICT DO NOTHING;

INSERT INTO lootlabs_campaign_rules (campaign_id, rule_type, rule_config, priority)
SELECT
    id as campaign_id,
    'security_based' as rule_type,
    '{"high_risk_tier": 1, "medium_risk_tier": 2, "low_risk_tier": 3, "risk_threshold": 0.7}' as rule_config,
    80 as priority
FROM lootlabs_campaigns
WHERE name = 'Project Madara'
ON CONFLICT DO NOTHING;

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - ANALYTICS TABLES
-- ========================================================================

-- Comprehensive analytics tracking for Lootlabs events
CREATE TABLE IF NOT EXISTS lootlabs_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL, -- 'session_started', 'link_created', 'link_click', 'step_completed', 'verification_attempt', 'error', etc.
    session_id TEXT, -- References key_sessions.session_id (TEXT type to match)
    campaign_id UUID REFERENCES lootlabs_campaigns(id),
    step INTEGER, -- 1 or 2, null for non-step events
    event_data JSONB DEFAULT '{}', -- Flexible data storage for event-specific information
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);



-- ========================================================================
-- LOOTLABS ENHANCEMENTS - SECURITY TABLES
-- ========================================================================

-- Session security tracking
CREATE TABLE IF NOT EXISTS lootlabs_session_security (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL, -- References key_sessions.session_id
    campaign_id UUID REFERENCES lootlabs_campaigns(id),
    risk_score DECIMAL(4,3) DEFAULT 0.000 CHECK (risk_score >= 0 AND risk_score <= 1), -- 0.000 to 1.000
    is_blocked BOOLEAN DEFAULT false,
    security_events JSONB DEFAULT '[]', -- Array of security events
    behavior_profile JSONB DEFAULT '{}', -- User behavior analysis data
    verification_attempts INTEGER DEFAULT 0,
    last_verification_score DECIMAL(4,3),
    ip_fingerprint VARCHAR(255),
    user_agent_hash VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security violations log
CREATE TABLE IF NOT EXISTS lootlabs_security_violations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID,
    campaign_id UUID REFERENCES lootlabs_campaigns(id),
    violation_type VARCHAR(100) NOT NULL, -- 'suspicious_timing', 'invalid_referrer', 'bot_behavior', etc.
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - CAMPAIGN FEATURES
-- ========================================================================

-- Campaign performance tracking
CREATE TABLE IF NOT EXISTS lootlabs_campaign_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES lootlabs_campaigns(id) ON DELETE CASCADE,
    date_period DATE NOT NULL, -- Daily aggregation
    location_code VARCHAR(10), -- Country code (US, CA, etc.)
    device_type VARCHAR(20), -- 'mobile', 'desktop', 'tablet'

    -- Metrics
    total_sessions INTEGER DEFAULT 0,
    link_clicks INTEGER DEFAULT 0,
    step1_completions INTEGER DEFAULT 0,
    step2_completions INTEGER DEFAULT 0,
    key_generations INTEGER DEFAULT 0,
    total_errors INTEGER DEFAULT 0,
    average_completion_time INTEGER, -- in milliseconds
    average_risk_score DECIMAL(4,3),

    -- Calculated fields
    conversion_rate DECIMAL(5,2), -- Percentage
    completion_rate DECIMAL(5,2), -- Percentage
    error_rate DECIMAL(5,2), -- Percentage

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Unique constraint to prevent duplicates
    UNIQUE(campaign_id, date_period, location_code, device_type)
);

-- Track encrypted URLs for security and analytics
CREATE TABLE IF NOT EXISTS lootlabs_encrypted_urls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL,
    campaign_id UUID REFERENCES lootlabs_campaigns(id),
    step INTEGER NOT NULL CHECK (step IN (1, 2)),
    original_url TEXT NOT NULL,
    encrypted_url TEXT NOT NULL,
    encryption_method VARCHAR(50) DEFAULT 'lootlabs_standard',
    password_protected BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE, -- Optional expiration
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE
);

-- Enhanced user behavior tracking (extends existing user_behavior_profiles)
CREATE TABLE IF NOT EXISTS lootlabs_user_behavior (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL,
    campaign_id UUID REFERENCES lootlabs_campaigns(id),

    -- Interaction metrics
    mouse_movements INTEGER DEFAULT 0,
    keyboard_events INTEGER DEFAULT 0,
    scroll_events INTEGER DEFAULT 0,
    focus_events INTEGER DEFAULT 0,
    click_events INTEGER DEFAULT 0,

    -- Timing metrics
    session_duration INTEGER, -- milliseconds
    step1_duration INTEGER, -- milliseconds
    step2_duration INTEGER, -- milliseconds
    idle_time INTEGER, -- milliseconds

    -- Behavior patterns
    interaction_pattern JSONB DEFAULT '{}', -- Detailed interaction analysis
    suspicious_indicators JSONB DEFAULT '[]', -- Array of suspicious behavior flags
    human_score DECIMAL(4,3) DEFAULT 0.500, -- 0.000 to 1.000, higher = more human-like

    -- Device/Environment
    screen_resolution VARCHAR(20),
    timezone VARCHAR(50),
    language VARCHAR(10),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - INDEXES
-- ========================================================================

-- Indexes for lootlabs_analytics
CREATE INDEX IF NOT EXISTS idx_lootlabs_analytics_event_type ON lootlabs_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_lootlabs_analytics_session_id ON lootlabs_analytics(session_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_analytics_campaign_id ON lootlabs_analytics(campaign_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_analytics_created_at ON lootlabs_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_lootlabs_analytics_step ON lootlabs_analytics(step);

-- Indexes for lootlabs_session_security
CREATE INDEX IF NOT EXISTS idx_lootlabs_session_security_session_id ON lootlabs_session_security(session_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_session_security_risk_score ON lootlabs_session_security(risk_score);
CREATE INDEX IF NOT EXISTS idx_lootlabs_session_security_is_blocked ON lootlabs_session_security(is_blocked);
CREATE INDEX IF NOT EXISTS idx_lootlabs_session_security_created_at ON lootlabs_session_security(created_at);

-- Indexes for lootlabs_security_violations
CREATE INDEX IF NOT EXISTS idx_lootlabs_security_violations_session_id ON lootlabs_security_violations(session_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_security_violations_type ON lootlabs_security_violations(violation_type);
CREATE INDEX IF NOT EXISTS idx_lootlabs_security_violations_severity ON lootlabs_security_violations(severity);
CREATE INDEX IF NOT EXISTS idx_lootlabs_security_violations_created_at ON lootlabs_security_violations(created_at);
CREATE INDEX IF NOT EXISTS idx_lootlabs_security_violations_ip ON lootlabs_security_violations(ip_address);

-- Indexes for lootlabs_campaign_rules
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_rules_campaign_id ON lootlabs_campaign_rules(campaign_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_rules_type ON lootlabs_campaign_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_rules_priority ON lootlabs_campaign_rules(priority);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_rules_active ON lootlabs_campaign_rules(is_active);

-- Indexes for lootlabs_campaign_performance
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_performance_campaign_id ON lootlabs_campaign_performance(campaign_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_performance_date ON lootlabs_campaign_performance(date_period);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_performance_location ON lootlabs_campaign_performance(location_code);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_performance_device ON lootlabs_campaign_performance(device_type);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_performance_conversion ON lootlabs_campaign_performance(conversion_rate);

-- Indexes for lootlabs_encrypted_urls
CREATE INDEX IF NOT EXISTS idx_lootlabs_encrypted_urls_session_id ON lootlabs_encrypted_urls(session_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_encrypted_urls_campaign_id ON lootlabs_encrypted_urls(campaign_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_encrypted_urls_step ON lootlabs_encrypted_urls(step);
CREATE INDEX IF NOT EXISTS idx_lootlabs_encrypted_urls_created_at ON lootlabs_encrypted_urls(created_at);

-- Indexes for lootlabs_user_behavior
CREATE INDEX IF NOT EXISTS idx_lootlabs_user_behavior_session_id ON lootlabs_user_behavior(session_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_user_behavior_campaign_id ON lootlabs_user_behavior(campaign_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_user_behavior_human_score ON lootlabs_user_behavior(human_score);
CREATE INDEX IF NOT EXISTS idx_lootlabs_user_behavior_created_at ON lootlabs_user_behavior(created_at);

-- Additional composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_lootlabs_analytics_session_event_time ON lootlabs_analytics(session_id, event_type, created_at);
CREATE INDEX IF NOT EXISTS idx_lootlabs_analytics_campaign_event_time ON lootlabs_analytics(campaign_id, event_type, created_at);
CREATE INDEX IF NOT EXISTS idx_lootlabs_session_security_risk_time ON lootlabs_session_security(risk_score DESC, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_lootlabs_security_violations_ip_time ON lootlabs_security_violations(ip_address, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_lootlabs_campaign_performance_metrics ON lootlabs_campaign_performance(campaign_id, date_period, conversion_rate DESC);
CREATE INDEX IF NOT EXISTS idx_lootlabs_user_behavior_score_time ON lootlabs_user_behavior(human_score DESC, created_at DESC);

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - ENABLE RLS
-- ========================================================================

-- Enable RLS for new Lootlabs tables
ALTER TABLE lootlabs_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE lootlabs_session_security ENABLE ROW LEVEL SECURITY;
ALTER TABLE lootlabs_security_violations ENABLE ROW LEVEL SECURITY;
ALTER TABLE lootlabs_campaign_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE lootlabs_campaign_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE lootlabs_encrypted_urls ENABLE ROW LEVEL SECURITY;
ALTER TABLE lootlabs_user_behavior ENABLE ROW LEVEL SECURITY;

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - RLS POLICIES
-- ========================================================================

-- Lootlabs Analytics - Allow read access for analytics, insert for tracking
CREATE POLICY "Allow analytics read access" ON lootlabs_analytics
    FOR SELECT USING (true);

CREATE POLICY "Allow analytics insert" ON lootlabs_analytics
    FOR INSERT WITH CHECK (true);

-- Session Security - Restrict access to session owners and admins
CREATE POLICY "Allow session security read" ON lootlabs_session_security
    FOR SELECT USING (true);

CREATE POLICY "Allow session security insert/update" ON lootlabs_session_security
    FOR ALL WITH CHECK (true);

-- Security Violations - Allow read for monitoring, insert for logging
CREATE POLICY "Allow security violations read" ON lootlabs_security_violations
    FOR SELECT USING (true);

CREATE POLICY "Allow security violations insert" ON lootlabs_security_violations
    FOR INSERT WITH CHECK (true);

-- Campaign Rules - Admin only access
CREATE POLICY "Allow campaign rules read" ON lootlabs_campaign_rules
    FOR SELECT USING (true);

CREATE POLICY "Allow campaign rules admin access" ON lootlabs_campaign_rules
    FOR ALL WITH CHECK (true);

-- Campaign Performance - Read access for analytics
CREATE POLICY "Allow campaign performance read" ON lootlabs_campaign_performance
    FOR SELECT USING (true);

CREATE POLICY "Allow campaign performance insert/update" ON lootlabs_campaign_performance
    FOR ALL WITH CHECK (true);

-- Encrypted URLs - Session-based access
CREATE POLICY "Allow encrypted urls read" ON lootlabs_encrypted_urls
    FOR SELECT USING (true);

CREATE POLICY "Allow encrypted urls insert" ON lootlabs_encrypted_urls
    FOR INSERT WITH CHECK (true);

-- User Behavior - Session-based access
CREATE POLICY "Allow user behavior read" ON lootlabs_user_behavior
    FOR SELECT USING (true);

CREATE POLICY "Allow user behavior insert" ON lootlabs_user_behavior
    FOR INSERT WITH CHECK (true);

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - VIEWS AND ANALYTICS
-- ========================================================================

-- Campaign performance summary view
CREATE OR REPLACE VIEW lootlabs_campaign_summary AS
SELECT
    c.id,
    c.name,
    c.tier_id,
    c.number_of_tasks,
    c.theme,
    c.is_active,
    COALESCE(p.total_sessions, 0) as total_sessions,
    COALESCE(p.total_conversions, 0) as total_conversions,
    COALESCE(p.avg_conversion_rate, 0) as avg_conversion_rate,
    COALESCE(p.total_errors, 0) as total_errors,
    c.created_at,
    c.updated_at
FROM lootlabs_campaigns c
LEFT JOIN (
    SELECT
        campaign_id,
        SUM(total_sessions) as total_sessions,
        SUM(key_generations) as total_conversions,
        AVG(conversion_rate) as avg_conversion_rate,
        SUM(total_errors) as total_errors
    FROM lootlabs_campaign_performance
    WHERE date_period >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY campaign_id
) p ON c.id = p.campaign_id;

-- Security violations summary view
CREATE OR REPLACE VIEW lootlabs_security_summary AS
SELECT
    violation_type,
    severity,
    COUNT(*) as violation_count,
    COUNT(DISTINCT session_id) as affected_sessions,
    COUNT(DISTINCT ip_address) as affected_ips,
    MIN(created_at) as first_occurrence,
    MAX(created_at) as last_occurrence
FROM lootlabs_security_violations
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY violation_type, severity
ORDER BY violation_count DESC;

-- User behavior insights view
CREATE OR REPLACE VIEW lootlabs_behavior_insights AS
SELECT
    DATE(created_at) as date,
    COUNT(*) as total_sessions,
    AVG(human_score) as avg_human_score,
    AVG(session_duration) as avg_session_duration,
    AVG(mouse_movements) as avg_mouse_movements,
    AVG(keyboard_events) as avg_keyboard_events,
    COUNT(CASE WHEN human_score < 0.3 THEN 1 END) as suspicious_sessions,
    COUNT(CASE WHEN human_score >= 0.8 THEN 1 END) as high_confidence_sessions
FROM lootlabs_user_behavior
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Function to get campaign analytics for a date range
CREATE OR REPLACE FUNCTION get_campaign_analytics(
    p_campaign_id UUID DEFAULT NULL,
    p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '7 days',
    p_end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    campaign_id UUID,
    campaign_name TEXT,
    total_sessions BIGINT,
    link_clicks BIGINT,
    step1_completions BIGINT,
    step2_completions BIGINT,
    key_generations BIGINT,
    conversion_rate DECIMAL,
    avg_completion_time DECIMAL,
    total_errors BIGINT,
    error_rate DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id as campaign_id,
        c.name as campaign_name,
        COALESCE(SUM(cp.total_sessions), 0) as total_sessions,
        COALESCE(SUM(cp.link_clicks), 0) as link_clicks,
        COALESCE(SUM(cp.step1_completions), 0) as step1_completions,
        COALESCE(SUM(cp.step2_completions), 0) as step2_completions,
        COALESCE(SUM(cp.key_generations), 0) as key_generations,
        COALESCE(AVG(cp.conversion_rate), 0) as conversion_rate,
        COALESCE(AVG(cp.average_completion_time), 0) as avg_completion_time,
        COALESCE(SUM(cp.total_errors), 0) as total_errors,
        COALESCE(AVG(cp.error_rate), 0) as error_rate
    FROM lootlabs_campaigns c
    LEFT JOIN lootlabs_campaign_performance cp ON c.id = cp.campaign_id
        AND cp.date_period BETWEEN p_start_date AND p_end_date
    WHERE (p_campaign_id IS NULL OR c.id = p_campaign_id)
        AND c.is_active = true
    GROUP BY c.id, c.name
    ORDER BY total_sessions DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get security violations summary
CREATE OR REPLACE FUNCTION get_security_violations_summary(
    p_start_date TIMESTAMP DEFAULT NOW() - INTERVAL '24 hours',
    p_end_date TIMESTAMP DEFAULT NOW()
)
RETURNS TABLE (
    violation_type VARCHAR,
    severity VARCHAR,
    violation_count BIGINT,
    affected_sessions BIGINT,
    affected_ips BIGINT,
    latest_occurrence TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        sv.violation_type,
        sv.severity,
        COUNT(*) as violation_count,
        COUNT(DISTINCT sv.session_id) as affected_sessions,
        COUNT(DISTINCT sv.ip_address) as affected_ips,
        MAX(sv.created_at) as latest_occurrence
    FROM lootlabs_security_violations sv
    WHERE sv.created_at BETWEEN p_start_date AND p_end_date
    GROUP BY sv.violation_type, sv.severity
    ORDER BY violation_count DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup old analytics data
CREATE OR REPLACE FUNCTION cleanup_old_analytics_data(
    p_retention_days INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete old analytics data
    DELETE FROM lootlabs_analytics
    WHERE created_at < NOW() - (p_retention_days || ' days')::INTERVAL;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- Delete old security violations
    DELETE FROM lootlabs_security_violations
    WHERE created_at < NOW() - (p_retention_days || ' days')::INTERVAL;

    -- Delete old user behavior data
    DELETE FROM lootlabs_user_behavior
    WHERE created_at < NOW() - (p_retention_days || ' days')::INTERVAL;

    -- Delete old encrypted URLs
    DELETE FROM lootlabs_encrypted_urls
    WHERE created_at < NOW() - (p_retention_days || ' days')::INTERVAL;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - FUNCTIONS AND TRIGGERS
-- ========================================================================

-- Function to calculate risk score
CREATE OR REPLACE FUNCTION calculate_session_risk_score(p_session_id UUID)
RETURNS DECIMAL(4,3) AS $$
DECLARE
    risk_score DECIMAL(4,3) := 0.000;
    violation_count INTEGER;
    behavior_score DECIMAL(4,3);
BEGIN
    -- Count security violations
    SELECT COUNT(*) INTO violation_count
    FROM lootlabs_security_violations
    WHERE session_id = p_session_id;

    -- Add risk based on violations
    risk_score := risk_score + (violation_count * 0.100);

    -- Get behavior score
    SELECT COALESCE(1.000 - human_score, 0.500) INTO behavior_score
    FROM lootlabs_user_behavior
    WHERE session_id = p_session_id
    ORDER BY created_at DESC
    LIMIT 1;

    risk_score := risk_score + behavior_score;

    -- Cap at 1.000
    RETURN LEAST(risk_score, 1.000);
END;
$$ LANGUAGE plpgsql;

-- Trigger to update session security risk score
CREATE OR REPLACE FUNCTION update_session_risk_score()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE lootlabs_session_security
    SET
        risk_score = calculate_session_risk_score(NEW.session_id),
        updated_at = NOW()
    WHERE session_id = NEW.session_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_update_risk_score_on_violation ON lootlabs_security_violations;
CREATE TRIGGER trigger_update_risk_score_on_violation
    AFTER INSERT ON lootlabs_security_violations
    FOR EACH ROW EXECUTE FUNCTION update_session_risk_score();

DROP TRIGGER IF EXISTS trigger_update_risk_score_on_behavior ON lootlabs_user_behavior;
CREATE TRIGGER trigger_update_risk_score_on_behavior
    AFTER INSERT OR UPDATE ON lootlabs_user_behavior
    FOR EACH ROW EXECUTE FUNCTION update_session_risk_score();

-- Add update triggers for new tables
CREATE TRIGGER update_lootlabs_session_security_updated_at
    BEFORE UPDATE ON lootlabs_session_security
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lootlabs_campaign_rules_updated_at
    BEFORE UPDATE ON lootlabs_campaign_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_lootlabs_campaign_performance_updated_at
    BEFORE UPDATE ON lootlabs_campaign_performance
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================================================
-- INITIAL SECURITY SETTINGS
-- ========================================================================

INSERT INTO security_settings (setting_key, setting_value, description, updated_by) VALUES
('max_failed_attempts', '5', 'Maximum failed key validation attempts before IP ban', 'system'),
('ban_duration_hours', '24', 'Duration of IP ban in hours (0 for permanent)', 'system'),
('enable_behavior_analysis', 'true', 'Enable behavioral analysis for bot detection', 'system'),
('enable_vm_detection', 'true', 'Enable virtual machine detection', 'system'),
('enable_console_detection', 'false', 'Enable console access detection (relaxed for dev)', 'system'),
('suspicious_activity_threshold', '0.8', 'Threshold for suspicious activity detection (0-1)', 'system'),
('auto_ban_suspicious', 'false', 'Automatically ban IPs for suspicious activity', 'system'),
('log_all_events', 'true', 'Log all security events for monitoring', 'system'),
('lootlabs_encryption_enabled', 'true', 'Enable Lootlabs URL encryption for enhanced security', 'system'),
('lootlabs_dynamic_tier_enabled', 'true', 'Enable dynamic tier selection based on user characteristics', 'system'),
('lootlabs_behavior_tracking_enabled', 'true', 'Enable enhanced behavior tracking for fraud detection', 'system'),
('lootlabs_risk_threshold', '0.7', 'Risk score threshold for blocking sessions (0.0-1.0)', 'system'),
('lootlabs_analytics_retention_days', '90', 'Number of days to retain Lootlabs analytics data', 'system'),
('lootlabs_max_retry_attempts', '3', 'Maximum retry attempts for Lootlabs operations', 'system'),
('lootlabs_verification_timeout', '600', 'Timeout for Lootlabs verification in seconds (10 minutes)', 'system')
ON CONFLICT (setting_key) DO NOTHING;

-- ========================================================================
-- SECURITY FUNCTIONS
-- ========================================================================

-- Function to check if IP is banned for specific violation type
CREATE OR REPLACE FUNCTION is_ip_banned(ip INET, violation_type_param TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM ip_bans
        WHERE ip_address = ip
        AND is_active = true
        AND expires_at > NOW()
        AND (violation_type_param IS NULL OR violation_type = violation_type_param)
    );
END;
$$ LANGUAGE plpgsql;

-- Function to apply temporary ban with escalation
CREATE OR REPLACE FUNCTION apply_temporary_ban(
    ip INET,
    violation_type_param TEXT,
    base_duration_minutes INTEGER DEFAULT 60,
    banned_by_param TEXT DEFAULT 'system'
)
RETURNS TABLE(ban_id UUID, expires_at_result TIMESTAMP WITH TIME ZONE, violation_count_result INTEGER) AS $$
DECLARE
    existing_count INTEGER := 0;
    final_duration INTEGER;
    ban_expires_at TIMESTAMP WITH TIME ZONE;
    new_ban_id UUID;
BEGIN
    -- Check for existing violations of this type in the last 24 hours
    SELECT COALESCE(MAX(violation_count), 0) INTO existing_count
    FROM ip_bans
    WHERE ip_address = ip
    AND violation_type = violation_type_param
    AND banned_at > NOW() - INTERVAL '24 hours';

    -- Calculate escalated duration (double for each repeat offense, max 24 hours)
    final_duration := LEAST(base_duration_minutes * POWER(2, existing_count), 1440);
    ban_expires_at := NOW() + (final_duration || ' minutes')::INTERVAL;

    -- Deactivate any existing bans of this type
    UPDATE ip_bans
    SET is_active = false
    WHERE ip_address = ip
    AND violation_type = violation_type_param
    AND is_active = true;

    -- Insert new ban
    INSERT INTO ip_bans (
        ip_address,
        violation_type,
        reason,
        ban_duration_minutes,
        banned_by,
        expires_at,
        violation_count
    ) VALUES (
        ip,
        violation_type_param,
        'Automatic temporary ban for ' || violation_type_param,
        final_duration,
        banned_by_param,
        ban_expires_at,
        existing_count + 1
    ) RETURNING id INTO new_ban_id;

    RETURN QUERY SELECT new_ban_id, ban_expires_at, existing_count + 1;
END;
$$ LANGUAGE plpgsql;

-- Function to log security event
CREATE OR REPLACE FUNCTION log_security_event(
    p_event_type VARCHAR(100),
    p_severity VARCHAR(20),
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_fingerprint_hash VARCHAR(255) DEFAULT NULL,
    p_key_id UUID DEFAULT NULL,
    p_details JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO security_events (
        event_type, severity, ip_address, user_agent, 
        fingerprint_hash, key_id, details
    ) VALUES (
        p_event_type, p_severity, p_ip_address, p_user_agent,
        p_fingerprint_hash, p_key_id, p_details
    ) RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql;

-- Function to log admin security action
CREATE OR REPLACE FUNCTION log_admin_security_action(
    p_admin_username TEXT,
    p_action_type VARCHAR(100),
    p_target_identifier TEXT DEFAULT NULL,
    p_details JSONB DEFAULT '{}',
    p_admin_ip INET DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    action_id UUID;
BEGIN
    INSERT INTO admin_security_actions (
        admin_username, action_type, target_identifier, details, ip_address
    ) VALUES (
        p_admin_username, p_action_type, p_target_identifier, p_details, p_admin_ip
    ) RETURNING id INTO action_id;
    
    RETURN action_id;
END;
$$ LANGUAGE plpgsql;

-- Insert default admin users with enhanced roles
INSERT INTO admin_users (username, password_hash, role, permissions) VALUES
('Sabin07', 'Sabin8705++', 'owner', '{"all": true}')
ON CONFLICT (username) DO NOTHING;

-- ========================================================================
-- LOOTLABS ENHANCEMENTS - DOCUMENTATION
-- ========================================================================

-- Table Comments for Documentation
COMMENT ON TABLE lootlabs_analytics IS 'Comprehensive event tracking for all Lootlabs interactions and user behavior';
COMMENT ON TABLE lootlabs_session_security IS 'Security monitoring and risk assessment for user sessions with fraud detection';
COMMENT ON TABLE lootlabs_security_violations IS 'Log of security violations and suspicious activities for monitoring';
COMMENT ON TABLE lootlabs_campaign_rules IS 'Dynamic rules for intelligent campaign selection based on user characteristics';
COMMENT ON TABLE lootlabs_campaign_performance IS 'Aggregated performance metrics for campaigns with conversion tracking';
COMMENT ON TABLE lootlabs_encrypted_urls IS 'Tracking of encrypted URLs for security and analytics purposes';
COMMENT ON TABLE lootlabs_user_behavior IS 'Detailed user behavior analysis for fraud detection and optimization';

-- View Comments
COMMENT ON VIEW lootlabs_campaign_summary IS 'Campaign performance overview with key metrics and conversion rates';
COMMENT ON VIEW lootlabs_security_summary IS 'Security violations summary for monitoring and alerting';
COMMENT ON VIEW lootlabs_behavior_insights IS 'User behavior analytics for fraud detection and user experience optimization';

-- Function Comments
COMMENT ON FUNCTION get_campaign_analytics IS 'Retrieve detailed campaign analytics for specified date range';
COMMENT ON FUNCTION get_security_violations_summary IS 'Get security violations summary for monitoring dashboard';
COMMENT ON FUNCTION cleanup_old_analytics_data IS 'Cleanup old analytics data based on retention policy';
COMMENT ON FUNCTION calculate_session_risk_score IS 'Calculate dynamic risk score for session security assessment';

-- ========================================================================
-- LOOTLABS ENHANCEMENTS SUMMARY
-- ========================================================================
--
-- This enhanced database now supports:
--
-- 1. COMPREHENSIVE ANALYTICS:
--    - Real-time event tracking for all Lootlabs interactions
--    - Performance metrics and conversion funnel analysis
--    - User behavior insights and fraud detection
--    - Campaign performance comparison and optimization
--
-- 2. ADVANCED SECURITY:
--    - Multi-layered security with risk scoring (0.0-1.0)
--    - Behavioral analysis and fraud prevention
--    - Session blocking and violation tracking
--    - Enhanced verification with multiple validation methods
--
-- 3. DYNAMIC CAMPAIGN OPTIMIZATION:
--    - Rule-based campaign selection
--    - Location and device-based optimization
--    - Performance-driven routing
--    - A/B testing support
--
-- 4. URL ENCRYPTION:
--    - Lootlabs URL encryption support
--    - Password protection capabilities
--    - Access tracking and monitoring
--
-- 5. ENHANCED USER EXPERIENCE:
--    - Intelligent retry mechanisms
--    - Optimized completion flows
--    - Real-time performance monitoring
--    - Dynamic tier selection for better conversion
--
-- New Tables Added:
-- - lootlabs_analytics: Event tracking and analytics
-- - lootlabs_session_security: Security monitoring
-- - lootlabs_security_violations: Violation logging
-- - lootlabs_campaign_rules: Dynamic campaign rules
-- - lootlabs_campaign_performance: Performance metrics
-- - lootlabs_encrypted_urls: URL encryption tracking
-- - lootlabs_user_behavior: Behavior analysis
--
-- New Views Added:
-- - lootlabs_campaign_summary: Campaign overview
-- - lootlabs_security_summary: Security monitoring
-- - lootlabs_behavior_insights: Behavior analytics
--
-- New Functions Added:
-- - get_campaign_analytics(): Campaign analytics
-- - get_security_violations_summary(): Security monitoring
-- - cleanup_old_analytics_data(): Data maintenance
-- - calculate_session_risk_score(): Risk assessment
--
-- The system is now ready for production use with enterprise-grade
-- security, comprehensive analytics, and intelligent optimization.
--
-- ========================================================================
-- ENHANCED SESSION MANAGEMENT TABLES
-- ========================================================================

-- Enhanced session management with IP binding and 24-hour expiration
CREATE TABLE IF NOT EXISTS secure_sessions (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID UNIQUE NOT NULL,
    session_token VARCHAR(64) UNIQUE NOT NULL,
    ip_address INET NOT NULL,
    ip_hash VARCHAR(64) NOT NULL,
    user_agent TEXT,
    device_fingerprint JSONB,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    invalidated_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    security_level VARCHAR(20) DEFAULT 'medium',
    access_count INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS session_security_events (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID REFERENCES secure_sessions(session_id),
    violation_type VARCHAR(50) NOT NULL,
    violation_details JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for enhanced session management
CREATE INDEX IF NOT EXISTS idx_secure_sessions_token ON secure_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_ip ON secure_sessions(ip_address);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_expires ON secure_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_active ON secure_sessions(is_active, expires_at);
CREATE INDEX IF NOT EXISTS idx_session_events_session ON session_security_events(session_id);
CREATE INDEX IF NOT EXISTS idx_session_events_type ON session_security_events(violation_type);
CREATE INDEX IF NOT EXISTS idx_session_events_timestamp ON session_security_events(timestamp);

-- Enable RLS for session tables
ALTER TABLE secure_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE session_security_events ENABLE ROW LEVEL SECURITY;

-- RLS policies for session management
CREATE POLICY "System can manage secure sessions" ON secure_sessions FOR ALL USING (true);
CREATE POLICY "System can manage session events" ON session_security_events FOR ALL USING (true);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS rate_limit_entries (
    id BIGSERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    endpoint VARCHAR(100) NOT NULL,
    user_agent TEXT,
    additional_data JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for rate limiting
CREATE INDEX IF NOT EXISTS idx_rate_limit_ip_endpoint ON rate_limit_entries(ip_address, endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limit_timestamp ON rate_limit_entries(timestamp);
CREATE INDEX IF NOT EXISTS idx_rate_limit_ip_timestamp ON rate_limit_entries(ip_address, timestamp);

-- Enable RLS for rate limiting
ALTER TABLE rate_limit_entries ENABLE ROW LEVEL SECURITY;
CREATE POLICY "System can manage rate limit entries" ON rate_limit_entries FOR ALL USING (true);

-- Lootlabs verification tokens table
CREATE TABLE IF NOT EXISTS lootlabs_tokens (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID NOT NULL,
    step INTEGER NOT NULL,
    token VARCHAR(64) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_used BOOLEAN DEFAULT false,
    used_at TIMESTAMP WITH TIME ZONE,
    verification_data JSONB,
    ip_address INET,
    user_agent TEXT
);

-- Indexes for Lootlabs tokens
CREATE INDEX IF NOT EXISTS idx_lootlabs_tokens_session ON lootlabs_tokens(session_id);
CREATE INDEX IF NOT EXISTS idx_lootlabs_tokens_token ON lootlabs_tokens(token);
CREATE INDEX IF NOT EXISTS idx_lootlabs_tokens_step ON lootlabs_tokens(session_id, step);
CREATE INDEX IF NOT EXISTS idx_lootlabs_tokens_expires ON lootlabs_tokens(expires_at);

-- Enable RLS for Lootlabs tokens
ALTER TABLE lootlabs_tokens ENABLE ROW LEVEL SECURITY;
CREATE POLICY "System can manage lootlabs tokens" ON lootlabs_tokens FOR ALL USING (true);

-- Simple IP-based user tracking (no emails or accounts)
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGSERIAL PRIMARY KEY,
    ip_address INET NOT NULL,
    user_agent_hash VARCHAR(255) NOT NULL,
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_keys_generated INTEGER DEFAULT 0,
    UNIQUE(ip_address, user_agent_hash)
);

-- Add HWID reset tracking to existing license_keys table
ALTER TABLE license_keys ADD COLUMN IF NOT EXISTS last_hwid_reset TIMESTAMP WITH TIME ZONE;



-- Download Tokens for secure key access
CREATE TABLE IF NOT EXISTS download_tokens (
    id BIGSERIAL PRIMARY KEY,
    token VARCHAR(255) UNIQUE NOT NULL,
    key_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT
);

-- Simple revenue tracking (IP-based)
CREATE TABLE IF NOT EXISTS revenue_events (
    id BIGSERIAL PRIMARY KEY,
    ip_address INET,
    revenue_source VARCHAR(100) NOT NULL, -- lootlabs, other
    amount DECIMAL(10,4) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Indexes for simplified tables
CREATE INDEX IF NOT EXISTS idx_user_sessions_ip ON user_sessions(ip_address);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_seen ON user_sessions(last_seen);
CREATE INDEX IF NOT EXISTS idx_license_keys_hwid_reset ON license_keys(last_hwid_reset);
CREATE INDEX IF NOT EXISTS idx_download_tokens_token ON download_tokens(token);
CREATE INDEX IF NOT EXISTS idx_download_tokens_key_id ON download_tokens(key_id);
CREATE INDEX IF NOT EXISTS idx_revenue_events_ip ON revenue_events(ip_address);

-- ========================================================================
-- OBFUSCATION SYSTEM TABLES
-- ========================================================================

-- Obfuscation logs table
CREATE TABLE IF NOT EXISTS obfuscation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    script_hash VARCHAR(32),
    admin_username VARCHAR(255),
    original_size INTEGER,
    obfuscated_size INTEGER,
    processing_time INTEGER, -- in milliseconds
    features_applied JSONB DEFAULT '[]',
    hwid VARCHAR(255),
    complexity VARCHAR(20),
    line_count INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for obfuscation logs
CREATE INDEX IF NOT EXISTS idx_obfuscation_logs_admin ON obfuscation_logs(admin_username);
CREATE INDEX IF NOT EXISTS idx_obfuscation_logs_created_at ON obfuscation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_obfuscation_logs_success ON obfuscation_logs(success);
CREATE INDEX IF NOT EXISTS idx_obfuscation_logs_hwid ON obfuscation_logs(hwid);

-- Enable RLS for simplified tables
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE download_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE revenue_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE obfuscation_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies (allowing system access)
CREATE POLICY "System can manage user sessions" ON user_sessions FOR ALL USING (true);
CREATE POLICY "System can manage download tokens" ON download_tokens FOR ALL USING (true);
CREATE POLICY "System can manage revenue events" ON revenue_events FOR ALL USING (true);

-- Update security settings with new session-related settings
INSERT INTO security_settings (setting_key, setting_value, description, updated_by) VALUES
('session_duration_hours', '24', 'Session duration in hours', 'system'),
('enable_ip_binding', 'true', 'Enable IP address binding for sessions', 'system'),
('max_sessions_per_ip', '5', 'Maximum concurrent sessions per IP address', 'system'),
('rate_limit_api_general', '100', 'General API rate limit per minute', 'system'),
('rate_limit_key_generation', '10', 'Key generation rate limit per minute', 'system'),
('rate_limit_step_completion', '10', 'Step completion rate limit per minute', 'system'),
('captcha_trigger_threshold', '20', 'Requests per minute to trigger CAPTCHA', 'system'),
('enable_captcha_protection', 'true', 'Enable CAPTCHA protection for suspicious behavior', 'system'),
('lootlabs_token_expiry_minutes', '30', 'Lootlabs verification token expiry in minutes', 'system'),
('enable_lootlabs_verification', 'true', 'Enable server-side Lootlabs verification', 'system')
ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- ========================================================================
-- DATABASE SETUP COMPLETE WITH ENHANCED SESSION MANAGEMENT
-- ========================================================================

-- Add new columns for enhanced security
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS fingerprint VARCHAR(32);
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS rotation_count INTEGER DEFAULT 0;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS rotated_at TIMESTAMP;

-- Create security events table
CREATE TABLE IF NOT EXISTS security_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(50) NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create security alerts table
CREATE TABLE IF NOT EXISTS security_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type VARCHAR(50) NOT NULL,
  severity VARCHAR(20) NOT NULL,
  message TEXT NOT NULL,
  details JSONB,
  triggered_at TIMESTAMP DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'active'
);