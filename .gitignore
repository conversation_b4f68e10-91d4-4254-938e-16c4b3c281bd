# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules

# Build output
dist
dist-ssr

# Environment files (never commit these!)
.env
.env.local
.env.*.local
.env.production
.env.production.*
!.env.example  # Only this one is safe to commit

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local development
*.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store

# Testing
coverage

# Production
build

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.env.development.local
.env.test.local
.env.production.local

# Production server environment (keep this secure!)
.env.production.server

# Local Netlify folder
.netlify
