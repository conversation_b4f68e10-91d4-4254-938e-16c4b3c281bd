# =============================================================================
# ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env files to version control

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=production
VITE_APP_NAME="Key System"
VITE_APP_VERSION="1.0.0"

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# API Key Configuration
API_KEY_HEADER=x-api-key
ADMIN_API_KEYS=your-admin-api-key-1,your-admin-api-key-2
KEY_PREFIX=PL_
KEY_SALT=ca016941e3effcef81c06e98ae30a7751ec54576afad5288c964b40a695c0034
HWID_SALT=406b5c6e8b2dc238718ed0666d67184ed6a3cf3f6c7c48a69c2c6db2d86f88ec

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=24h

# Session Configuration
SESSION_SECRET=your-session-secret-here
SESSION_TIMEOUT=3600000

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# General API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Authentication Rate Limiting
AUTH_RATE_LIMIT_WINDOW_MS=3600000
AUTH_RATE_LIMIT_MAX_REQUESTS=10

# Key Generation Rate Limiting
KEY_GEN_RATE_LIMIT_WINDOW_MS=60000
KEY_GEN_RATE_LIMIT_MAX_REQUESTS=6

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGIN=https://projectmadara.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,x-api-key,x-client-hwid
CORS_CREDENTIALS=true

# =============================================================================
# RECAPTCHA CONFIGURATION
# =============================================================================
VITE_RECAPTCHA_SITE_KEY=your-recaptcha-site-key
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# =============================================================================
# REDIS CONFIGURATION (Optional - for production rate limiting)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
SENTRY_DSN=your-sentry-dsn-here
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_CLIENT_PROTECTION=true
ENABLE_DEVICE_FINGERPRINTING=true
ENABLE_RATE_LIMITING=true
ENABLE_AUDIT_LOGGING=true
ENABLE_EMAIL_NOTIFICATIONS=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Only used in development
DEV_BYPASS_RECAPTCHA=false
DEV_BYPASS_RATE_LIMITING=false
DEV_MOCK_HWID=false
