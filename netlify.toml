[build]
  command = "npm run build"
  publish = "dist"
  functions = "netlify/functions"

[build.environment]
  # Environment variables are set in Netlify UI, not here
  # This section is for build-time environment overrides
  
  # JWT
  JWT_EXPIRES_IN = "7d"
  
  # Rate limiting
  RATE_LIMIT_WINDOW_MS = "900000"  # 15 minutes
  RATE_LIMIT_MAX_REQUESTS = "100"

# Function-specific configurations
[functions]
  node_bundler = "esbuild"
  external_node_modules = ["@supabase/supabase-js", "joi", "jsonwebtoken"]
  included_files = [".env.*"]

# Redirects
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' https://fonts.gstatic.com; connect-src 'self' https://*.supabase.co; frame-src 'self' https://*.supabase.co;"

# API rate limiting
[[headers]]
  for = "/api/*"
  [headers.values]
    X-RateLimit-Limit = "100"
    X-RateLimit-Remaining = "99"
    X-RateLimit-Reset = "900"



# Development environment overrides
[context.development.environment]
  NODE_ENV = "development"
  VITE_APP_ENV = "development"

# Preview environment overrides (for PR previews)
[context.deploy-preview.environment]
  NODE_ENV = "preview"
  VITE_APP_ENV = "preview"

# Make sure to set these in your Netlify site settings:
# - SUPABASE_URL: Your Supabase project URL
# - SUPABASE_ANON_KEY: Your Supabase anon/public key
# - SUPABASE_SERVICE_ROLE_KEY: Your Supabase service role key (keep this secret!)
# - JWT_SECRET: A secure random string for JWT signing
# - API_KEY_SALT: A secure random string for hashing API keys