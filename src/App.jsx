import { BrowserRouter as Router, Routes, Route, useLocation, Navigate, Outlet } from 'react-router-dom';
import { Toaster } from 'sonner';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect } from 'react';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import { SecurityProvider } from './context/SecurityContext';
import { AuthProvider, useAuth } from './context/AuthContext';
import { ScriptRequestProvider } from './context/ScriptRequestContext';
import MainLayout from './components/layout/MainLayout';
import LoginPage from './pages/LoginPage';
import Home from './pages/Home';
import NotFound from './pages/NotFound';
import Scripts from './pages/Scripts';
import ScriptDetail from './pages/ScriptDetail';
import Projects from './pages/Projects';
import Contact from './pages/Contact';
import ScriptRequest from './pages/ScriptRequest';
import RequestStatus from './pages/RequestStatus';
import FAQ from './pages/FAQ';
import Terms from './pages/Terms';
import Privacy from './pages/Privacy';
import CookiePolicy from './pages/CookiePolicy';
import AdminRoutes from './pages/admin';
import KeySystemRoutes from './pages/keysystem';
import { cn } from './lib/utils';

// Add theme class to body for better theme support
const ThemeWrapper = ({ children }) => {
  const { resolvedTheme } = useTheme();
  
  useEffect(() => {
    document.body.className = resolvedTheme === 'dark' ? 'dark' : '';
  }, [resolvedTheme]);
  
  return children;
};

// Protected route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  // If still loading auth state, show loading indicator
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If not authenticated, redirect to login with return URL
  if (!isAuthenticated || !isAuthenticated()) {
    console.log('Not authenticated, redirecting to login');
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  // If authenticated, render the protected content
  return children;
};

// Layout for public routes
const PublicLayout = () => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  // If user is authenticated and tries to access login page, redirect to home
  if (location.pathname === '/login' && isAuthenticated && isAuthenticated()) {
    return <Navigate to="/admin" replace />;
  }

  return (
    <MainLayout>
      <Outlet />
    </MainLayout>
  );
};

// Main app content
const AppContent = () => {
  const location = useLocation();

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <Routes location={location} key={location.pathname}>
        {/* Public routes with layout */}
        <Route element={<PublicLayout />}>
          {/* Main Navigation */}
          <Route path="/" element={<Home />} />
          <Route path="/scripts" element={<Scripts />} />
          <Route path="/scripts/:id" element={<ScriptDetail />} />
          <Route path="/projects" element={<Projects />} />
          <Route path="/contact" element={<Contact />} />
          
          {/* Request System */}
          <Route path="/request-script" element={<ScriptRequest />} />
          <Route path="/request-status" element={<RequestStatus />} />
          
          {/* Resources */}
          <Route path="/faq" element={<FAQ />} />
          
          {/* Legal */}
          <Route path="/terms" element={<Terms />} />
          <Route path="/privacy" element={<Privacy />} />
          <Route path="/cookies" element={<CookiePolicy />} />
          
          {/* Auth */}
          <Route path="/login" element={<LoginPage />} />
          
          {/* Test Page */}
          
          {/* Catch-all for undefined public routes */}
          <Route path="*" element={<NotFound />} />
        </Route>
        
        {/* Admin routes with protection */}
        <Route 
          path="/admin/*" 
          element={
            <ProtectedRoute>
              <AdminRoutes />
            </ProtectedRoute>
          } 
        />
        
        <Route 
          path="/keysystem/*" 
          element={<KeySystemRoutes />} 
        />
      </Routes>
      
      <Toaster 
        position="top-right" 
        toastOptions={{
          className: '!bg-background !text-foreground !border !border-border',
        }}
      />
    </div>
  );
};

// Main App component with providers
const App = () => {
  return (
    <Router>
      <ThemeProvider>
        <ThemeWrapper>
          <AuthProvider>
            <SecurityProvider>
              <ScriptRequestProvider>
                <AppContent />
              </ScriptRequestProvider>
            </SecurityProvider>
          </AuthProvider>
        </ThemeWrapper>
      </ThemeProvider>
    </Router>
  );
};

export default App;
