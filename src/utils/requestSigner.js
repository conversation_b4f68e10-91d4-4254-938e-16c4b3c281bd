import { createHmac } from 'crypto';

/**
 * Signs a request with HMAC-SHA256
 * @param {string} secret - The secret key for signing
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {string} path - Request path
 * @param {Object} body - Request body (will be stringified)
 * @param {number} timestamp - Request timestamp
 * @param {Object} query - Query parameters
 * @returns {string} The signature
 */
export const signRequest = (secret, method, path, body = {}, timestamp, query = {}) => {
  try {
    // Sort query parameters alphabetically
    const sortedQuery = Object.keys(query)
      .sort()
      .reduce((acc, key) => ({
        ...acc,
        [key]: query[key]
      }), {});

    // Create a canonical string representation
    const stringToSign = [
      method.toUpperCase(),
      path,
      typeof body === 'object' ? JSON.stringify(sortObjectKeys(body)) : String(body || ''),
      timestamp,
      JSON.stringify(sortedQuery)
    ].join('|');

    // Create HMAC signature
    return createHmac('sha256', secret)
      .update(stringToSign)
      .digest('hex');
  } catch (error) {
    console.error('Error signing request:', error);
    throw new Error('Failed to sign request');
  }
};

/**
 * Verifies a request signature
 * @param {Object} req - Express request object
 * @param {string} secret - The secret key for verification
 * @returns {boolean} True if signature is valid
 */
export const verifyRequestSignature = (req, secret) => {
  try {
    const signature = req.headers['x-signature'];
    const timestamp = req.headers['x-timestamp'];
    
    if (!signature || !timestamp) {
      return false;
    }

    // Verify timestamp is recent (within 5 minutes)
    const requestTime = parseInt(timestamp, 10);
    const currentTime = Date.now();
    
    if (Math.abs(currentTime - requestTime) > 300000) { // 5 minutes
      return false;
    }

    // Clone request body to avoid mutating the original
    const body = req.body ? { ...req.body } : {};
    
    // Remove signature from body if present
    if (body.signature) {
      delete body.signature;
    }

    // Get the expected signature
    const expectedSignature = signRequest(
      secret,
      req.method,
      req.path,
      body,
      timestamp,
      req.query
    );

    // Compare signatures in constant time
    return compareSignatures(signature, expectedSignature);
  } catch (error) {
    console.error('Error verifying signature:', error);
    return false;
  }
};

/**
 * Compares two signatures in constant time to prevent timing attacks
 * @param {string} a - First signature
 * @param {string} b - Second signature
 * @returns {boolean} True if signatures match
 */
const compareSignatures = (a, b) => {
  if (a.length !== b.length) return false;
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
};

/**
 * Recursively sorts object keys alphabetically
 * @param {Object} obj - The object to sort
 * @returns {Object} New object with sorted keys
 */
const sortObjectKeys = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sortObjectKeys);
  }
  
  const sorted = {};
  Object.keys(obj)
    .sort()
    .forEach(key => {
      sorted[key] = sortObjectKeys(obj[key]);
    });
  
  return sorted;
};

/**
 * Middleware to verify request signatures
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export const verifySignatureMiddleware = (req, res, next) => {
  // Skip verification for GET requests without body
  if (req.method === 'GET' && !Object.keys(req.body || {}).length) {
    return next();
  }

  // Get API key from header or query parameter
  const apiKey = req.headers['x-api-key'] || req.query.apiKey;
  
  if (!apiKey) {
    return res.status(401).json({ 
      error: 'API key is required',
      code: 'MISSING_API_KEY'
    });
  }

  // In a real implementation, you would look up the secret associated with the API key
  // For this example, we'll use a dummy secret
  const secret = process.env.API_SECRET || 'your-secret-key';
  
  if (!verifyRequestSignature(req, secret)) {
    return res.status(401).json({ 
      error: 'Invalid request signature',
      code: 'INVALID_SIGNATURE'
    });
  }
  
  next();
};

/**
 * Creates a signed request configuration
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {Object} data - Request data
 * @param {string} apiKey - API key
 * @param {string} secret - API secret
 * @returns {Object} Axios request config
 */
export const createSignedRequest = (method, url, data, apiKey, secret) => {
  const timestamp = Date.now();
  const path = new URL(url).pathname;
  
  // Create signature
  const signature = signRequest(
    secret,
    method,
    path,
    method !== 'GET' ? data : {},
    timestamp,
    method === 'GET' ? data : {}
  );
  
  // Return axios config
  return {
    method,
    url,
    ...(method !== 'GET' && { data }),
    ...(method === 'GET' && { params: data }),
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': apiKey,
      'X-Signature': signature,
      'X-Timestamp': timestamp.toString(),
      'X-Request-ID': generateRequestId()
    }
  };
};

/**
 * Generates a unique request ID
 * @returns {string} A unique request ID
 */
const generateRequestId = () => {
  return 'req_' + Math.random().toString(36).substr(2, 9);
};

// Export a default instance with common configuration
export default {
  signRequest,
  verifyRequestSignature,
  verifySignatureMiddleware,
  createSignedRequest
};
