// Text encoding/decoding utilities
const textEncoder = new TextEncoder();
const textDecoder = new TextDecoder();

/**
 * Convert a hex string to an ArrayBuffer
 * @param {string} hexString - The hex string to convert
 * @returns {ArrayBuffer} - The converted ArrayBuffer
 */
function hexToArrayBuffer(hexString) {
  const bytes = new Uint8Array(hexString.length / 2);
  for (let i = 0; i < hexString.length; i += 2) {
    bytes[i / 2] = parseInt(hexString.substring(i, i + 2), 16);
  }
  return bytes.buffer;
}

/**
 * Convert an ArrayBuffer to a hex string
 * @param {ArrayBuffer} buffer - The buffer to convert
 * @returns {string} - The hex string
 */
function arrayBufferToHex(buffer) {
  return Array.from(new Uint8Array(buffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Hash a string using SHA-256
 * @param {string} input - The string to hash
 * @returns {Promise<string>} - A promise that resolves to the hashed string
 */
export const hashString = async (input) => {
  if (!input) return null;
  const msgBuffer = textEncoder.encode(input);
  const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
  return arrayBufferToHex(hashBuffer);
};

/**
 * Generate a cryptographically secure random string
 * @param {number} length - Length of the random string
 * @returns {string} - The generated random string
 */
export const generateRandomString = (length = 32) => {
  const array = new Uint8Array(Math.ceil(length / 2));
  crypto.getRandomValues(array);
  return Array.from(array, b => b.toString(16).padStart(2, '0')).join('').slice(0, length);
};

/**
 * Encrypt data using AES-256-GCM
 * @param {string} text - The text to encrypt
 * @param {string} key - The encryption key (must be 32 bytes for AES-256)
 * @returns {Promise<string>} - A promise that resolves to the encrypted data as a string
 */
export const encrypt = async (text, key) => {
  const iv = crypto.getRandomValues(new Uint8Array(12));
  const encodedKey = await crypto.subtle.importKey(
    'raw',
    hexToArrayBuffer(key),
    { name: 'AES-GCM' },
    false,
    ['encrypt']
  );
  
  const encrypted = await crypto.subtle.encrypt(
    { name: 'AES-GCM', iv },
    encodedKey,
    textEncoder.encode(text)
  );
  
  return [
    arrayBufferToHex(iv.buffer),
    arrayBufferToHex(encrypted.slice(-16)), // Auth tag is last 16 bytes
    arrayBufferToHex(encrypted.slice(0, -16)) // Encrypted data is all but last 16 bytes
  ].join(':');
};

/**
 * Decrypt data using AES-256-GCM
 * @param {string} encryptedData - The encrypted data string
 * @param {string} key - The encryption key (must be 32 bytes for AES-256)
 * @returns {Promise<string>} - A promise that resolves to the decrypted text
 */
export const decrypt = async (encryptedData, key) => {
  const [ivHex, authTagHex, encryptedHex] = encryptedData.split(':');
  const iv = hexToArrayBuffer(ivHex);
  const encrypted = new Uint8Array([
    ...new Uint8Array(hexToArrayBuffer(encryptedHex)),
    ...new Uint8Array(hexToArrayBuffer(authTagHex))
  ]);
  
  const encodedKey = await crypto.subtle.importKey(
    'raw',
    hexToArrayBuffer(key),
    { name: 'AES-GCM' },
    false,
    ['decrypt']
  );
  
  try {
    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      encodedKey,
      encrypted
    );
    
    return textDecoder.decode(decrypted);
  } catch (error) {
    console.error('Decryption failed:', error);
    throw new Error('Failed to decrypt data');
  }
  const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(key, 'hex'), iv);
  decipher.setAuthTag(authTag);
  
  return decipher.update(encrypted, 'binary', 'utf8') + decipher.final('utf8');
};

/**
 * Sanitize user input to prevent XSS
 * @param {string} input - The input to sanitize
 * @returns {string} - The sanitized input
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

/**
 * Validate email format
 * @param {string} email - The email to validate
 * @returns {boolean} - Whether the email is valid
 */
export const isValidEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(String(email).toLowerCase());
};

/**
 * Validate password strength
 * @param {string} password - The password to validate
 * @returns {Object} - Object with validation results
 */
export const validatePassword = (password) => {
  const minLength = 12;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  const isLongEnough = password.length >= minLength;
  
  return {
    isValid: hasUppercase && hasLowercase && hasNumbers && hasSpecial && isLongEnough,
    hasUppercase,
    hasLowercase,
    hasNumbers,
    hasSpecial,
    isLongEnough,
    minLength
  };
};

/**
 * Generate a secure API key
 * @param {string} prefix - Optional prefix for the API key
 * @returns {string} - The generated API key
 */
export const generateApiKey = (prefix = 'sk_') => {
  const randomPart = crypto.randomBytes(32).toString('hex');
  return `${prefix}${randomPart}`;
};

/**
 * Hash an API key for secure storage
 * @param {string} apiKey - The API key to hash
 * @param {string} salt - The salt to use for hashing
 * @returns {string} - The hashed API key
 */
export const hashApiKey = (apiKey, salt) => {
  return crypto.pbkdf2Sync(apiKey, salt, 10000, 64, 'sha512').toString('hex');
};

/**
 * Verify an API key against a hash
 * @param {string} apiKey - The API key to verify
 * @param {string} hashedKey - The hashed key to verify against
 * @param {string} salt - The salt used for hashing
 * @returns {boolean} - Whether the API key is valid
 */
export const verifyApiKey = (apiKey, hashedKey, salt) => {
  const hash = crypto.pbkdf2Sync(apiKey, salt, 10000, 64, 'sha512').toString('hex');
  return hash === hashedKey;
};
