import { sha256 } from 'crypto-hash';

/**
 * Gets the client's IP address using a fallback mechanism
 * @returns {Promise<string>} The client's IP address or a fallback ID
 */
export const getClientIP = async () => {
  try {
    // First try to get IP from a public IP API
    const response = await fetch('https://api.ipify.org?format=json');
    if (response.ok) {
      const data = await response.json();
      return data.ip;
    }
    
    // Fallback to WebRTC IP detection (works in most browsers)
    const peerConnection = window.RTCPeerConnection || 
                          window.mozRTCPeerConnection || 
                          window.webkitRTCPeerConnection;
    
    if (peerConnection) {
      const pc = new peerConnection({ iceServers: [] });
      const noop = () => {};
      let ip = null;
      
      // Create a dummy data channel
      pc.createDataChannel('');
      
      // Create offer and set local description
      pc.createOffer()
        .then(sdp => pc.setLocalDescription(sdp, noop, noop))
        .catch(noop);
      
      // Get the IP from the ICE candidate
      pc.onicecandidate = (ice) => {
        if (!ice || !ice.candidate || !ice.candidate.candidate) return;
        const ipMatch = ice.candidate.candidate.match(/([0-9]{1,3}(\.[0-9]{1,3}){3})/);
        if (ipMatch) {
          ip = ipMatch[1];
          pc.onicecandidate = noop;
          pc.close();
        }
      };
      
      if (ip) return ip;
    }
    
    // Final fallback to a random ID if IP detection fails
    return `fallback-${Math.random().toString(36).substr(2, 9)}`;
  } catch (error) {
    console.error('Error getting client IP:', error);
    return `error-${Math.random().toString(36).substr(2, 9)}`;
  }
};

/**
 * Generates a hardware ID (HWID) from the client's IP address using SHA-256
 * @returns {Promise<string>} A SHA-256 hash of the client's IP address
 */
export const generateHWID = async () => {
  try {
    const ip = await getClientIP();
    // Add some client-specific info to make it more unique
    const userAgent = window.navigator.userAgent;
    const platform = window.navigator.platform;
    const language = window.navigator.language;
    
    // Combine all the data
    const combinedData = `${ip}:${userAgent}:${platform}:${language}`;
    
    // Generate SHA-256 hash
    const hash = await sha256(combinedData);
    return hash;
  } catch (error) {
    console.error('Error generating HWID:', error);
    // Return a fallback HWID if there's an error
    return `hwid-${Math.random().toString(36).substr(2, 16)}`;
  }
};

/**
 * Gets the HWID from sessionStorage or generates a new one
 * @returns {Promise<string>} The HWID
 */
export const getHWID = async () => {
  // Check if HWID exists in sessionStorage
  const storedHWID = sessionStorage.getItem('hwid');
  if (storedHWID) return storedHWID;
  
  // Generate a new HWID if it doesn't exist
  const hwid = await generateHWID();
  
  // Store the HWID in sessionStorage
  sessionStorage.setItem('hwid', hwid);
  
  return hwid;
};
