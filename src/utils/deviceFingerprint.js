import { hashString } from './security';

/**
 * Generates a unique device fingerprint using multiple browser attributes
 * @returns {Promise<string>} A hash representing the device fingerprint
 */
export const generateDeviceFingerprint = async () => {
  const components = {
    // Screen properties
    screen: {
      width: screen.width,
      height: screen.height,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth,
      availableWidth: screen.availWidth,
      availableHeight: screen.availHeight
    },
    // Browser properties
    navigator: {
      userAgent: navigator.userAgent,
      language: navigator.language,
      languages: navigator.languages,
      hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
      deviceMemory: navigator.deviceMemory || 'unknown',
      maxTouchPoints: navigator.maxTouchPoints || 0,
      platform: navigator.platform,
      vendor: navigator.vendor,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack,
      webdriver: navigator.webdriver,
      plugins: Array.from(navigator.plugins || []).map(p => ({
        name: p.name,
        description: p.description,
        filename: p.filename,
        length: p.length
      })),
      mimeTypes: Array.from(navigator.mimeTypes || []).map(m => ({
        type: m.type,
        description: m.description,
        suffixes: m.suffixes
      }))
    },
    // WebGL fingerprint
    webgl: await getWebGLFingerprint(),
    // Canvas fingerprint
    canvas: getCanvasFingerprint(),
    // Audio context fingerprint
    audio: await getAudioFingerprint(),
    // Font detection
    fonts: await getFontsFingerprint(),
    // Timezone and locale
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timezoneOffset: new Date().getTimezoneOffset(),
    // Storage capabilities
    storage: {
      localStorage: !!window.localStorage,
      sessionStorage: !!window.sessionStorage,
      indexedDB: !!window.indexedDB,
      serviceWorker: 'serviceWorker' in navigator,
      webSQL: 'openDatabase' in window,
      webkitPersistentStorage: 'webkitPersistentStorage' in navigator,
      webkitTemporaryStorage: 'webkitTemporaryStorage' in navigator
    },
    // Touch support
    touchSupport: {
      touch: 'ontouchstart' in window,
      maxTouchPoints: navigator.maxTouchPoints || 0,
      touchEvent: 'ontouchstart' in window,
      touchStart: 'ontouchstart' in window
    },
    // Browser features
    features: {
      geolocation: 'geolocation' in navigator,
      notifications: 'Notification' in window,
      pushManager: 'PushManager' in window,
      mediaDevices: 'mediaDevices' in navigator,
      permissions: 'permissions' in navigator,
      battery: 'getBattery' in navigator,
      connection: 'connection' in navigator || 'mozConnection' in navigator || 'webkitConnection' in navigator,
      credentials: 'credentials' in navigator,
      crypto: 'crypto' in window && 'getRandomValues' in window.crypto,
      performance: 'performance' in window,
      serviceWorker: 'serviceWorker' in navigator,
      webGL: 'WebGLRenderingContext' in window
    },
    // Math constants (for additional entropy)
    math: {
      sin: Math.sin(1),
      cos: Math.cos(1),
      tan: Math.tan(1),
      log: Math.log(1),
      sqrt: Math.sqrt(2),
      exp: Math.exp(1)
    },
    // Time-based component
    timestamp: Date.now(),
    // Random component (for additional entropy)
    random: Math.random()
  };

  // Add WebRTC IP if available
  try {
    const ip = await getWebRTCIP();
    if (ip) {
      components.webRTC = { ip };
    }
  } catch (e) {
    console.debug('WebRTC IP detection failed');
  }

  // Generate a stable string representation
  const fingerprintString = JSON.stringify(components, (key, value) => {
    // Handle undefined values
    if (value === undefined) return 'undefined';
    // Handle functions
    if (typeof value === 'function') return value.toString();
    return value;
  });

  // Return a hash of the fingerprint
  return hashString(fingerprintString);
};

// Helper function to get WebGL fingerprint
const getWebGLFingerprint = () => {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) return null;
    
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    return {
      vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
      renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL),
      version: gl.getParameter(gl.VERSION),
      shadingLanguage: gl.getParameter(gl.SHADING_LANGUAGE_VERSION),
      maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
      maxRenderBufferSize: gl.getParameter(gl.MAX_RENDERBUFFER_SIZE),
      maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS),
      vendorPrefixes: [
        'WEBKIT_',
        'MOZ_',
        'MS_',
        'O_',
        'CHROME_'
      ].filter(prefix => {
        try {
          return gl.getExtension(`${prefix}WEBGL_debug_renderer_info`);
        } catch (e) {
          return false;
        }
      })
    };
  } catch (e) {
    console.debug('WebGL fingerprint failed:', e);
    return null;
  }
};

// Helper function to get Canvas fingerprint
const getCanvasFingerprint = () => {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Text with lowercase/uppercase/punctuation
    const txt = 'ClientFingerprint';
    
    // Draw text
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.textBaseline = 'alphabetic';
    ctx.fillStyle = '#f60';
    ctx.fillRect(125, 1, 62, 20);
    ctx.fillStyle = '#069';
    ctx.fillText(txt, 2, 15);
    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
    ctx.fillText(txt, 4, 17);
    
    // Get image data
    return canvas.toDataURL();
  } catch (e) {
    console.debug('Canvas fingerprint failed:', e);
    return null;
  }
};

// Helper function to get Audio fingerprint
const getAudioFingerprint = () => {
  return new Promise((resolve) => {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const analyser = audioContext.createAnalyser();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(analyser);
      analyser.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      // Set up analyser
      analyser.fftSize = 256;
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      // Get frequency data
      analyser.getByteFrequencyData(dataArray);
      
      // Convert to string
      const fingerprint = Array.from(dataArray).join(',');
      
      // Clean up
      oscillator.disconnect();
      analyser.disconnect();
      gainNode.disconnect();
      audioContext.close();
      
      resolve(fingerprint);
    } catch (e) {
      console.debug('Audio fingerprint failed:', e);
      resolve(null);
    }
  });
};

// Helper function to detect installed fonts
const getFontsFingerprint = async () => {
  try {
    // List of fonts to check
    const fonts = [
      // Windows 10
      'Arial', 'Arial Black', 'Arial Unicode MS', 'Calibri', 'Cambria', 'Comic Sans MS',
      'Consolas', 'Courier New', 'Georgia', 'Impact', 'Lucida Console', 'Segoe UI',
      'Tahoma', 'Times New Roman', 'Trebuchet MS', 'Verdana',
      // Mac OS X
      'American Typewriter', 'Andale Mono', 'Arial Narrow', 'Avenir', 'Avenir Next',
      'Baskerville', 'Big Caslon', 'Brush Script MT', 'Chalkboard', 'Chalkboard SE',
      'Chalkduster', 'Charter', 'Cochin', 'Copperplate', 'Didot', 'Futura',
      'Geneva', 'Gill Sans', 'Helvetica', 'Helvetica Neue', 'Hoefler Text',
      'Lucida Grande', 'Optima', 'Palatino', 'Times', 'Zapfino'
    ];
    
    // Check which fonts are available
    const availableFonts = [];
    
    // Create a test element
    const testElement = document.createElement('span');
    testElement.style.fontSize = '24px';
    testElement.style.position = 'absolute';
    testElement.style.left = '-9999px';
    testElement.style.visibility = 'hidden';
    testElement.innerHTML = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    
    // Add to document
    document.body.appendChild(testElement);
    
    // Test each font
    for (const font of fonts) {
      testElement.style.fontFamily = `"${font}", monospace`;
      const width1 = testElement.offsetWidth;
      testElement.style.fontFamily = `"${font}", arial, sans-serif`;
      const width2 = testElement.offsetWidth;
      
      // If widths are different, the font is available
      if (width1 !== width2) {
        availableFonts.push(font);
      }
    }
    
    // Clean up
    document.body.removeChild(testElement);
    
    return availableFonts;
  } catch (e) {
    console.debug('Font detection failed:', e);
    return [];
  }
};

// Helper function to get WebRTC IP (local IP)
const getWebRTCIP = () => {
  return new Promise((resolve) => {
    try {
      const RTCPeerConnection = window.RTCPeerConnection ||
        window.mozRTCPeerConnection ||
        window.webkitRTCPeerConnection;
      
      if (!RTCPeerConnection) {
        resolve(null);
        return;
      }
      
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });
      
      pc.createDataChannel('');
      
      pc.createOffer()
        .then(offer => pc.setLocalDescription(offer))
        .catch(() => resolve(null));
      
      pc.onicecandidate = (ice) => {
        if (!ice || !ice.candidate || !ice.candidate.candidate) {
          resolve(null);
          return;
        }
        
        const ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/;
        const ipMatch = ice.candidate.candidate.match(ipRegex);
        
        if (ipMatch) {
          resolve(ipMatch[1]);
        } else {
          resolve(null);
        }
        
        pc.onicecandidate = () => {};
        pc.close();
      };
      
      // Timeout after 1 second
      setTimeout(() => {
        pc.onicecandidate = () => {};
        pc.close();
        resolve(null);
      }, 1000);
    } catch (e) {
      console.debug('WebRTC IP detection failed:', e);
      resolve(null);
    }
  });
};

// Export a singleton instance
let fingerprintCache = null;

export const getFingerprint = async () => {
  if (!fingerprintCache) {
    fingerprintCache = await generateDeviceFingerprint();
  }
  return fingerprintCache;
};

// Initialize fingerprint on module load
if (typeof window !== 'undefined') {
  getFingerprint().catch(console.error);
}
