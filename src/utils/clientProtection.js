import { getFingerprint } from './deviceFingerprint';

class ClientProtection {
  constructor() {
    this.initialized = false;
    this.checks = new Map();
    this.violationCount = 0;
    this.maxViolations = 3;
    this.checkInterval = null;
    this.lastViolationTime = 0;
    this.cooldownPeriod = 5 * 60 * 1000; // 5 minutes cooldown
    this.preventDevTools = true;
    this.preventDebugger = true;
    this.preventTampering = true;
    this.detectionEnabled = true;
  }

  /**
   * Initialize the client protection system
   * @param {Object} options - Configuration options
   */
  async initialize(options = {}) {
    if (this.initialized) return;

    // Apply options
    this.preventDevTools = options.preventDevTools !== false;
    this.preventDebugger = options.preventDebugger !== false;
    this.preventTampering = options.preventTampering !== false;
    this.maxViolations = options.maxViolations || 3;
    this.cooldownPeriod = options.cooldownPeriod || 5 * 60 * 1000;

    // Add default checks
    if (this.preventDevTools) {
      this.addCheck('devTools', this.checkDevTools.bind(this));
      this.addCheck('devToolsSize', this.checkDevToolsSize.bind(this));
    }

    if (this.preventDebugger) {
      this.addCheck('debugger', this.checkDebugger.bind(this));
    }

    if (this.preventTampering) {
      this.addCheck('tampering', this.checkCodeTampering.bind(this));
      this.addCheck('environment', this.checkEnvironment.bind(this));
      this.addCheck('timeCheating', this.checkTimeCheating.bind(this));
    }

    // Start monitoring
    this.startMonitoring();
    this.initialized = true;

    // Get device fingerprint
    this.fingerprint = await getFingerprint();
    
    // Initialize event listeners
    this.initializeEventListeners();
    
    // Initial check
    this.runChecks().catch(console.error);
  }

  /**
   * Add a custom protection check
   * @param {string} name - Name of the check
   * @param {Function} checkFn - Function that returns a boolean or Promise<boolean>
   */
  addCheck(name, checkFn) {
    this.checks.set(name, {
      fn: checkFn,
      lastRun: 0,
      lastResult: null,
      enabled: true
    });
  }

  /**
   * Remove a protection check
   * @param {string} name - Name of the check to remove
   */
  removeCheck(name) {
    this.checks.delete(name);
  }

  /**
   * Enable or disable a specific check
   * @param {string} name - Name of the check
   * @param {boolean} enabled - Whether to enable or disable the check
   */
  setCheckEnabled(name, enabled) {
    const check = this.checks.get(name);
    if (check) {
      check.enabled = enabled;
    }
  }

  /**
   * Start the monitoring process
   */
  startMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    // Run checks every 10 seconds
    this.checkInterval = setInterval(() => {
      if (this.detectionEnabled) {
        this.runChecks().catch(console.error);
      }
    }, 10000);

    // Initial check
    this.runChecks().catch(console.error);
  }

  /**
   * Stop the monitoring process
   */
  stopMonitoring() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Run all protection checks
   */
  async runChecks() {
    if (!this.detectionEnabled) return;

    const now = Date.now();
    const results = {};
    let hasViolation = false;

    // Run each check
    for (const [name, check] of this.checks.entries()) {
      if (!check.enabled) continue;

      try {
        const result = await check.fn();
        const isValid = result === true || (result && result.valid !== false);
        
        check.lastRun = now;
        check.lastResult = result;
        results[name] = result;

        if (!isValid) {
          hasViolation = true;
          await this.handleViolation(name, result);
        }
      } catch (error) {
        console.error(`Check ${name} failed:`, error);
        results[name] = { valid: false, error: error.message };
      }
    }

    return results;
  }

  /**
   * Handle a protection violation
   * @param {string} checkName - Name of the check that failed
   * @param {Object} result - Check result
   */
  async handleViolation(checkName, result) {
    const now = Date.now();
    
    // Reset violation count if cooldown period has passed
    if (now - this.lastViolationTime > this.cooldownPeriod) {
      this.violationCount = 0;
    }

    this.violationCount++;
    this.lastViolationTime = now;

    // Prepare violation data
    const violation = {
      timestamp: now,
      check: checkName,
      result,
      fingerprint: this.fingerprint,
      url: window.location.href,
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language
    };

    // Log the violation
    console.warn('Security violation detected:', violation);

    // Report to server
    this.reportViolation(violation);

    // Take action if max violations reached
    if (this.violationCount >= this.maxViolations) {
      await this.enforceSecurityPolicy();
    }
  }

  /**
   * Report a violation to the server
   * @param {Object} violation - Violation data
   */
  async reportViolation(violation) {
    try {
      // In a real application, you would send this to your server
      // For now, we'll just log it
      if (process.env.NODE_ENV !== 'production') {
        console.warn('Violation reported:', violation);
      }

      // Example of sending to server (uncomment and implement as needed)
      /*
      await fetch('/api/security/violations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(violation),
        keepalive: true // Ensure the request completes even if page is closed
      });
      */
    } catch (error) {
      console.error('Failed to report violation:', error);
    }
  }

  /**
   * Enforce security policy when violations are detected
   */
  async enforceSecurityPolicy() {
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    if (isDevelopment) {
      // In development, just log a warning and continue
      console.warn('Security policy would be enforced in production');
      return;
    }

    // Disable further checks to prevent infinite loops
    this.detectionEnabled = false;
    this.stopMonitoring();

    // Log the user out
    // Example: await authService.logout();

    // Redirect to a security notice page or show a message
    window.location.href = '/security-violation';

    // Clear sensitive data from memory
    // Example: clearSensitiveData();

    // Notify the user
    const message = 'Security violation detected. Please refresh the page and try again.';
    console.error(message);
    
    // Show a user-facing message
    this.showSecurityAlert(message);
  }

  /**
   * Show a security alert to the user
   * @param {string} message - The message to display
   */
  showSecurityAlert(message) {
    // Create a non-dismissible overlay
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    overlay.style.color = 'white';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.zIndex = '999999';
    overlay.style.fontFamily = 'Arial, sans-serif';
    overlay.style.textAlign = 'center';
    overlay.style.padding = '20px';
    overlay.style.boxSizing = 'border-box';
    overlay.innerHTML = `
      <div style="max-width: 600px; background: #1a1a1a; padding: 30px; border-radius: 8px; border: 1px solid #ff4444;">
        <h2 style="color: #ff4444; margin-top: 0;">Security Alert</h2>
        <p>${message}</p>
        <p>If you believe this is an error, please contact support.</p>
        <button id="refresh-btn" style="
          background: #ff4444;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
          margin-top: 20px;
        ">Refresh Page</button>
      </div>
    `;

    // Add to document
    document.body.appendChild(overlay);

    // Add refresh button handler
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        window.location.reload();
      });
    }
  }

  /**
   * Initialize event listeners for additional protection
   */
  initializeEventListeners() {
    // Prevent context menu (right-click)
    document.addEventListener('contextmenu', (e) => {
      if (this.preventTampering) {
        e.preventDefault();
        return false;
      }
    }, false);

    // Prevent keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+Shift+C, Ctrl+U
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J' || e.key === 'C')) ||
        (e.ctrlKey && e.key === 'u')
      ) {
        if (this.preventDevTools) {
          e.preventDefault();
          return false;
        }
      }
    });

    // Detect window resize (potential devtools)
    let lastWidth = window.innerWidth;
    let lastHeight = window.innerHeight;
    
    window.addEventListener('resize', () => {
      const newWidth = window.innerWidth;
      const newHeight = window.innerHeight;
      
      // If window was resized to a very small size, it might be devtools
      if ((newWidth < 300 || newHeight < 300) && (newWidth !== lastWidth || newHeight !== lastHeight)) {
        this.handleViolation('windowResize', {
          valid: false,
          message: 'Suspicious window resize detected',
          width: newWidth,
          height: newHeight
        });
      }
      
      lastWidth = newWidth;
      lastHeight = newHeight;
    });
  }

  /**
   * Check if DevTools is open
   */
  checkDevTools() {
    // Method 1: Check for devtools size
    const threshold = 160;
    const widthThreshold = window.outerWidth - window.innerWidth > threshold;
    const heightThreshold = window.outerHeight - window.innerHeight > threshold;
    
    if (widthThreshold || heightThreshold) {
      return {
        valid: false,
        message: 'DevTools detected via window size difference',
        widthDiff: window.outerWidth - window.innerWidth,
        heightDiff: window.outerHeight - window.innerHeight
      };
    }

    // Method 2: Check for devtools function toString modification
    const devtools = /./;
    devtools.toString = function() {
      this.opened = true;
      return '';
    };
    
    if (devtools.opened) {
      return {
        valid: false,
        message: 'DevTools detected via toString check'
      };
    }

    return { valid: true };
  }

  /**
   * Check for DevTools via window size
   */
  checkDevToolsSize() {
    const threshold = 200;
    const widthThreshold = window.outerWidth - window.innerWidth > threshold;
    const heightThreshold = window.outerHeight - window.innerHeight > threshold;
    
    if (widthThreshold || heightThreshold) {
      return {
        valid: false,
        message: 'DevTools detected via window size check',
        widthDiff: window.outerWidth - window.innerWidth,
        heightDiff: window.outerHeight - window.innerHeight
      };
    }
    
    return { valid: true };
  }

  /**
   * Check for debugger statements
   */
  checkDebugger() {
    // This will be caught by the debugger if it's active
    const startTime = Date.now();
    debugger;
    const endTime = Date.now();
    
    // If the debugger is active, there will be a noticeable delay
    if (endTime - startTime > 100) {
      return {
        valid: false,
        message: 'Debugger detected',
        delay: endTime - startTime
      };
    }
    
    return { valid: true };
  }

  /**
   * Check for code tampering
   */
  checkCodeTampering() {
    // Check if important functions have been tampered with
    const functionsToCheck = [
      'fetch',
      'XMLHttpRequest.prototype.open',
      'XMLHttpRequest.prototype.send',
      'window.console.log',
      'Object.defineProperty',
      'Object.defineProperties'
    ];
    
    const tampered = [];
    
    for (const funcPath of functionsToCheck) {
      try {
        const parts = funcPath.split('.');
        let obj = window;
        
        for (let i = 0; i < parts.length - 1; i++) {
          obj = obj[parts[i]];
          if (!obj) break;
        }
        
        if (obj) {
          const funcName = parts[parts.length - 1];
          const func = obj[funcName];
          
          // Check if function has been modified
          if (func && func.toString().includes('[native code]')) {
            // Native function, likely not tampered with
          } else if (func) {
            // Non-native function, might be tampered with
            tampered.push(funcPath);
          }
        }
      } catch (e) {
        console.error(`Error checking function ${funcPath}:`, e);
      }
    }
    
    if (tampered.length > 0) {
      return {
        valid: false,
        message: 'Code tampering detected',
        tamperedFunctions: tampered
      };
    }
    
    return { valid: true };
  }

  /**
   * Check for suspicious environment
   */
  checkEnvironment() {
    // Check for common automation tools
    const automationTools = [
      'webdriver',
      '__nightmare',
      '_selenium',
      '_phantom',
      'callPhantom',
      'domAutomation',
      'domAutomationController',
      'puppeteer',
      'playwright',
      'cypress',
      'testcafe',
      'webdriverio'
    ];
    
    const detected = [];
    
    for (const tool of automationTools) {
      if (navigator[tool] || window[tool]) {
        detected.push(tool);
      }
    }
    
    if (detected.length > 0) {
      return {
        valid: false,
        message: 'Automation tools detected',
        tools: detected
      };
    }
    
    // Check for headless browsers
    const isHeadless = !navigator.webdriver && 
                      !window.chrome && 
                      !window.sidebar && 
                      'scrabble' in window;
    
    if (isHeadless) {
      return {
        valid: false,
        message: 'Headless browser detected'
      };
    }
    
    return { valid: true };
  }

  /**
   * Check for time cheating
   */
  checkTimeCheating() {
    // Check if system time was changed
    const serverTime = Date.now(); // In a real app, get this from the server
    const clientTime = Date.now();
    
    // Allow small differences due to network latency
    if (Math.abs(serverTime - clientTime) > 5000) { // 5 seconds
      return {
        valid: false,
        message: 'Time manipulation detected',
        serverTime,
        clientTime,
        difference: Math.abs(serverTime - clientTime)
      };
    }
    
    return { valid: true };
  }
}

// Export a singleton instance
const clientProtection = new ClientProtection();
export default clientProtection;
