import { RateLimiterRedis } from 'rate-limiter-flexible';
import Redis from 'ioredis';
import { getClientIp } from '@supercharge/request-ip';
import { getFingerprint } from '../utils/deviceFingerprint';

// Initialize Redis client
const redisClient = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Configure different rate limiters for different endpoints
const rateLimiters = {
  // Strict limiter for authentication endpoints
  auth: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'rl_auth',
    points: 5, // 5 login attempts
    duration: 60 * 5, // per 5 minutes
    blockDuration: 60 * 15, // block for 15 minutes after limit
  }),
  
  // General API limiter
  api: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'rl_api',
    points: 100, // 100 requests
    duration: 60, // per minute
  }),
  
  // Strict limiter for sensitive operations
  sensitive: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'rl_sensitive',
    points: 10, // 10 requests
    duration: 60 * 5, // per 5 minutes
  }),
  
  // Public endpoints with higher limits
  public: new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'rl_public',
    points: 500, // 500 requests
    duration: 60 * 15, // per 15 minutes
  }),
};

/**
 * Creates a rate limiter middleware
 * @param {string} type - Type of rate limiter to use
 * @param {Object} options - Additional options
 * @param {Function} options.keyGenerator - Function to generate rate limit key
 * @param {boolean} options.consumeOnError - Whether to consume points on error
 * @returns {Function} Express middleware
 */
export const rateLimiter = (type = 'api', options = {}) => {
  const limiter = rateLimiters[type] || rateLimiters.api;
  
  return async (req, res, next) => {
    try {
      // Generate a unique key for rate limiting
      const key = await generateRateLimitKey(req, options.keyGenerator);
      
      // Try to consume a point
      await limiter.consume(key);
      
      // Set rate limit headers
      const rateLimitHeaders = await getRateLimitHeaders(limiter, key);
      Object.entries(rateLimitHeaders).forEach(([key, value]) => {
        res.setHeader(key, value);
      });
      
      next();
    } catch (error) {
      // Rate limit exceeded
      const retryAfter = error.msBeforeNext ? Math.ceil(error.msBeforeNext / 1000) : 60;
      
      res.setHeader('Retry-After', retryAfter);
      res.status(429).json({
        error: 'Too many requests',
        message: `Rate limit exceeded. Please try again in ${retryAfter} seconds.`,
        retryAfter,
        code: 'RATE_LIMIT_EXCEEDED',
      });
    }
  };
};

/**
 * Generate a rate limit key based on request details
 * @param {Object} req - Express request object
 * @param {Function} customKeyGenerator - Custom key generator function
 * @returns {Promise<string>} Rate limit key
 */
async function generateRateLimitKey(req, customKeyGenerator) {
  if (typeof customKeyGenerator === 'function') {
    return customKeyGenerator(req);
  }
  
  // Default key generator: IP + user agent + device fingerprint
  const ip = getClientIp(req) || 'unknown-ip';
  const userAgent = req.headers['user-agent'] || 'unknown-ua';
  
  try {
    const fingerprint = await getFingerprint();
    return `rate:${ip}:${userAgent}:${fingerprint}`;
  } catch (error) {
    // Fallback to IP + user agent if fingerprinting fails
    return `rate:${ip}:${userAgent}`;
  }
}

/**
 * Get rate limit headers
 * @param {Object} limiter - Rate limiter instance
 * @param {string} key - Rate limit key
 * @returns {Promise<Object>} Rate limit headers
 */
async function getRateLimitHeaders(limiter, key) {
  try {
    const res = await limiter.get(key);
    if (!res) return {};
    
    return {
      'X-RateLimit-Limit': limiter.points,
      'X-RateLimit-Remaining': res.remainingPoints,
      'X-RateLimit-Reset': Math.ceil(res.msBeforeNext / 1000),
    };
  } catch (error) {
    console.error('Error getting rate limit headers:', error);
    return {};
  }
}

/**
 * Middleware to block suspicious requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export const blockSuspiciousActivity = async (req, res, next) => {
  try {
    // Check for common attack patterns
    const isSuspicious = checkForSuspiciousActivity(req);
    
    if (isSuspicious) {
      // Log the suspicious activity
      await logSuspiciousActivity(req, 'Suspicious request pattern detected');
      
      // Block the request
      return res.status(403).json({
        error: 'Access denied',
        code: 'ACCESS_DENIED',
      });
    }
    
    next();
  } catch (error) {
    console.error('Error in suspicious activity check:', error);
    next(); // Continue on error
  }
};

/**
 * Check for suspicious activity in the request
 * @param {Object} req - Express request object
 * @returns {boolean} True if request is suspicious
 */
function checkForSuspiciousActivity(req) {
  // Check for common attack patterns in headers
  const suspiciousHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'cf-connecting-ip',
    'true-client-ip',
  ];
  
  // Check for suspicious headers
  for (const header of suspiciousHeaders) {
    if (req.headers[header] && isSuspiciousValue(req.headers[header])) {
      return true;
    }
  }
  
  // Check for suspicious user agents
  const userAgent = req.headers['user-agent'] || '';
  if (isSuspiciousUserAgent(userAgent)) {
    return true;
  }
  
  // Check for suspicious query parameters
  for (const [key, value] of Object.entries(req.query)) {
    if (isSuspiciousValue(key) || isSuspiciousValue(value)) {
      return true;
    }
  }
  
  // Check for suspicious request body
  if (req.body) {
    const bodyString = JSON.stringify(req.body);
    if (isSuspiciousValue(bodyString)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Check if a value contains suspicious patterns
 * @param {string} value - Value to check
 * @returns {boolean} True if value is suspicious
 */
function isSuspiciousValue(value) {
  if (value == null || value === false) return false;

  const strValue = String(value).toLowerCase();

  // Common attack patterns
  const patterns = [
    // SQL Injection
    /\b(select|insert|update|delete|drop|union|exec|xp_|sp_)\b/i,
    /--|\/\*|\*\//,         // SQL comment patterns
    /;\s*$/,                // Statement termination

    // XSS patterns
    /<script\b[^>]*>.*?<\/script>/i,
    /on\w+\s*=/i,           // Inline event handlers
    /javascript:/i,
    /vbscript:/i,

    // Directory traversal
    /\.\.\//,
    /\\.\\/,               // Windows-style backslash traversal
    /\/etc\/(?:passwd|shadow)/i,

    // Command injection
    /\|\|/,
    /&&/,
    /;\s*[^$]/,            // Command separator (avoid matching end-of-sql semicolon)
    /`.*?`/,
    /\$\((.*?)\)/,
    /\$\{.*?\}/,

    // Common exploit tools
    /\b(sqlmap|nikto|nmap|burp|w3af|metasploit)\b/i,
  ];

  return patterns.some(pattern => pattern.test(strValue));
}


/**
 * Check if user agent is suspicious
 * @param {string} userAgent - User agent string
 * @returns {boolean} True if user agent is suspicious
 */
function isSuspiciousUserAgent(userAgent) {
  if (!userAgent) return true;
  
  const suspiciousPatterns = [
    // Common malicious tools
    /sqlmap/i,
    /nikto/i,
    /nmap/i,
    /burp/i,
    /w3af/i,
    /metasploit/i,
    /hydra/i,
    /dirbuster/i,
    /gobuster/i,
    /wpscan/i,
    /acunetix/i,
    /nessus/i,
    /openvas/i,
    /nikto/i,
    /skipfish/i,
    /w3af/i,
    
    // Suspicious user agents
    /^$/,
    /^python-requests\//i,
    /^curl\//i,
    /^wget\//i,
    /^libwww-perl\//i,
    /^Java\//i,
    /^Go\s/i,
    /^node-fetch\//i,
    /^axios\//i,
    /^okhttp\//i,
    /^python-httpx\//i,
  ];
  
  return suspiciousPatterns.some(pattern => pattern.test(userAgent));
}

/**
 * Log suspicious activity
 * @param {Object} req - Express request object
 * @param {string} reason - Reason for logging
 */
async function logSuspiciousActivity(req, reason) {
  try {
    const logEntry = {
      timestamp: new Date(),
      ip: getClientIp(req),
      method: req.method,
      path: req.path,
      query: req.query,
      headers: {
        'user-agent': req.headers['user-agent'],
        referer: req.headers['referer'],
        'x-forwarded-for': req.headers['x-forwarded-for'],
        'x-real-ip': req.headers['x-real-ip'],
      },
      reason,
    };
    
    // In a real application, you would save this to a database or log file
    console.warn('Suspicious activity detected:', logEntry);
    
    // Optionally, you could also send an alert to an admin
    await sendSecurityAlert(logEntry);
  } catch (error) {
    console.error('Error logging suspicious activity:', error);
  }
}

/**
 * Send a security alert
 * @param {Object} data - Alert data
 */
async function sendSecurityAlert(data) {
  // In a real application, you might want to send an email, Slack message, etc.
  // This is just a placeholder implementation
  console.warn('SECURITY ALERT:', data);
  
  // Example: Send alert to a webhook
  if (process.env.SECURITY_WEBHOOK_URL) {
    try {
      await fetch(process.env.SECURITY_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: '🔒 *Security Alert*',
          attachments: [
            {
              color: 'danger',
              fields: [
                { title: 'Path', value: data.path, short: true },
                { title: 'Method', value: data.method, short: true },
                { title: 'IP', value: data.ip || 'unknown', short: true },
                { title: 'Reason', value: data.reason || 'Suspicious activity detected' },
                { title: 'User Agent', value: data.headers['user-agent'] || 'unknown' },
                { title: 'Timestamp', value: data.timestamp.toISOString() },
              ],
            },
          ],
        }),
      });
    } catch (error) {
      console.error('Failed to send security alert:', error);
    }
  }
}

// Export rate limiters for direct use
export { rateLimiters };

export default {
  rateLimiter,
  blockSuspiciousActivity,
  rateLimiters,
};
