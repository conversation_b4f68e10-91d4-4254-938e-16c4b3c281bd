import { supabase } from '../lib/supabase';
import { getHWID } from '../utils/hwid';

// Rate limit configuration
const RATE_LIMIT = {
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  MAX_REQUESTS: 100, // Max requests per window per HWID
  MESSAGE: 'Too many requests, please try again later.'
};

// In-memory store for rate limiting
const rateLimitStore = new Map();

// Clean up old entries
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now - entry.timestamp > RATE_LIMIT.WINDOW_MS) {
      rateLimitStore.delete(key);
    }
  }
}, RATE_LIMIT.WINDOW_MS);

/**
 * Rate limiting middleware using HWID
 * @param {Object} options - Rate limiting options
 * @param {number} options.windowMs - Time window in milliseconds
 * @param {number} options.max - Maximum number of requests per window
 * @returns {Function} Express middleware function
 */
export const rateLimiter = (options = {}) => {
  const windowMs = options.windowMs || RATE_LIMIT.WINDOW_MS;
  const max = options.max || RATE_LIMIT.MAX_REQUESTS;

  return async (req, res, next) => {
    try {
      // Get HWID from request or generate a new one
      const hwid = req.headers['x-hwid'] || await getHWID();
      
      if (!hwid) {
        return res.status(400).json({ error: 'HWID is required' });
      }

      const now = Date.now();
      const windowStart = now - windowMs;
      
      // Get or create rate limit entry
      let entry = rateLimitStore.get(hwid) || {
        count: 0,
        resetTime: now + windowMs,
        timestamp: now
      };

      // Reset count if window has passed
      if (entry.timestamp < windowStart) {
        entry.count = 0;
        entry.resetTime = now + windowMs;
      }

      // Check if rate limit exceeded
      if (entry.count >= max) {
        const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
        
        // Log the rate limit event
        await logRateLimitEvent({
          hwid,
          ip: req.ip,
          path: req.path,
          method: req.method,
          userAgent: req.headers['user-agent']
        });

        // Set rate limit headers
        res.set({
          'Retry-After': retryAfter,
          'X-RateLimit-Limit': max,
          'X-RateLimit-Remaining': 0,
          'X-RateLimit-Reset': entry.resetTime
        });

        return res.status(429).json({
          error: RATE_LIMIT.MESSAGE,
          retryAfter: `${retryAfter} seconds`
        });
      }

      // Update rate limit entry
      entry.count++;
      entry.timestamp = now;
      rateLimitStore.set(hwid, entry);

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': max,
        'X-RateLimit-Remaining': Math.max(0, max - entry.count),
        'X-RateLimit-Reset': entry.resetTime
      });

      // Add HWID to request for later use
      req.hwid = hwid;
      next();
    } catch (error) {
      console.error('Rate limiter error:', error);
      // On error, allow the request to proceed
      next();
    }
  };
};

/**
 * Log rate limit events to the database
 * @param {Object} data - Event data
 */
async function logRateLimitEvent(data) {
  try {
    await supabase
      .from('rate_limit_events')
      .insert([{
        hwid: data.hwid,
        ip: data.ip,
        path: data.path,
        method: data.method,
        user_agent: data.userAgent,
        metadata: {
          timestamp: new Date().toISOString(),
          ...data.metadata
        }
      }]);
  } catch (error) {
    console.error('Error logging rate limit event:', error);
  }
}

/**
 * Get rate limit info for a specific HWID
 * @param {string} hwid - The HWID to check
 * @returns {Promise<Object>} Rate limit information
 */
export const getRateLimitInfo = async (hwid) => {
  const entry = rateLimitStore.get(hwid);
  if (!entry) {
    return {
      remaining: RATE_LIMIT.MAX_REQUESTS,
      limit: RATE_LIMIT.MAX_REQUESTS,
      resetTime: Date.now() + RATE_LIMIT.WINDOW_MS
    };
  }

  return {
    remaining: Math.max(0, RATE_LIMIT.MAX_REQUESTS - entry.count),
    limit: RATE_LIMIT.MAX_REQUESTS,
    resetTime: entry.resetTime
  };
};

/**
 * Middleware to check if a user is rate limited
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export const checkRateLimit = async (req, res, next) => {
  try {
    const hwid = req.headers['x-hwid'];
    if (!hwid) {
      return res.status(400).json({ error: 'HWID is required' });
    }

    const limitInfo = await getRateLimitInfo(hwid);
    
    // Add rate limit info to response headers
    res.set({
      'X-RateLimit-Limit': limitInfo.limit,
      'X-RateLimit-Remaining': limitInfo.remaining,
      'X-RateLimit-Reset': limitInfo.resetTime
    });

    next();
  } catch (error) {
    console.error('Rate limit check error:', error);
    next();
  }
};
