import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with fallback to VITE_ prefixed variables
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Rate limit configurations
const RATE_LIMITS = {
  // Key generation rate limits
  GENERATE: {
    WINDOW_MS: 60 * 60 * 1000, // 1 hour
    MAX_REQUESTS: parseInt(process.env.KEY_GENERATION_LIMIT) || 5,
    MESSAGE: 'Too many key generation requests. Please try again later.'
  },
  // Key validation rate limits
  VALIDATE: {
    WINDOW_MS: 60 * 60 * 1000, // 1 hour
    MAX_REQUESTS: parseInt(process.env.KEY_VALIDATION_LIMIT) || 60,
    MESSAGE: 'Too many validation requests. Please try again later.'
  },
  // HWID reset rate limits
  HWID_RESET: {
    WINDOW_MS: 24 * 60 * 60 * 1000, // 24 hours
    MAX_REQUESTS: parseInt(process.env.HWID_RESET_LIMIT) || 3,
    MESSAGE: 'Too many HWID reset requests. Please contact support.'
  }
};

// In-memory store for rate limiting
const rateLimitStore = new Map();

// Clean up old entries
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now - entry.timestamp > Math.max(
      RATE_LIMITS.GENERATE.WINDOW_MS,
      RATE_LIMITS.VALIDATE.WINDOW_MS,
      RATE_LIMITS.HWID_RESET.WINDOW_MS
    )) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000); // Clean up every 5 minutes

/**
 * Get rate limit key based on IP and type
 */
const getRateLimitKey = (ip, type, identifier = '') => {
  return `${ip}:${type}:${identifier}`;
};

/**
 * Check if a request is rate limited
 */
const checkRateLimit = (ip, type, identifier = '') => {
  const config = RATE_LIMITS[type] || RATE_LIMITS.VALIDATE;
  const key = getRateLimitKey(ip, type, identifier);
  const now = Date.now();
  
  // Get or create rate limit entry
  let entry = rateLimitStore.get(key) || {
    count: 0,
    resetTime: now + config.WINDOW_MS,
    timestamp: now
  };

  // Reset count if window has passed
  if (now - entry.timestamp > config.WINDOW_MS) {
    entry.count = 0;
    entry.resetTime = now + config.WINDOW_MS;
  }

  // Check if rate limit exceeded
  const remaining = Math.max(0, config.MAX_REQUESTS - entry.count - 1);
  const isRateLimited = entry.count >= config.MAX_REQUESTS;
  
  // Update the entry
  entry.count++;
  entry.timestamp = now;
  rateLimitStore.set(key, entry);

  return {
    isRateLimited,
    remaining,
    resetTime: entry.resetTime,
    limit: config.MAX_REQUESTS,
    message: isRateLimited ? config.MESSAGE : ''
  };
};

/**
 * Log rate limit event to the database
 */
const logRateLimitEvent = async (eventData) => {
  try {
    const { error } = await supabase
      .from('security_events')
      .insert([{
        event_type: 'rate_limit_triggered',
        ip_address: eventData.ip,
        user_agent: eventData.userAgent,
        metadata: {
          path: eventData.path,
          method: eventData.method,
          rate_limit_type: eventData.type,
          identifier: eventData.identifier
        }
      }]);

    if (error) {
      console.error('Failed to log rate limit event:', error);
    }
  } catch (error) {
    console.error('Error logging rate limit event:', error);
  }
};

/**
 * Rate limiting middleware for key system endpoints
 */
export const keyRateLimiter = (type = 'VALIDATE', identifierField = '') => {
  return async (req, res, next) => {
    try {
      const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
      const identifier = identifierField ? req.body[identifierField] || '' : '';
      
      // Check rate limit
      const rateLimit = checkRateLimit(ip, type, identifier);
      
      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': rateLimit.limit,
        'X-RateLimit-Remaining': rateLimit.remaining,
        'X-RateLimit-Reset': Math.ceil(rateLimit.resetTime / 1000)
      });

      // If rate limited, log the event and send error response
      if (rateLimit.isRateLimited) {
        await logRateLimitEvent({
          ip,
          type,
          path: req.path,
          method: req.method,
          userAgent: req.headers['user-agent'],
          identifier
        });

        return res.status(429).json({
          error: rateLimit.message,
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        });
      }

      // Proceed to the next middleware/route handler
      next();
    } catch (error) {
      console.error('Error in rate limiter:', error);
      // Fail open in case of errors to avoid blocking legitimate requests
      next();
    }
  };
};

/**
 * Middleware to check if a key is rate limited
 */
export const checkKeyRateLimit = async (req, res, next) => {
  try {
    const { keyId } = req.params;
    const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    
    // Check if this key has been used too many times
    const rateLimit = checkRateLimit(ip, 'VALIDATE', keyId);
    
    if (rateLimit.isRateLimited) {
      return res.status(429).json({
        error: rateLimit.message,
        retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
      });
    }
    
    next();
  } catch (error) {
    console.error('Error checking key rate limit:', error);
    next();
  }
};
