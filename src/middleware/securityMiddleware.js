const { verifyRequestSignature } = require('../utils/requestSigner');
const { rateLimiters, blockSuspiciousActivity } = require('./rateLimiter');
const { verifyHwid } = require('../utils/security');
const { SecurityLog } = require('../models');
const { getClientIp } = require('@supercharge/request-ip');

/**
 * Security middleware for Express.js that combines multiple security checks
 */
const securityMiddleware = (options = {}) => {
  const {
    // Whether to require request signing
    requireSignature = true,
    // Whether to verify HWID
    verifyHwid = true,
    // Rate limiting options
    rateLimit = {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    },
    // Additional security headers
    securityHeaders = true,
    // Whether to enable CORS
    enableCors = true,
  } = options;

  return [
    // 1. Suspicious activity detection (early in the chain)
    blockSuspiciousActivity,
    
    // 2. Security headers middleware
    securityHeaders && securityHeadersMiddleware(),
    
    // 3. CORS middleware
    enableCors && corsMiddleware(),
    
    // 4. Rate limiting with different rules for different endpoints
    (req, res, next) => {
      // Apply different rate limiters based on the endpoint
      let limiter;
      
      // Authentication endpoints
      if (req.path.startsWith('/api/auth')) {
        limiter = rateLimiters.auth;
      } 
      // Sensitive operations
      else if (req.path.startsWith('/api/admin') || req.path.startsWith('/api/security')) {
        limiter = rateLimiters.sensitive;
      }
      // Public API endpoints
      else if (req.path.startsWith('/api')) {
        limiter = rateLimiters.api;
      }
      // Public assets (higher limits)
      else {
        limiter = rateLimiters.public;
      }
      
      // Apply the selected rate limiter
      limiter.consume(getClientIp(req))
        .then(() => next())
        .catch(() => {
          logSecurityEvent({
            type: 'rate_limit_exceeded',
            ip: getClientIp(req),
            userAgent: req.headers['user-agent'],
            path: req.path,
            method: req.method,
            severity: 'high'
          });
          
          res.status(429).json({
            error: 'Too many requests',
            message: 'Rate limit exceeded. Please try again later.'
          });
        });
    },
    
    // 5. Request validation
    validateRequestMiddleware({ requireSignature, verifyHwid }),
    
    // 6. Error handler
    securityErrorHandler,
    
    // Filter out any falsy values (disabled middlewares)
  ].filter(Boolean);
};

/**
 * Middleware to set security headers
 */
function securityHeadersMiddleware() {
  return (req, res, next) => {
    // Set security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'");
    
    // Remove X-Powered-By header
    res.removeHeader('X-Powered-By');
    
    next();
  };
}

/**
 * CORS middleware with secure defaults
 */
function corsMiddleware() {
  return (req, res, next) => {
    const allowedOrigins = [
      'http://localhost:3000',
      'https://your-production-domain.com',
    ];
    
    const origin = req.headers.origin;
    
    if (allowedOrigins.includes(origin)) {
      res.setHeader('Access-Control-Allow-Origin', origin);
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Device-Fingerprint, X-Request-Signature, X-Request-Timestamp');
      res.setHeader('Access-Control-Allow-Credentials', 'true');
    }
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    next();
  };
}

/**
 * Request validation middleware
 */
function validateRequestMiddleware(options = {}) {
  const { requireSignature = true, verifyHwid = true } = options;
  
  return async (req, res, next) => {
    try {
      // 1. Verify request signature if required
      if (requireSignature) {
        const isValid = await verifyRequestSignature(req);
        if (!isValid) {
          return res.status(401).json({ 
            error: 'Invalid request signature',
            code: 'INVALID_SIGNATURE'
          });
        }
      }
      
      // 2. Verify HWID if required
      if (verifyHwid && req.headers['x-device-fingerprint']) {
        const hwid = req.headers['x-device-fingerprint'];
        const isHwidValid = await verifyHwid(hwid);
        
        if (!isHwidValid) {
          return res.status(403).json({ 
            error: 'Invalid device fingerprint',
            code: 'INVALID_DEVICE'
          });
        }
        
        // Attach the validated HWID to the request for later use
        req.deviceFingerprint = hwid;
      }
      
      // 3. Additional security checks can be added here
      // For example, check for suspicious user agents, IP reputation, etc.
      
      next();
    } catch (error) {
      console.error('Request validation error:', error);
      next(error);
    }
  };
}

/**
 * Log a security event to the database
 */
async function logSecurityEvent(eventData) {
  try {
    const event = new SecurityLog({
      type: eventData.type,
      ip: eventData.ip,
      userAgent: eventData.userAgent,
      path: eventData.path,
      method: eventData.method,
      details: eventData.details || {},
      severity: eventData.severity || 'medium',
      timestamp: new Date()
    });
    
    await event.save();
  } catch (error) {
    console.error('Error logging security event:', error);
  }
}

/**
 * Error handler for security-related errors
 */
async function securityErrorHandler(err, req, res, next) {
  // Handle rate limit exceeded errors
  if (err.status === 429) {
    const retryAfter = res.get('Retry-After') || 60;
    
    return res.status(429).json({
      error: 'Too many requests, please try again later',
      retryAfter: parseInt(retryAfter, 10),
      code: 'RATE_LIMIT_EXCEEDED'
    });
  }
  
  // Handle other security-related errors
  if (err.code === 'INVALID_SIGNATURE' || err.code === 'INVALID_DEVICE') {
    return res.status(403).json({
      error: err.message || 'Access denied',
      code: err.code || 'ACCESS_DENIED'
    });
  }
  
  // Pass to the next error handler
  next(err);
}

/**
 * Middleware to check if the user has the required permissions
 * @param {Array} requiredPermissions - Array of required permission strings
 * @returns {Function} Express middleware function
 */
function checkPermissions(requiredPermissions = []) {
  return (req, res, next) => {
    try {
      // Get user permissions from the request (attached by auth middleware)
      const userPermissions = req.user?.permissions || [];
      
      // Check if user has all required permissions
      const hasAllPermissions = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );
      
      if (!hasAllPermissions) {
        return res.status(403).json({
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: requiredPermissions,
          has: userPermissions
        });
      }
      
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        error: 'Internal server error during permission check',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
}

/**
 * Middleware to check if the request is coming from a trusted IP
 * @param {Array} trustedIps - Array of trusted IP addresses or CIDR ranges
 * @returns {Function} Express middleware function
 */
function ipWhitelist(trustedIps = []) {
  return (req, res, next) => {
    try {
      // Get the client IP address
      const clientIp = req.ip || 
                      req.connection.remoteAddress || 
                      req.socket.remoteAddress ||
                      (req.connection.socket ? req.connection.socket.remoteAddress : null);
      
      // If no trusted IPs are provided, allow all
      if (trustedIps.length === 0) {
        return next();
      }
      
      // Check if the client IP is in the whitelist
      const isAllowed = trustedIps.some(ip => {
        // Simple IP match
        if (ip === clientIp) return true;
        
        // TODO: Add CIDR range matching if needed
        // Example: '***********/24' should match '***********', '***********', etc.
        
        return false;
      });
      
      if (!isAllowed) {
        // Log the unauthorized access attempt
        console.warn(`Unauthorized IP access attempt from ${clientIp}`);
        
        return res.status(403).json({
          error: 'Access denied',
          code: 'IP_NOT_ALLOWED'
        });
      }
      
      next();
    } catch (error) {
      console.error('IP whitelist check error:', error);
      res.status(500).json({
        error: 'Internal server error during IP check',
        code: 'IP_CHECK_ERROR'
      });
    }
  };
}

// Middleware to track and log security events
module.exports.securityEventLogger = (req, res, next) => {
  // Log the start time of the request
  const startTime = Date.now();
  
  // Override the end method to log the completion of the request
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    // Log the security event
    logSecurityEvent({
      type: 'request_completed',
      ip: getClientIp(req),
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method,
      statusCode: res.statusCode,
      responseTime,
      details: {
        params: req.params,
        query: req.query,
        body: req.body,
        headers: {
          // Only log non-sensitive headers
          'content-type': req.headers['content-type'],
          'content-length': req.headers['content-length'],
          'referer': req.headers['referer'],
          'x-requested-with': req.headers['x-requested-with'],
        }
      },
      severity: res.statusCode >= 400 ? 'medium' : 'low'
    });
    
    // Call the original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

module.exports = {
  securityMiddleware,
  checkPermissions,
  ipWhitelist,
  securityHeadersMiddleware,
  corsMiddleware,
  validateRequestMiddleware,
  securityErrorHandler,
  logSecurityEvent
};
