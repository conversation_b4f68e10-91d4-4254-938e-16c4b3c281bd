import React from 'react';
import { motion } from 'framer-motion';
import { Slot } from '@radix-ui/react-slot';

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  icon: Icon,
  iconPosition = 'left',
  isLoading = false,
  asChild = false,
  ...props
}) => {
  const Comp = asChild ? Slot : motion.button;

  // Base button styles
  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-lg transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
  
  // Variant styles
  const variants = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary/50',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/90 focus:ring-secondary/50',
    outline: 'border border-input bg-transparent hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'text-primary underline-offset-4 hover:underline',
  };

  // Size styles
  const sizes = {
    sm: 'h-9 px-3 text-sm',
    md: 'h-10 py-2 px-4 text-base',
    lg: 'h-12 px-6 text-lg',
    icon: 'h-10 w-10 p-0',
  };

  // Icon sizes
  const iconSizes = {
    sm: 'h-5 w-5',
    md: 'h-6 w-6',
    lg: 'h-7 w-7',
  };

  // Determine if this is an icon-only button
  const hasChildren = React.Children.count(children) > 0;
  const isIconOnly = !hasChildren && Icon;
  const sizeClass = isIconOnly ? 'icon' : size;
  
  // Get icon size based on button size
  const iconSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'md';
  const iconClassName = `${iconSizes[iconSize]} ${hasChildren ? (iconPosition === 'left' ? 'mr-2' : 'ml-2') : ''}`;

  // Render the button
  return (
    <Comp
      whileTap={!asChild ? { scale: 0.98 } : {}}
      className={`${baseStyles} ${variants[variant]} ${sizes[sizeClass]} ${className}`}
      disabled={isLoading}
      {...props}
    >
      {isLoading ? (
        <svg
          className={`animate-spin ${iconSizes[iconSize]}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      ) : (
        <>
          {Icon && iconPosition === 'left' && (
            <span className={iconClassName}>
              <Icon />
            </span>
          )}
          {children}
          {Icon && iconPosition === 'right' && (
            <span className={iconClassName}>
              <Icon />
            </span>
          )}
        </>
      )}
    </Comp>
  );
};

export { Button };
