import React from 'react';
import { motion } from 'framer-motion';

const CardComponent = ({
  children,
  className = '',
  hoverEffect = 'scale',
  variant = 'default',
  ...props
}) => {
  const baseStyles = 'rounded-xl border bg-card text-card-foreground shadow-sm transition-all';
  
  const variants = {
    default: 'bg-card',
    glass: 'glass border border-white/10',
    primary: 'bg-primary/5 border-primary/20',
    secondary: 'bg-secondary/5 border-secondary/20',
  };

  const hoverEffects = {
    scale: 'hover:scale-[1.02]',
    shadow: 'hover:shadow-md',
    lift: 'hover:-translate-y-1',
    none: '',
  };

  return (
    <motion.div
      whileHover={hoverEffect !== 'none' ? { y: -4 } : {}}
      className={`${baseStyles} ${variants[variant]} ${hoverEffects[hoverEffect]} ${className}`}
      {...props}
    >
      {children}
    </motion.div>
  );
};

const CardHeader = ({ className = '', ...props }) => (
  <div
    className={`flex flex-col space-y-1.5 p-6 ${className}`}
    {...props}
  />
);

const CardTitle = ({ className = '', ...props }) => (
  <h3
    className={`text-xl font-semibold leading-none tracking-tight ${className}`}
    {...props}
  />
);

const CardDescription = ({ className = '', ...props }) => (
  <p
    className={`text-sm text-muted-foreground ${className}`}
    {...props}
  />
);

const CardContent = ({ className = '', ...props }) => (
  <div className={`p-6 pt-0 ${className}`} {...props} />
);

const CardFooter = ({ className = '', ...props }) => (
  <div
    className={`flex items-center p-6 pt-0 ${className}`}
    {...props}
  />
);

export const Card = Object.assign(CardComponent, {
  Header: CardHeader,
  Title: CardTitle,
  Description: CardDescription,
  Content: CardContent,
  Footer: CardFooter
});
