import { motion, AnimatePresence } from 'framer-motion';
import { FiX, FiClock } from 'react-icons/fi';
import { Button } from '../ui/Button';

const UpdateLogModal = ({ isOpen, onClose, script }) => {
  if (!script) return null;

  // Sample update log data - in a real app, this would come from an API
  const updateLogs = [
    {
      version: '1.2.0',
      date: '2023-06-15',
      changes: [
        'Added new ESP features',
        'Improved performance',
        'Fixed minor bugs',
      ],
    },
    {
      version: '1.1.0',
      date: '2023-05-28',
      changes: [
        'Added auto-update functionality',
        'Improved UI/UX',
        'Fixed compatibility issues',
      ],
    },
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="relative w-full max-w-2xl max-h-[80vh] overflow-y-auto bg-background rounded-lg shadow-xl border border-border"
          >
            <div className="sticky top-0 z-10 flex items-center justify-between p-4 border-b border-border bg-background/80 backdrop-blur-sm">
              <h2 className="text-xl font-semibold">Update Log - {script.title}</h2>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={onClose}
                className="text-muted-foreground hover:text-foreground"
              >
                <FiX className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="p-6 space-y-6">
              <div className="space-y-1">
                <h3 className="text-lg font-medium">Current Version: {updateLogs[0].version}</h3>
                <p className="text-sm text-muted-foreground">Last updated: {updateLogs[0].date}</p>
              </div>

              <div className="space-y-6">
                {updateLogs.map((log, index) => (
                  <div key={log.version} className="relative pl-6 pb-6 border-l-2 border-border">
                    <div className="absolute w-3 h-3 rounded-full bg-primary -left-1.5 top-1.5" />
                    <div className="flex items-center space-x-2">
                      <h4 className="text-base font-medium">v{log.version}</h4>
                      <span className="text-sm text-muted-foreground">{log.date}</span>
                    </div>
                    <ul className="mt-2 space-y-1.5 text-sm">
                      {log.changes.map((change, i) => (
                        <li key={i} className="flex items-start">
                          <span className="mr-2">•</span>
                          <span>{change}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>

            <div className="sticky bottom-0 flex justify-end p-4 bg-background/80 backdrop-blur-sm border-t border-border">
              <Button onClick={onClose}>Close</Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default UpdateLogModal;
