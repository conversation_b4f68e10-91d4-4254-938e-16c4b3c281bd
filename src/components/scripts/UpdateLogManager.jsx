import { useState, useEffect } from 'react';
import { FiPlus, FiX, FiSave, FiTrash2, FiEdit2 } from 'react-icons/fi';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { toast } from 'react-hot-toast';

const UpdateLogManager = ({ scriptId, updateLogs: initialLogs = [], onSave }) => {
  const [updateLogs, setUpdateLogs] = useState(initialLogs);
  const [editingLog, setEditingLog] = useState(null);
  const [newLog, setNewLog] = useState({ version: '', changes: [''] });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize with current date
  useEffect(() => {
    if (!editingLog && !newLog.date) {
      setNewLog(prev => ({
        ...prev,
        date: new Date().toISOString().split('T')[0]
      }));
    }
  }, [editingLog]);

  const handleAddChange = () => {
    setNewLog(prev => ({
      ...prev,
      changes: [...prev.changes, '']
    }));
  };

  const handleChangeUpdate = (index, value) => {
    setNewLog(prev => ({
      ...prev,
      changes: prev.changes.map((change, i) => (i === index ? value : change))
    }));
  };

  const handleRemoveChange = (index) => {
    setNewLog(prev => ({
      ...prev,
      changes: prev.changes.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!newLog.version || !newLog.changes.some(change => change.trim())) {
      toast.error('Version and at least one change are required');
      return;
    }

    setIsSubmitting(true);
    try {
      const logToSave = {
        ...newLog,
        date: newLog.date || new Date().toISOString().split('T')[0],
        changes: newLog.changes.filter(change => change.trim())
      };

      const updatedLogs = editingLog
        ? updateLogs.map(log => (log.id === editingLog.id ? logToSave : log))
        : [...updateLogs, { ...logToSave, id: Date.now().toString() }];

      // Call the onSave prop with the updated logs
      await onSave(scriptId, updatedLogs);
      
      setUpdateLogs(updatedLogs);
      setNewLog({ version: '', changes: [''], date: '' });
      setEditingLog(null);
      
      toast.success(`Update log ${editingLog ? 'updated' : 'added'} successfully`);
    } catch (error) {
      console.error('Error saving update log:', error);
      toast.error(`Failed to ${editingLog ? 'update' : 'add'} update log`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditLog = (log) => {
    setEditingLog(log);
    setNewLog({
      version: log.version,
      changes: [...log.changes, ''], // Add empty field for new change
      date: log.date
    });
  };

  const handleDeleteLog = async (logId) => {
    if (!window.confirm('Are you sure you want to delete this update log?')) {
      return;
    }

    try {
      const updatedLogs = updateLogs.filter(log => log.id !== logId);
      await onSave(scriptId, updatedLogs);
      setUpdateLogs(updatedLogs);
      toast.success('Update log deleted successfully');
    } catch (error) {
      console.error('Error deleting update log:', error);
      toast.error('Failed to delete update log');
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h3 className="text-lg font-medium">Update Logs</h3>
        <p className="text-sm text-muted-foreground">
          Track updates and changes to this script
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="version" className="block text-sm font-medium mb-1">
              Version
            </label>
            <Input
              id="version"
              value={newLog.version}
              onChange={(e) => setNewLog(prev => ({ ...prev, version: e.target.value }))}
              placeholder="e.g., 1.0.0"
              required
            />
          </div>
          <div>
            <label htmlFor="date" className="block text-sm font-medium mb-1">
              Date
            </label>
            <Input
              id="date"
              type="date"
              value={newLog.date || ''}
              onChange={(e) => setNewLog(prev => ({ ...prev, date: e.target.value }))}
              required
            />
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="block text-sm font-medium">Changes</label>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleAddChange}
              className="text-sm"
            >
              <FiPlus className="mr-1 h-4 w-4" /> Add Change
            </Button>
          </div>
          
          <div className="space-y-2">
            {newLog.changes.map((change, index) => (
              <div key={index} className="flex items-start gap-2">
                <div className="flex-1">
                  <Input
                    value={change}
                    onChange={(e) => handleChangeUpdate(index, e.target.value)}
                    placeholder={`Change ${index + 1}`}
                  />
                </div>
                {newLog.changes.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRemoveChange(index)}
                    className="text-destructive hover:bg-destructive/10"
                  >
                    <FiX className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-2">
          {editingLog && (
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setEditingLog(null);
                setNewLog({ version: '', changes: [''], date: '' });
              }}
            >
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <FiLoader className="animate-spin mr-2 h-4 w-4" />
                {editingLog ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              <>
                <FiSave className="mr-2 h-4 w-4" />
                {editingLog ? 'Update Log' : 'Add Log'}
              </>
            )}
          </Button>
        </div>
      </form>

      {updateLogs.length > 0 && (
        <div className="space-y-4 pt-4">
          <h4 className="text-sm font-medium">Previous Updates</h4>
          <div className="space-y-4">
            {updateLogs.map((log) => (
              <div key={log.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">v{log.version}</h4>
                    <p className="text-sm text-muted-foreground">
                      {new Date(log.date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditLog(log)}
                      className="h-8 w-8"
                    >
                      <FiEdit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteLog(log.id)}
                      className="h-8 w-8 text-destructive hover:bg-destructive/10"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <ul className="mt-2 space-y-1">
                  {log.changes.map((change, i) => (
                    <li key={i} className="text-sm flex items-start">
                      <span className="mr-2">•</span>
                      <span>{change}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UpdateLogManager;
