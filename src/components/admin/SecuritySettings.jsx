import React, { useState, useEffect } from 'react';
import { Card, Switch, Button, Input, Select, message, Table, Tag, Space, Alert, Tabs, List, Divider, Form } from 'antd';
import { 
  SafetyCertificateOutlined, 
  WarningOutlined, 
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  KeyOutlined,
  DeleteOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

const SecuritySettings = () => {
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState({
    twoFactorAuth: false,
    loginAttempts: 5,
    sessionTimeout: 30,
    ipWhitelist: [],
    rateLimiting: true,
    suspiciousActivityDetection: true,
    autoBlockSuspicious: false,
  });

  const [securityLogs, setSecurityLogs] = useState([]);
  const [activeTab, setActiveTab] = useState('general');
  const [ipAddress, setIpAddress] = useState('');
  const [apiKeys, setApiKeys] = useState([]);
  const [newKeyName, setNewKeyName] = useState('');
  const [newKeyPermissions, setNewKeyPermissions] = useState(['read']);

  // Load initial data
  useEffect(() => {
    fetchSecuritySettings();
    fetchSecurityLogs();
    fetchApiKeys();
  }, []);

  // Mock API calls
  const fetchSecuritySettings = async () => {
    // Simulate API call
    setTimeout(() => {
      setSettings({
        twoFactorAuth: true,
        loginAttempts: 5,
        sessionTimeout: 30,
        ipWhitelist: ['***********', '********'],
        rateLimiting: true,
        suspiciousActivityDetection: true,
        autoBlockSuspicious: false,
      });
      setLoading(false);
    }, 500);
  };

  const fetchSecurityLogs = async () => {
    // Simulate API call
    setTimeout(() => {
      setSecurityLogs([
        {
          id: '1',
          timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
          type: 'failed_login',
          ip: '***********00',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
          details: 'Invalid credentials',
          severity: 'high'
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
          type: 'rate_limit',
          ip: '***********01',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
          details: 'Too many requests',
          severity: 'medium'
        },
      ]);
    }, 500);
  };

  const fetchApiKeys = async () => {
    // Simulate API call
    setTimeout(() => {
      setApiKeys([
        {
          id: 'key_123',
          name: 'Production API',
          keyPrefix: 'sk_prod_',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
          lastUsed: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
          permissions: ['read', 'write'],
          isActive: true
        },
        {
          id: 'key_456',
          name: 'Development',
          keyPrefix: 'sk_dev_',
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
          lastUsed: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
          permissions: ['read'],
          isActive: true
        },
      ]);
    }, 500);
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('Security settings updated successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      message.error('Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  const addIpToWhitelist = () => {
    if (!ipAddress) {
      message.warning('Please enter an IP address');
      return;
    }
    
    const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    
    if (!ipRegex.test(ipAddress)) {
      message.error('Please enter a valid IP address');
      return;
    }
    
    if (settings.ipWhitelist.includes(ipAddress)) {
      message.warning('This IP is already in the whitelist');
      return;
    }
    
    handleSettingChange('ipWhitelist', [...settings.ipWhitelist, ipAddress]);
    setIpAddress('');
    message.success('IP address added to whitelist');
  };

  const removeIpFromWhitelist = (ip) => {
    handleSettingChange(
      'ipWhitelist', 
      settings.ipWhitelist.filter(item => item !== ip)
    );
    message.success('IP address removed from whitelist');
  };

  const generateApiKey = async () => {
    if (!newKeyName) {
      message.warning('Please enter a name for the API key');
      return;
    }
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newKey = {
        id: `key_${Math.random().toString(36).substr(2, 9)}`,
        name: newKeyName,
        key: `sk_${Math.random().toString(36).substr(2, 32)}`,
        permissions: newKeyPermissions,
        createdAt: new Date().toISOString(),
        isActive: true
      };
      
      message.success(
        <div>
          <p>API key created successfully!</p>
          <p>Please copy this key now as it won't be shown again:</p>
          <Input.Password 
            value={newKey.key} 
            readOnly 
            style={{ marginTop: 8 }}
            onClick={(e) => e.target.select()}
          />
        </div>,
        0 // No auto-close
      );
      
      setNewKeyName('');
      setNewKeyPermissions(['read']);
      fetchApiKeys();
    } catch (error) {
      console.error('Error generating API key:', error);
      message.error('Failed to generate API key');
    }
  };

  const revokeApiKey = async (keyId) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('API key revoked successfully');
      fetchApiKeys();
    } catch (error) {
      console.error('Error revoking API key:', error);
      message.error('Failed to revoke API key');
    }
  };

  const renderSeverityTag = (severity) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'blue'
    };
    
    return <Tag color={colorMap[severity] || 'default'}>{severity.toUpperCase()}</Tag>;
  };

  const renderLogType = (type) => {
    const typeMap = {
      failed_login: { text: 'Failed Login', icon: <CloseCircleOutlined />, color: 'red' },
      login_success: { text: 'Login Success', icon: <CheckCircleOutlined />, color: 'green' },
      rate_limit: { text: 'Rate Limit', icon: <ClockCircleOutlined />, color: 'orange' },
    };
    
    const typeInfo = typeMap[type] || { text: type, icon: null, color: 'default' };
    
    return (
      <Tag icon={typeInfo.icon} color={typeInfo.color}>
        {typeInfo.text}
      </Tag>
    );
  };

  const columns = [
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp) => new Date(timestamp).toLocaleString(),
      sorter: (a, b) => new Date(a.timestamp) - new Date(b.timestamp),
      defaultSortOrder: 'descend',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: renderLogType,
    },
    {
      title: 'IP Address',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render: renderSeverityTag,
    },
    {
      title: 'Details',
      dataIndex: 'details',
      key: 'details',
    },
  ];

  const apiKeyColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <>
          <div>{text}</div>
          <div style={{ fontSize: '12px', color: '#888' }}>
            {record.keyPrefix}•••••••••••••••••
          </div>
        </>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Last Used',
      dataIndex: 'lastUsed',
      key: 'lastUsed',
      render: (date) => date ? new Date(date).toLocaleString() : 'Never',
    },
    {
      title: 'Permissions',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => (
        <span>
          {permissions.map(permission => (
            <Tag color="blue" key={permission}>
              {permission}
            </Tag>
          ))}
        </span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Revoked'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          {record.isActive ? (
            <Button 
              type="link" 
              danger 
              onClick={() => revokeApiKey(record.id)}
            >
              Revoke
            </Button>
          ) : (
            <span>Revoked</span>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="security-settings">
      <h2>
        <SafetyCertificateOutlined /> Security Settings
      </h2>
      
      <Alert
        message="Security is a top priority"
        description="These settings help protect your account from unauthorized access and potential threats."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Tabs 
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
      >
        <TabPane tab="General Settings" key="general">
          <Card 
            title="Authentication Settings"
            style={{ marginBottom: 24 }}
            loading={loading}
          >
            <Form layout="vertical">
              <Form.Item label="Two-Factor Authentication" extra="Add an extra layer of security">
                <Switch 
                  checked={settings.twoFactorAuth}
                  onChange={(checked) => handleSettingChange('twoFactorAuth', checked)}
                />
                <span style={{ marginLeft: 8 }}>
                  {settings.twoFactorAuth ? 'Enabled' : 'Disabled'}
                </span>
              </Form.Item>

              <Form.Item label="Failed Login Attempts" extra="Number of failed attempts before lockout">
                <Input 
                  type="number" 
                  min={1} 
                  max={10}
                  value={settings.loginAttempts}
                  onChange={(e) => handleSettingChange('loginAttempts', parseInt(e.target.value) || 1)}
                  style={{ width: 100 }}
                />
              </Form.Item>

              <Form.Item label="Session Timeout" extra="Inactivity period before automatic logout (minutes)">
                <Input 
                  type="number" 
                  min={1} 
                  max={1440}
                  value={settings.sessionTimeout}
                  onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value) || 30)}
                  style={{ width: 100 }}
                />
              </Form.Item>
            </Form>
          </Card>

          <Card 
            title="IP Whitelist"
            style={{ marginBottom: 24 }}
            loading={loading}
          >
            <div style={{ marginBottom: 16 }}>
              <Space.Compact style={{ width: '100%', maxWidth: 400 }}>
                <Input 
                  placeholder="Enter IP address (e.g., ***********)" 
                  value={ipAddress}
                  onChange={(e) => setIpAddress(e.target.value)}
                  onPressEnter={addIpToWhitelist}
                />
                <Button 
                  type="primary" 
                  onClick={addIpToWhitelist}
                >
                  Add IP
                </Button>
              </Space.Compact>
              <div style={{ fontSize: 12, color: '#888', marginTop: 8 }}>
                Only allow access from these IP addresses. Leave empty to allow all.
              </div>
            </div>

            {settings.ipWhitelist.length > 0 ? (
              <List
                size="small"
                bordered
                dataSource={settings.ipWhitelist}
                renderItem={(ip) => (
                  <List.Item
                    actions={[
                      <Button 
                        type="text" 
                        danger 
                        onClick={() => removeIpFromWhitelist(ip)}
                        icon={<DeleteOutlined />}
                      />
                    ]}
                  >
                    <Text code>{ip}</Text>
                  </List.Item>
                )}
                style={{ maxWidth: 500 }}
              />
            ) : (
              <Alert 
                message="No IP addresses in whitelist" 
                type="warning"
                showIcon
                style={{ maxWidth: 500 }}
              />
            )}
          </Card>

          <Card 
            title="Advanced Security"
            loading={loading}
            extra={
              <Button 
                type="primary" 
                onClick={saveSettings}
                loading={loading}
              >
                Save Settings
              </Button>
            }
          >
            <Form layout="vertical">
              <Form.Item label="Rate Limiting" extra="Prevent abuse by limiting request rates">
                <Switch 
                  checked={settings.rateLimiting}
                  onChange={(checked) => handleSettingChange('rateLimiting', checked)}
                />
              </Form.Item>

              <Form.Item label="Suspicious Activity Detection" extra="Monitor for unusual behavior">
                <Switch 
                  checked={settings.suspiciousActivityDetection}
                  onChange={(checked) => handleSettingChange('suspiciousActivityDetection', checked)}
                />
              </Form.Item>

              <Form.Item label="Auto-Block Suspicious IPs" extra="Automatically block IPs with suspicious activity">
                <Switch 
                  checked={settings.autoBlockSuspicious}
                  onChange={(checked) => handleSettingChange('autoBlockSuspicious', checked)}
                  disabled={!settings.suspiciousActivityDetection}
                />
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab="API Keys" key="api">
          <Card 
            title="Generate New API Key"
            style={{ marginBottom: 24 }}
            loading={loading}
          >
            <Form layout="vertical" style={{ maxWidth: 600 }}>
              <Form.Item label="Key Name" required>
                <Input 
                  placeholder="e.g., Production Server" 
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                />
              </Form.Item>
              
              <Form.Item label="Permissions" required>
                <Select
                  mode="multiple"
                  placeholder="Select permissions"
                  value={newKeyPermissions}
                  onChange={setNewKeyPermissions}
                  style={{ width: '100%' }}
                >
                  <Option value="read">Read</Option>
                  <Option value="write">Write</Option>
                  <Option value="admin">Admin</Option>
                </Select>
              </Form.Item>
              
              <Form.Item>
                <Button 
                  type="primary" 
                  onClick={generateApiKey}
                  icon={<KeyOutlined />}
                >
                  Generate API Key
                </Button>
              </Form.Item>
            </Form>
          </Card>

          <Card title="Your API Keys" loading={loading}>
            <Table 
              columns={apiKeyColumns} 
              dataSource={apiKeys} 
              rowKey="id"
              pagination={false}
            />
            
            <div style={{ marginTop: 16, color: '#888', fontSize: 12 }}>
              <InfoCircleOutlined /> API keys have full access to your account. Keep them secure and never share them in client-side code.
            </div>
          </Card>
        </TabPane>

        <TabPane tab="Security Logs" key="logs">
          <Card 
            title="Recent Security Events"
            loading={loading}
            extra={
              <Button 
                icon={<ReloadOutlined />}
                onClick={fetchSecurityLogs}
              >
                Refresh
              </Button>
            }
          >
            <Table 
              columns={columns} 
              dataSource={securityLogs} 
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SecuritySettings;
