import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, Select, Tag, Space, Tooltip, Progress } from 'antd';
import { EyeOutlined, DeleteOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { toast } from 'react-hot-toast';
import keyService from '../../services/keyService';
import monitoringService from '../../services/monitoringService';

const { Option } = Select;
const { confirm } = Modal;

const KeyManagementDashboard = () => {
  const [keys, setKeys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState(null);
  const [selectedKey, setSelectedKey] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    loadKeys();
    loadMetrics();
    
    // Refresh data every 30 seconds
    const interval = setInterval(() => {
      loadKeys();
      loadMetrics();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const loadKeys = async () => {
    try {
      const response = await keyService.getAllKeys();
      setKeys(response.data || []);
    } catch (error) {
      toast.error('Failed to load keys');
      console.error('Error loading keys:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMetrics = async () => {
    try {
      const metricsData = await monitoringService.getSecurityMetrics('24h');
      setMetrics(metricsData);
    } catch (error) {
      console.error('Error loading metrics:', error);
    }
  };

  const handleCreateKey = async (values) => {
    try {
      await keyService.createKey(values);
      toast.success('Key created successfully');
      setIsModalVisible(false);
      form.resetFields();
      loadKeys();
    } catch (error) {
      toast.error('Failed to create key');
      console.error('Error creating key:', error);
    }
  };

  const handleDeleteKey = (keyId) => {
    confirm({
      title: 'Are you sure you want to delete this key?',
      icon: <ExclamationCircleOutlined />,
      content: 'This action cannot be undone. The key will be immediately deactivated.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await keyService.deleteKey(keyId);
          toast.success('Key deleted successfully');
          loadKeys();
        } catch (error) {
          toast.error('Failed to delete key');
          console.error('Error deleting key:', error);
        }
      },
    });
  };

  const handleRotateKey = (keyId) => {
    confirm({
      title: 'Rotate API Key?',
      icon: <ReloadOutlined />,
      content: 'This will generate a new key and invalidate the current one. Make sure to update your applications.',
      okText: 'Rotate Key',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await keyService.rotateKey(keyId);
          toast.success('Key rotated successfully');
          loadKeys();
        } catch (error) {
          toast.error('Failed to rotate key');
          console.error('Error rotating key:', error);
        }
      },
    });
  };

  const getStatusTag = (key) => {
    if (!key.is_active) return <Tag color="red">Inactive</Tag>;
    if (new Date(key.expires_at) < new Date()) return <Tag color="orange">Expired</Tag>;
    return <Tag color="green">Active</Tag>;
  };

  const getUsageProgress = (key) => {
    if (!key.max_uses) return null;
    const percentage = Math.round((key.use_count / key.max_uses) * 100);
    return (
      <Progress 
        percent={percentage} 
        size="small" 
        status={percentage >= 90 ? 'exception' : 'normal'}
        showInfo={false}
      />
    );
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.fingerprint}</div>
        </div>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => getStatusTag(record),
    },
    {
      title: 'Service Type',
      dataIndex: 'service_type',
      key: 'service_type',
      render: (type) => <Tag>{type}</Tag>,
    },
    {
      title: 'Usage',
      key: 'usage',
      render: (_, record) => (
        <div>
          <div className="text-sm">
            {record.use_count || 0}
            {record.max_uses && ` / ${record.max_uses}`}
          </div>
          {getUsageProgress(record)}
        </div>
      ),
    },
    {
      title: 'Last Used',
      dataIndex: 'last_used_at',
      key: 'last_used_at',
      render: (date) => date ? new Date(date).toLocaleDateString() : 'Never',
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (date) => date ? new Date(date).toLocaleDateString() : 'Never',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => setSelectedKey(record)}
            />
          </Tooltip>
          <Tooltip title="Rotate Key">
            <Button 
              icon={<ReloadOutlined />} 
              size="small"
              onClick={() => handleRotateKey(record.id)}
            />
          </Tooltip>
          <Tooltip title="Delete Key">
            <Button 
              icon={<DeleteOutlined />} 
              size="small" 
              danger
              onClick={() => handleDeleteKey(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Metrics Overview */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <div className="text-2xl font-bold">{metrics.keyUsageStats.totalKeys}</div>
            <div className="text-gray-500">Total Keys</div>
          </Card>
          <Card>
            <div className="text-2xl font-bold text-green-600">{metrics.keyUsageStats.activeKeys}</div>
            <div className="text-gray-500">Active Keys</div>
          </Card>
          <Card>
            <div className="text-2xl font-bold">{metrics.keyUsageStats.totalUsage}</div>
            <div className="text-gray-500">Total Usage</div>
          </Card>
          <Card>
            <div className="text-2xl font-bold">{metrics.totalEvents}</div>
            <div className="text-gray-500">Events (24h)</div>
          </Card>
        </div>
      )}

      {/* Keys Table */}
      <Card 
        title="API Keys" 
        extra={
          <Button 
            type="primary" 
            onClick={() => setIsModalVisible(true)}
          >
            Create New Key
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={keys}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>

      {/* Create Key Modal */}
      <Modal
        title="Create New API Key"
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateKey}
        >
          <Form.Item
            name="name"
            label="Key Name"
            rules={[{ required: true, message: 'Please enter a name for this key' }]}
          >
            <Input placeholder="e.g., Production API Key" />
          </Form.Item>

          <Form.Item
            name="service_type"
            label="Service Type"
            rules={[{ required: true, message: 'Please select a service type' }]}
          >
            <Select placeholder="Select service type">
              <Option value="linkvertise">Linkvertise</Option>
              <Option value="shrinkme">ShrinkMe</Option>
              <Option value="standard">Standard</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="expires_in_days"
            label="Expires In (Days)"
          >
            <Input type="number" placeholder="30" />
          </Form.Item>

          <Form.Item
            name="max_uses"
            label="Maximum Uses (Optional)"
          >
            <Input type="number" placeholder="Unlimited" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Create Key
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Key Details Modal */}
      {selectedKey && (
        <Modal
          title={`Key Details: ${selectedKey.name}`}
          visible={!!selectedKey}
          onCancel={() => setSelectedKey(null)}
          footer={[
            <Button key="close" onClick={() => setSelectedKey(null)}>
              Close
            </Button>
          ]}
        >
          <div className="space-y-4">
            <div>
              <strong>Fingerprint:</strong> {selectedKey.fingerprint}
            </div>
            <div>
              <strong>Created:</strong> {new Date(selectedKey.created_at).toLocaleString()}
            </div>
            <div>
              <strong>Last Used:</strong> {selectedKey.last_used_at ? new Date(selectedKey.last_used_at).toLocaleString() : 'Never'}
            </div>
            <div>
              <strong>Usage Count:</strong> {selectedKey.use_count || 0}
            </div>
            {selectedKey.last_used_hwid && (
              <div>
                <strong>Last HWID:</strong> {selectedKey.last_used_hwid.slice(0, 16)}...
              </div>
            )}
          </div>
        </Modal>
      )}
    </div>
  );
};

export default KeyManagementDashboard;
