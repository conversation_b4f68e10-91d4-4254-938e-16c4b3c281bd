import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  Tooltip,
  Popconfirm,
  message,
  Card,
  Typography,
  Alert
} from 'antd';
import {
  PlusOutlined,
  KeyOutlined,
  DeleteOutlined,
  CopyOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons';
import { useSecurity } from '../../context/SecurityContext';

const { Text } = Typography;
const { Option } = Select;

const ApiKeys = () => {
  const { securityService } = useSecurity();
  const [apiKeys, setApiKeys] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [form] = Form.useForm();

  // Load API keys
  useEffect(() => {
    const fetchApiKeys = async () => {
      try {
        setLoading(true);
        const keys = await securityService.apiKeys.list();
        setApiKeys(keys);
      } catch (error) {
        console.error('Failed to fetch API keys:', error);
        message.error('Failed to load API keys');
      } finally {
        setLoading(false);
      }
    };
    fetchApiKeys();
  }, [securityService]);

  const handleGenerateKey = async (values) => {
    try {
      const key = await securityService.apiKeys.generate(values.name, values.permissions);
      setApiKeys(prev => [key, ...prev]);
      form.resetFields();
      setIsModalVisible(false);
      Modal.info({
        title: 'API Key Generated',
        content: (
          <div>
            <p>Your new API key has been generated. Please copy it now as it won't be shown again:</p>
            <Input.Password
              value={key.key}
              readOnly
              style={{ marginTop: 16 }}
              iconRender={visible => (visible ? <KeyOutlined /> : <KeyOutlined />)}
            />
            <p style={{ marginTop: 16, color: '#ff4d4f' }}>
              <SafetyCertificateOutlined /> Store this key in a secure location.
            </p>
          </div>
        ),
        okText: 'I have copied the key',
        width: 600
      });
    } catch (error) {
      console.error('Error generating API key:', error);
      message.error('Failed to generate API key. Please try again.');
    }
  };

  const handleRevokeKey = async (keyId) => {
    try {
      await securityService.apiKeys.revoke(keyId);
      setApiKeys(prev => prev.filter(key => key.id !== keyId));
      message.success('API key has been revoked.');
    } catch (error) {
      console.error('Error revoking API key:', error);
      message.error('Failed to revoke API key. Please try again.');
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div>{text}</div>
          <div style={{ fontSize: 12, color: '#888' }}>
            Created {new Date(record.createdAt).toLocaleDateString()}
          </div>
        </div>
      ),
    },
    {
      title: 'Key',
      dataIndex: 'key',
      key: 'key',
      render: (text) => (
        <div style={{ fontFamily: 'monospace' }}>
          {`${text.substring(0, 8)}...${text.substring(text.length - 4)}`}
        </div>
      ),
    },
    {
      title: 'Permissions',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => (
        <div>
          {permissions.map(permission => (
            <Tag key={permission} color="blue" style={{ marginBottom: 4 }}>
              {permission}
            </Tag>
          ))}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'status',
      render: (isActive) => (
        <Tag color={isActive ? 'success' : 'default'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Copy to clipboard">
            <Button
              size="small"
              icon={<CopyOutlined />}
              onClick={() => {
                navigator.clipboard.writeText(record.key);
                message.success('API key copied to clipboard');
              }}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to revoke this API key?"
            onConfirm={() => handleRevokeKey(record.id)}
            okText="Yes, revoke"
            cancelText="No, keep it"
            placement="left"
          >
            <Button size="small" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="API Keys"
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          Generate New Key
        </Button>
      }
    >
      <Alert
        message="API Key Security"
        description="Your API keys carry many privileges. Be sure to keep them secure and do not share them in publicly accessible areas such as GitHub, client-side code, and so forth."
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />
      <Table
        columns={columns}
        dataSource={apiKeys}
        rowKey="id"
        pagination={false}
        loading={loading}
        locale={{
          emptyText: (
            <div style={{ padding: '40px 0' }}>
              <SafetyCertificateOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
              <div style={{ color: 'rgba(0, 0, 0, 0.45)' }}>No API keys found</div>
            </div>
          )
        }}
      />
      <Modal
        title="Generate New API Key"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleGenerateKey}
        >
          <Form.Item
            name="name"
            label="Key Name"
            rules={[{ required: true, message: 'Please enter a name for this key' }]}
          >
            <Input placeholder="e.g., My Integration" />
          </Form.Item>
          <Form.Item
            name="permissions"
            label="Permissions"
            initialValue={['read']}
            rules={[{ required: true, message: 'Please select at least one permission' }]}
          >
            <Select
              mode="multiple"
              placeholder="Select permissions"
              optionLabelProp="label"
            >
              <Option value="read" label="Read">
                <div>
                  <div>Read</div>
                  <div style={{ fontSize: 12, color: '#888' }}>Read-only access to resources</div>
                </div>
              </Option>
              <Option value="write" label="Write">
                <div>
                  <div>Write</div>
                  <div style={{ fontSize: 12, color: '#888' }}>Create and update resources</div>
                </div>
              </Option>
              <Option value="delete" label="Delete">
                <div>
                  <div>Delete</div>
                  <div style={{ fontSize: 12, color: '#888' }}>Delete resources</div>
                </div>
              </Option>
              <Option value="admin" label="Admin">
                <div>
                  <div>Admin</div>
                  <div style={{ fontSize: 12, color: '#888' }}>Full access (use with caution)</div>
                </div>
              </Option>
            </Select>
          </Form.Item>
          <Alert
            message="Security Notice"
            description="Please keep your API keys secure and do not share them in publicly accessible areas such as GitHub, client-side code, and so forth."
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <div style={{ textAlign: 'right' }}>
            <Button
              onClick={() => {
                setIsModalVisible(false);
                form.resetFields();
              }}
              style={{ marginRight: 8 }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Generate Key
            </Button>
          </div>
        </Form>
      </Modal>
    </Card>
  );
};

export default ApiKeys;
