import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>tTriangle, FiHelpCircle } from 'react-icons/fi';
import { Badge } from './ui/Badge';

const executorCategories = [
  {
    title: 'Fully Supported',
    description: 'These executors work perfectly with all our scripts',
    icon: <FiCheck className="h-5 w-5 text-green-500" />,
    executors: [
      { name: 'Arceus X', status: 'Popular' },
      { name: 'AWP', status: 'Free' },
      { name: 'Codex', status: 'Popular' },
      { name: 'Delta', status: 'Popular' },
      { name: 'Fluxus', status: 'Free' },
      { name: 'Hydrogen', status: 'Stable' },
      { name: 'Sirhurt', status: 'Popular' },
      { name: 'Synapse Z', status: 'Popular' },
      { name: 'Vega X', status: 'Free' },
      { name: 'Volcano', status: 'Free' },
      { name: 'Wave', status: 'Free' }
    ]
  },
  {
    title: 'Limited Support',
    description: 'Some functions may not work properly',
    icon: <FiAlertTriangle className="h-5 w-5 text-amber-500" />,
    executors: [
      { 
        name: 'JJSploit', 
        status: 'Free',
        note: 'Missing advanced functions'
      },
      { 
        name: '<PERSON><PERSON>', 
        status: 'Free',
        note: 'Limited API support'
      },
      { 
        name: 'Xeno', 
        status: 'Stable',
        note: 'Unstable execution'
      }
    ]
  },
  {
    title: 'Variable Compatibility',
    description: 'Results may vary depending on device and setup',
    icon: <FiHelpCircle className="h-5 w-5 text-blue-500" />,
    executors: [
      { 
        name: 'Swift', 
        status: 'Free',
        note: 'Works for some users only'
      },
      { 
        name: 'Visual', 
        status: 'Niche',
        note: 'Inconsistent performance'
      }
    ]
  }
];

const statusColors = {
  'Popular': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 px-3 py-1 text-sm',
  'Free': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 px-3 py-1 text-sm',
  'Stable': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 px-3 py-1 text-sm',
  'Niche': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 px-3 py-1 text-sm'
};

export default function ExecutorsList() {
  return (
    <section className="py-12 md:py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Supported Executors</h2>
          <p className="text-foreground/70">
            We support a wide range of executors with varying levels of compatibility.
            Choose the one that best fits your needs.
          </p>
        </div>

          <div className="grid gap-6 md:grid-cols-3">
          {executorCategories.map((category, index) => (
            <div 
              key={index}
              className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5"
            >
              <div className="p-6 border-b">
                <div className="flex items-center gap-3 mb-2">
                  {category.icon}
                  <h3 className="text-xl font-semibold">{category.title}</h3>
                </div>
                <p className="text-sm text-foreground/70">{category.description}</p>
              </div>
              
              <div className="p-4 bg-muted/30">
                <ul className="space-y-3">
                  {category.executors.map((executor, idx) => (
                    <li key={idx} className="flex justify-between items-center py-2 px-3 hover:bg-muted/50 rounded-md transition-colors">
                      <span className="font-medium text-base">{executor.name}</span>
                      <div className="flex items-center gap-2">
                        <Badge 
                          className={`${statusColors[executor.status] || 'bg-gray-100 dark:bg-gray-800'} text-sm font-medium`}
                        >
                          {executor.status}
                        </Badge>
                      </div>
                    </li>
                  ))}
                </ul>
                
                {category.executors.some(e => e.note) && (
                  <div className="mt-4 pt-4 border-t border-border">
                    <ul className="space-y-2 text-sm text-foreground/70">
                      {category.executors
                        .filter(e => e.note)
                        .map((executor, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="font-medium">{executor.name}:</span>
                            <span>{executor.note}</span>
                          </li>
                        ))
                      }
                    </ul>
                  </div>
                )}
                
                <div className="mt-4 pt-3 text-sm font-medium text-foreground/70 border-t border-border">
                  Total executors: {category.executors.length}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
