import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { FiGrid, FiFileText, FiUsers, FiSettings, FiLogOut, FiMenu, FiX, FiChevronDown, FiUser, FiPlus, FiShield } from 'react-icons/fi';
import { KeyOutlined } from '@ant-design/icons';
import { useState, useEffect } from 'react';
import { Button } from '../ui/Button';
import { cn } from '../../lib/utils';
import { useTheme } from '../../context/ThemeContext';

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: FiGrid },
  { name: 'Scrip<PERSON>', href: '/admin/scripts', icon: FiFileText },
  { name: 'Script Requests', href: '/admin/requests', icon: FiUsers },
  { name: 'Users', href: '/admin/users', icon: FiUser },
  { name: 'Public Keys', href: '/admin/public-keys', icon: KeyOutlined },
  { name: 'Security', href: '/admin/security', icon: FiShield },
  { name: 'Setting<PERSON>', href: '/admin/settings', icon: FiSettings },
];

const userNavigation = [
  { name: 'Your Profile', href: '#' },
  { name: 'Sign out', href: '#' },
];

export default function AdminLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { theme, toggleTheme, resolvedTheme } = useTheme();

  // Close sidebar when route changes
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-background flex">
      {/* Mobile sidebar */}
      <div className="md:hidden">
        <div className={cn(
          'fixed inset-0 z-40 bg-black/50 transition-opacity',
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        )} 
        onClick={() => setSidebarOpen(false)} 
        />
        <div className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out',
          'bg-background border-r border-border',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        )}>
          <div className="flex h-full flex-col">
            {/* User Profile */}
            <div className="px-4 py-3 border-b border-border">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <FiUser className="h-5 w-5 text-primary" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-foreground">Admin User</p>
                  <p className="text-xs text-muted-foreground">Administrator</p>
                </div>
              </div>
            </div>
            <nav className="flex-1 space-y-1 px-2 py-4 overflow-y-auto">
              
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    location.pathname === item.href
                      ? 'bg-accent/20 text-foreground'
                      : 'text-foreground/70 hover:bg-accent/10 hover:text-foreground',
                    'group flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors'
                  )}
                >
                  <item.icon
                    className={cn(
                      location.pathname === item.href ? 'text-primary' : 'text-foreground/50 group-hover:text-foreground',
                      'mr-3 flex-shrink-0 h-5 w-5'
                    )}
                    aria-hidden="true"
                  />
                  {item.name}
                </Link>
              ))}
            </nav>
            <div className="border-t border-border p-4 space-y-2">
              <Button 
                variant="ghost" 
                className="w-full justify-between text-foreground/70 hover:text-foreground"
                onClick={() => toggleTheme(resolvedTheme === 'dark' ? 'light' : 'dark')}
              >
                <span>Theme</span>
                <span className="text-xs bg-accent/20 px-2 py-1 rounded">
                  {resolvedTheme === 'dark' ? 'Dark' : 'Light'}
                </span>
              </Button>
              <Button variant="ghost" className="w-full justify-start text-foreground/70 hover:text-foreground">
                <FiLogOut className="mr-2 h-4 w-4" />
                Sign out
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Static sidebar for desktop */}
      <div className="hidden md:fixed md:inset-y-0 md:flex md:w-64 md:flex-col">
        <div className="flex min-h-0 flex-1 flex-col border-r border-border bg-background">
          {/* User Profile */}
          <div className="px-4 py-3 border-b border-border">
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                <FiUser className="h-5 w-5 text-primary" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-foreground">Admin User</p>
                <p className="text-xs text-muted-foreground">Administrator</p>
              </div>
            </div>
          </div>
          <div className="flex flex-1 flex-col overflow-y-auto">
            <nav className="flex-1 space-y-1 px-2 py-4">
              
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    location.pathname === item.href
                      ? 'bg-accent/20 text-foreground'
                      : 'text-foreground/70 hover:bg-accent/10 hover:text-foreground',
                    'group flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors'
                  )}
                >
                  <item.icon
                    className={cn(
                      location.pathname === item.href ? 'text-primary' : 'text-foreground/50 group-hover:text-foreground',
                      'mr-3 flex-shrink-0 h-5 w-5'
                    )}
                    aria-hidden="true"
                  />
                  {item.name}
                </Link>
              ))}
            </nav>
            <div className="border-t border-border p-4 space-y-2">
              <Button 
                variant="ghost" 
                className="w-full justify-between text-foreground/70 hover:text-foreground"
                onClick={() => toggleTheme(resolvedTheme === 'dark' ? 'light' : 'dark')}
              >
                <span>Theme</span>
                <span className="text-xs bg-accent/20 px-2 py-1 rounded">
                  {resolvedTheme === 'dark' ? 'Dark' : 'Light'}
                </span>
              </Button>
              <Button variant="ghost" className="w-full justify-start text-foreground/70 hover:text-foreground">
                <FiLogOut className="mr-2 h-4 w-4" />
                Sign out
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile header */}
      <div className="sticky top-0 z-10 flex h-16 items-center bg-background/80 backdrop-blur-sm border-b border-border px-4 md:hidden">
        <button
          type="button"
          className="rounded-md p-1 text-foreground/50 hover:text-foreground"
          onClick={() => setSidebarOpen(true)}
        >
          <FiMenu className="h-6 w-6" aria-hidden="true" />
        </button>
        <h1 className="ml-4 text-lg font-semibold text-foreground">
          {navigation.find((item) => item.href === location.pathname)?.name || 'Dashboard'}
        </h1>
      </div>

      {/* Main content */}
      <div className="flex-1 md:pl-64">
        <main className="flex-1">
          <div className="py-6">
            <div className="mx-auto px-4 sm:px-6 md:px-8">
              {children || <Outlet />}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
