import { Link, useLocation } from 'react-router-dom';
import { 
  FiGithub, 
  FiMessageCircle, 
  FiExternalLink, 
  FiCode, 
  FiBookOpen, 
  FiFileText,
  FiShield,
  FiHeart,
  FiCoffee
} from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '../../lib/utils';
import { useTheme } from '../../context/ThemeContext';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const location = useLocation();
  const { theme } = useTheme();

  const sections = [
    {
      title: 'Explore',
      icon: FiCode,
      links: [
        { name: 'Home', path: '/', icon: null },
        { name: 'Scripts', path: '/scripts', icon: null },
      ],
    },
    {
      title: 'Requests',
      icon: FiFileText,
      links: [
        { name: 'Request a Script', path: '/request-script', icon: null },
        { name: 'Request Status', path: '/request-status', icon: null },
      ],
    },
    {
      title: 'Resources',
      icon: FiBookOpen,
      links: [
        { name: 'FAQ', path: '/faq', icon: null },
        { name: 'Documentation', path: '/docs', icon: null },
        { name: 'API Reference', path: '/api', icon: null },
      ],
    },
    {
      title: 'Legal',
      icon: FiShield,
      links: [
        { name: 'Terms of Service', path: '/terms', icon: null },
        { name: 'Privacy Policy', path: '/privacy', icon: null },
        { name: 'Cookie Policy', path: '/cookies', icon: null },
      ],
    },
  ];

  const socialLinks = [
    { 
      name: 'GitHub', 
      icon: FiGithub, 
      href: 'https://github.com/scriptmaster',
      label: 'Visit our GitHub repository',
    },
    { 
      name: 'Discord', 
      icon: FiMessageCircle, 
      href: 'https://discord.gg/scriptmaster',
      label: 'Join our Discord community',
    },
  ];

  const LinkItem = ({ 
    href, 
    children, 
    isExternal = false, 
    className = '',
    isActive = false,
    icon: Icon = null
  }) => {
    const baseClasses = 'flex items-center gap-2 transition-colors';
    const activeClasses = isActive 
      ? 'text-primary font-medium' 
      : 'text-foreground/70 hover:text-foreground';
    
    const content = (
      <>
        {Icon && <Icon className="h-4 w-4 flex-shrink-0" />}
        <span>{children}</span>
        {isExternal && (
          <FiExternalLink className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
        )}
      </>
    );
    
    if (isExternal) {
      return (
        <a 
          href={href} 
          target="_blank" 
          rel="noopener noreferrer nofollow"
          className={cn(
            baseClasses, 
            activeClasses,
            'group',
            className
          )}
          aria-label={`${children} (opens in new tab)`}
        >
          {content}
        </a>
      );
    }
    
    return (
      <Link 
        to={href} 
        className={cn(
          baseClasses, 
          activeClasses,
          className
        )}
      >
        {content}
      </Link>
    );
  };

  return (
    <footer className="border-t border-border/10 bg-background/80 backdrop-blur-lg">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex flex-col space-y-4">
              <Link to="/" className="flex items-center space-x-2 group w-fit">
                <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center text-white font-bold text-xl group-hover:scale-105 transition-transform">
                  SM
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  Project Madara
                </span>
              </Link>
              <p className="text-foreground/70 text-sm leading-relaxed">
                Empowering developers with high-quality scripts and tools to supercharge your workflow and productivity.
              </p>
              <div className="flex items-center space-x-4 pt-2">
                {socialLinks.map((link) => (
                  <a
                    key={link.name}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer nofollow"
                    aria-label={link.label}
                    className="text-foreground/60 hover:text-foreground hover:bg-accent/10 p-2 rounded-full transition-colors"
                  >
                    <link.icon className="h-5 w-5" />
                  </a>
                ))}
              </div>
              <div className="pt-4">
                <p className="text-xs text-foreground/50 flex items-center">
                  <FiHeart className="h-3 w-3 mr-1 text-red-500" />
                  Made with love for the developer community
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Sections */}
          {sections.map((section) => (
            <div key={section.title} className="space-y-4">
              <div className="flex items-center space-x-2">
                {section.icon && <section.icon className="h-5 w-5 text-primary" />}
                <h3 className="text-sm font-semibold text-foreground/90 tracking-wider">
                  {section.title}
                </h3>
              </div>
              <ul className="space-y-3">
                {section.links.map((link) => {
                  const isActive = location.pathname === link.path;
                  return (
                    <li key={link.name}>
                      <LinkItem 
                        href={link.path}
                        isActive={isActive}
                        icon={link.icon}
                        className="text-sm py-1.5 -mx-2 px-2 rounded-md hover:bg-accent/10"
                      >
                        {link.name}
                      </LinkItem>
                    </li>
                  );
                })}
              </ul>
            </div>
          ))}
        </div>

        {/* Copyright and Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-border/10">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-foreground/60 flex items-center">
              <FiCoffee className="h-4 w-4 mr-1.5 text-amber-500" />
              &copy; {currentYear} Project Madara. All rights reserved.
            </p>
            <div className="flex items-center space-x-6">
              <LinkItem 
                href="/privacy" 
                className="text-xs hover:underline hover:text-foreground/90"
              >
                Privacy Policy
              </LinkItem>
              <LinkItem 
                href="/terms" 
                className="text-xs hover:underline hover:text-foreground/90"
              >
                Terms of Service
              </LinkItem>
              <LinkItem 
                href="/cookies" 
                className="text-xs hover:underline hover:text-foreground/90"
              >
                Cookie Policy
              </LinkItem>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-border/10">
            <p className="text-xs text-foreground/50 text-center">
              Made with ❤️ by the Project Madara team
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
