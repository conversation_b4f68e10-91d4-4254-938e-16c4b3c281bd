import React from 'react';
import { motion } from 'framer-motion';
import { FiGithub } from 'react-icons/fi';
import { FaDiscord } from 'react-icons/fa';
import { Button } from './ui/Button';
import { Card } from './ui/Card';

const teamMembers = [
  {
    name: '<PERSON><PERSON>',
    role: 'Founder & Lead Developer',
    description: 'Lead developer and maintainer of Project Madara. Responsible for core script development, maintenance, and project direction. Dedicated to creating high-quality, reliable scripts for the gaming community.',
    avatar: 'https://avatars.githubusercontent.com/u/150212080?v=4',
    socials: [
      { icon: <FiGithub />, url: 'https://github.com/IsThisMe01', label: 'GitHub' },
      { icon: <FaDiscord />, url: 'https://discord.gg/your-discord-invite', label: 'Discord' }
    ]
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Lead Developer',
    description: 'One of the developers focused on script suggestions and advanced features. Prefers to stay behind the scenes while supporting Project Madara.',
    avatar: null, // No avatar as per preference
    socials: [
      { icon: <FaDiscord />, url: 'https://discord.gg/your-discord-invite', label: 'Discord' }
    ]
  },
];

const Team = () => {
  return (
    <section className="pt-24 pb-16 md:pt-32 md:pb-24 bg-muted/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            Meet the Developer
          </h2>
          <p className="text-foreground/80 text-lg max-w-2xl mx-auto">
            The creators and maintainers behind ScriptHub, dedicated to delivering high-quality gaming scripts
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="w-full"
            >
              <Card className="h-full p-8 md:p-10 hover:shadow-xl transition-all duration-300 border border-border/20 flex flex-col">
                <div className="w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden border-4 border-primary/20 shadow-lg bg-muted/50 flex items-center justify-center">
                  {member.avatar ? (
                    <img 
                      src={member.avatar} 
                      alt={member.name} 
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(member.name)}&background=random`;
                      }}
                    />
                  ) : (
                    <div className="text-4xl font-bold text-foreground/30">
                      {member.name.charAt(0)}
                    </div>
                  )}
                </div>
                <div className="text-center flex-grow">
                  <h3 className="text-2xl font-bold mb-1">{member.name}</h3>
                  <p className="text-primary text-lg font-medium mb-4">{member.role}</p>
                  <p className="text-foreground/80 mb-6">{member.description}</p>
                </div>
                <div className="flex justify-center space-x-4 mt-auto">
                  {member.socials.map((social, socialIndex) => (
                    <a
                      key={socialIndex}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="h-10 w-10 rounded-full bg-muted flex items-center justify-center text-foreground/70 hover:text-primary hover:bg-muted/80 transition-colors"
                      aria-label={social.label}
                    >
                      {social.icon}
                    </a>
                  ))}
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Team;
