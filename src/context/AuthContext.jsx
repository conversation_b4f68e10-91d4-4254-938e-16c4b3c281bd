import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { supabase } from '../lib/supabase';

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [admin, setAdmin] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check for existing admin session on mount
  useEffect(() => {
    try {
      const adminData = localStorage.getItem('admin_user');
      if (adminData) {
        setAdmin(JSON.parse(adminData));
      }
    } catch (err) {
      console.error('Failed to parse admin data from localStorage', err);
      localStorage.removeItem('admin_user');
      localStorage.removeItem('admin_token');
    } finally {
      setLoading(false);
    }
  }, []);

  // Admin login with username and password
  const login = useCallback(async (username, password) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/.netlify/functions/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Login failed');
      }

      // Store token and admin data in localStorage
      localStorage.setItem('admin_token', data.token);
      localStorage.setItem('admin_user', JSON.stringify(data.admin));

      // Update the admin state
      setAdmin(data.admin);

      return { success: true, admin: data.admin };
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Logout admin
  const logout = useCallback(async () => {
    // Clear local state and storage
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
    setAdmin(null);
    setError(null);
    return { success: true };
  }, []);

  // Check if admin has specific permission
  const hasPermission = useCallback((requiredPermission) => {
    if (!admin) return false;
    if (admin.role === 'super_admin') return true;
    return admin.permissions?.includes(requiredPermission);
  }, [admin]);

  // Check if user is authenticated
  const isAuthenticated = useCallback(() => {
    const token = localStorage.getItem('admin_token');
    return !!token && !!admin;
  }, [admin]);

  // Get current admin
  const getCurrentAdmin = useCallback(() => {
    return admin;
  }, [admin]);

  return (
    <AuthContext.Provider
      value={{
        admin,
        loading,
        error,
        login,
        logout,
        isAuthenticated,
        isAdmin: !!admin,
        isSuperAdmin: admin?.role === 'super_admin',
        hasPermission,
        getCurrentAdmin
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
