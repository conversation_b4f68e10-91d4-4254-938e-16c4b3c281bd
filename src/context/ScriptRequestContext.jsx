import { createContext, useContext, useState, useEffect } from 'react';

const ScriptRequestContext = createContext();

export const ScriptRequestProvider = ({ children }) => {
  const [requests, setRequests] = useState(() => {
    // Load requests from localStorage if available
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('scriptRequests');
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });

  // Save requests to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('scriptRequests', JSON.stringify(requests));
    }
  }, [requests]);

  const submitRequest = (requestData) => {
    const newRequest = {
      id: `REQ-${Date.now()}`,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      ...requestData
    };

    setRequests(prev => [newRequest, ...prev]);
    return newRequest.id;
  };

  const getRequestById = (id) => {
    return requests.find(request => request.id === id);
  };

  // For admin purposes - update request status
  const updateRequestStatus = (id, status, adminNotes = '') => {
    setRequests(prev => 
      prev.map(request => 
        request.id === id 
          ? { 
              ...request, 
              status,
              adminNotes,
              updatedAt: new Date().toISOString() 
            } 
          : request
      )
    );
  };

  return (
    <ScriptRequestContext.Provider 
      value={{
        requests,
        submitRequest,
        getRequestById,
        updateRequestStatus
      }}
    >
      {children}
    </ScriptRequestContext.Provider>
  );
};

export const useScriptRequest = () => {
  const context = useContext(ScriptRequestContext);
  if (!context) {
    throw new Error('useScriptRequest must be used within a ScriptRequestProvider');
  }
  return context;
};
