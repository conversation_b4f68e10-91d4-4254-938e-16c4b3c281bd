import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';

const ThemeContext = createContext(undefined);

/**
 * ThemeProvider - Manages the application's theme state and provides theme-related utilities.
 * Supports 'light', 'dark', and 'system' themes with proper persistence and system preference detection.
 */
export function ThemeProvider({ children, defaultTheme = 'system' }) {
  const [theme, setTheme] = useState(defaultTheme);
  const [isMounted, setIsMounted] = useState(false);
  const [systemTheme, setSystemTheme] = useState('light');
  const [isChangingTheme, setIsChangingTheme] = useState(false);

  // Memoized system theme detection
  const getSystemTheme = useCallback(() => {
    if (typeof window === 'undefined') return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }, []);

  // Get the currently active theme (resolves 'system' to actual theme)
  const resolvedTheme = useMemo(() => {
    return theme === 'system' ? systemTheme : theme;
  }, [theme, systemTheme]);

  // Apply theme class to document element with transition
  const applyTheme = useCallback((themeToApply) => {
    const root = document.documentElement;
    const transitionDuration = '300ms';
    
    // Start transition
    root.style.transition = `color ${transitionDuration} ease, background-color ${transitionDuration} ease, border-color ${transitionDuration} ease`;
    
    // Remove all theme classes
    root.classList.remove('light', 'dark');
    
    let themeClass = themeToApply;
    if (themeToApply === 'system') {
      const currentSystemTheme = getSystemTheme();
      themeClass = currentSystemTheme;
      setSystemTheme(currentSystemTheme);
    }
    
    // Add the new theme class
    root.classList.add(themeClass);
    root.setAttribute('data-theme', themeClass);
    
    // Remove transition after it completes
    const timeoutId = setTimeout(() => {
      root.style.transition = '';
    }, 300);
    
    return () => clearTimeout(timeoutId);
  }, [getSystemTheme]);

  // Toggle between light, dark, and system themes
  const toggleTheme = useCallback((newTheme) => {
    if (isChangingTheme) return;
    
    setIsChangingTheme(true);
    const themeToSet = newTheme || (theme === 'dark' ? 'light' : theme === 'light' ? 'system' : 'dark');
    
    setTheme(themeToSet);
    localStorage.setItem('theme', themeToSet);
    
    // Apply the theme with a small delay to allow state updates
    const timeoutId = setTimeout(() => {
      applyTheme(themeToSet);
      setIsChangingTheme(false);
    }, 10);
    
    return () => clearTimeout(timeoutId);
  }, [theme, isChangingTheme, applyTheme]);

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    try {
      const savedTheme = localStorage.getItem('theme');
      const initialTheme = savedTheme || defaultTheme;
      
      // Ensure the theme is valid
      const validTheme = ['light', 'dark', 'system'].includes(initialTheme) 
        ? initialTheme 
        : defaultTheme;
      
      setTheme(validTheme);
      applyTheme(validTheme);
      setSystemTheme(getSystemTheme());
      setIsMounted(true);
    } catch (error) {
      console.error('Error initializing theme:', error);
      // Fallback to system theme
      const systemTheme = getSystemTheme();
      setTheme(systemTheme);
      applyTheme(systemTheme);
      setIsMounted(true);
    }
  }, [defaultTheme, applyTheme, getSystemTheme]);

  // Watch for system theme changes when in 'system' mode
  useEffect(() => {
    if (theme !== 'system') return;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = () => {
      const newSystemTheme = getSystemTheme();
      setSystemTheme(newSystemTheme);
      if (theme === 'system') {
        applyTheme('system');
      }
    };

    // Initial setup
    handleSystemThemeChange();
    
    // Listen for system theme changes
    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [theme, applyTheme, getSystemTheme]);

  // Only render the app after we've determined the theme
  if (!isMounted) {
    return null;
  }

  return (
    <ThemeContext.Provider
      value={{
        theme,
        setTheme: toggleTheme,
        toggleTheme,
        systemTheme,
        isDark: resolvedTheme === 'dark',
        resolvedTheme,
        isChangingTheme,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
}

/**
 * useTheme - Hook to access theme context
 * @returns {Object} Theme context with theme, setTheme, toggleTheme, systemTheme, isDark, and resolvedTheme
 */
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
