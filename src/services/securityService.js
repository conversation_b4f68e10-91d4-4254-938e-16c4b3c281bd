import axios from 'axios';
import { message } from 'antd';

// Create an axios instance with default config
const api = axios.create({
  baseURL: '/api/security',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Include cookies in requests
});

// Add a request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    // Get token from localStorage or your auth context
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle errors
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    // Handle errors here (e.g., redirect to login on 401)
    if (error.response?.status === 401) {
      // Handle unauthorized
      console.error('Unauthorized access - please login again');
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

// Security Logs API
export const securityLogsApi = {
  // Get paginated security logs
  getLogs: async (params = {}) => {
    try {
      const {
        page = 1,
        limit = 50,
        type,
        severity,
        startDate,
        endDate,
        search,
        sortBy = 'timestamp',
        sortOrder = 'desc',
      } = params;

      const response = await api.get('/logs', {
        params: {
          page,
          limit,
          ...(type && { type }),
          ...(severity && { severity }),
          ...(startDate && { startDate }),
          ...(endDate && { endDate }),
          ...(search && { search }),
          sortBy,
          sortOrder,
        },
      });

      return response;
    } catch (error) {
      console.error('Error fetching security logs:', error);
      message.error('Failed to fetch security logs');
      throw error;
    }
  },

  // Get security statistics
  getStats: async () => {
    try {
      const response = await api.get('/stats');
      return response;
    } catch (error) {
      console.error('Error fetching security stats:', error);
      message.error('Failed to fetch security statistics');
      throw error;
    }
  },
  
  // Report a security violation
  reportViolation: async (violation) => {
    try {
      const response = await api.post('/violations', violation);
      return response;
    } catch (error) {
      console.error('Error reporting security violation:', error);
      throw error;
    }
  },
  
  // Get violation types
  getViolationTypes: async () => {
    try {
      const response = await api.get('/violations/types');
      return response;
    } catch (error) {
      console.error('Error fetching violation types:', error);
      return [];
    }
  }
};

// IP Management API
export const ipManagementApi = {
  // Get list of blocked IPs with pagination and filtering
  getBlockedIps: async (params = {}) => {
    try {
      const { page = 1, limit = 50, search } = params;
      const response = await api.get('/blocked-ips', {
        params: { page, limit, ...(search && { search }) },
      });
      return response.blockedIPs || [];
    } catch (error) {
      console.error('Error fetching blocked IPs:', error);
      message.error('Failed to fetch blocked IPs');
      return [];
    }
  },

  // Block an IP address
  blockIp: async (ip, reason = 'Manual block') => {
    try {
      const response = await api.post('/block-ip', { ip, reason });
      message.success(`IP ${ip} has been blocked`);
      return response;
    } catch (error) {
      console.error(`Error blocking IP ${ip}:`, error);
      const errorMessage = error.response?.data?.message || 'Failed to block IP';
      message.error(errorMessage);
      throw error;
    }
  },

  // Unblock an IP address
  unblockIp: async (ip) => {
    try {
      const response = await api.post('/unblock-ip', { ip });
      message.success(`IP ${ip} has been unblocked`);
      return response;
    } catch (error) {
      console.error(`Error unblocking IP ${ip}:`, error);
      const errorMessage = error.response?.data?.message || 'Failed to unblock IP';
      message.error(errorMessage);
      throw error;
    }
  },

  // Get IP geolocation information
  getIpInfo: async (ip) => {
    try {
      const response = await api.get(`/ip-info/${ip}`);
      return response;
    } catch (error) {
      console.error('Error fetching IP info:', error);
      return null;
    }
  }
};

// API Keys Management
export const apiKeysApi = {
  // List all API keys
  list: async () => {
    try {
      const response = await api.get('/api-keys');
      return response.keys || [];
    } catch (error) {
      console.error('Error fetching API keys:', error);
      message.error('Failed to fetch API keys');
      return [];
    }
  },
  
  // Generate a new API key
  generate: async (name, permissions = []) => {
    try {
      const response = await api.post('/api-keys', { name, permissions });
      message.success('API key generated successfully');
      return response.key;
    } catch (error) {
      console.error('Error generating API key:', error);
      const errorMessage = error.response?.data?.message || 'Failed to generate API key';
      message.error(errorMessage);
      throw error;
    }
  },
  
  // Revoke an API key
  revoke: async (keyId) => {
    try {
      await api.delete(`/api-keys/${keyId}`);
      message.success('API key has been revoked');
      return true;
    } catch (error) {
      console.error('Error revoking API key:', error);
      const errorMessage = error.response?.data?.message || 'Failed to revoke API key';
      message.error(errorMessage);
      throw error;
    }
  },
  
  // Update API key permissions
  updatePermissions: async (keyId, permissions) => {
    try {
      const response = await api.put(`/api-keys/${keyId}/permissions`, { permissions });
      message.success('API key permissions updated');
      return response.key;
    } catch (error) {
      console.error('Error updating API key permissions:', error);
      const errorMessage = error.response?.data?.message || 'Failed to update API key permissions';
      message.error(errorMessage);
      throw error;
    }
  }
};

// Security Settings API
export const securitySettingsApi = {
  // Get current security settings
  getSettings: async () => {
    try {
      const response = await api.get('/settings');
      return response.settings || {};
    } catch (error) {
      console.error('Error fetching security settings:', error);
      message.error('Failed to load security settings');
      return {};
    }
  },
  
  // Update security settings
  updateSettings: async (settings) => {
    try {
      const response = await api.put('/settings', settings);
      message.success('Security settings updated');
      return response.settings;
    } catch (error) {
      console.error('Error updating security settings:', error);
      const errorMessage = error.response?.data?.message || 'Failed to update security settings';
      message.error(errorMessage);
      throw error;
    }
  },
  
  // Get security audit logs
  getAuditLogs: async (params = {}) => {
    try {
      const response = await api.get('/audit-logs', { params });
      return response.logs || [];
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      message.error('Failed to fetch audit logs');
      return [];
    }
  },
  
  // Get security health status
  getHealthStatus: async () => {
    try {
      const response = await api.get('/health');
      return response.status || {};
    } catch (error) {
      console.error('Error fetching security health status:', error);
      return { status: 'error', message: 'Unable to determine security health status' };
    }
  }
};

// Export all APIs under a single namespace
export default {
  // Core security logs and monitoring
  logs: securityLogsApi,
  
  // IP management and blocking
  ip: ipManagementApi,
  
  // API keys management
  keys: apiKeysApi,
  
  // Security settings and configuration
  settings: securitySettingsApi,
  
  // Helper function to check if the user has required permissions
  hasPermission: (requiredPermissions, userPermissions) => {
    if (!requiredPermissions || requiredPermissions.length === 0) return true;
    if (!userPermissions || userPermissions.length === 0) return false;
    
    return requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );
  },
  
  // Helper to format error messages consistently
  formatError: (error) => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.response?.data?.message) return error.response.data.message;
    return 'An unknown error occurred';
  }
};
