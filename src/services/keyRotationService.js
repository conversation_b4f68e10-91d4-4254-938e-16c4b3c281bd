import { supabase } from '../lib/supabaseClient';
import { generateApiKey, hashApi<PERSON>ey } from '../utils/security';

class KeyRotationService {
  constructor() {
    this.rotationSchedule = new Map();
    this.isRotating = false;
  }

  /**
   * Schedule automatic key rotation
   * @param {string} keyId - The key ID to rotate
   * @param {number} intervalDays - Rotation interval in days
   */
  scheduleRotation(keyId, intervalDays = 90) {
    const intervalMs = intervalDays * 24 * 60 * 60 * 1000;
    
    if (this.rotationSchedule.has(keyId)) {
      clearInterval(this.rotationSchedule.get(keyId));
    }
    
    const intervalId = setInterval(async () => {
      await this.rotateKey(keyId);
    }, intervalMs);
    
    this.rotationSchedule.set(keyId, intervalId);
  }

  /**
   * Rotate a specific API key
   * @param {string} keyId - The key ID to rotate
   * @returns {Object} New key information
   */
  async rotateKey(keyId) {
    if (this.isRotating) {
      throw new Error('Key rotation already in progress');
    }

    this.isRotating = true;
    
    try {
      // Get current key
      const { data: currentKey, error: fetchError } = await supabase
        .from('api_keys')
        .select('*')
        .eq('id', keyId)
        .single();

      if (fetchError || !currentKey) {
        throw new Error('Key not found');
      }

      // Generate new key
      const { key: newKey, hash: newHash, fingerprint } = generateApiKey();
      
      // Update key in database with overlap period
      const { error: updateError } = await supabase
        .from('api_keys')
        .update({
          key_hash: newHash,
          fingerprint,
          rotated_at: new Date().toISOString(),
          rotation_count: (currentKey.rotation_count || 0) + 1,
          metadata: {
            ...currentKey.metadata,
            previous_fingerprint: currentKey.fingerprint,
            rotation_reason: 'scheduled'
          }
        })
        .eq('id', keyId);

      if (updateError) throw updateError;

      // Log rotation event
      await this.logRotationEvent(keyId, 'success', {
        old_fingerprint: currentKey.fingerprint,
        new_fingerprint: fingerprint,
        rotation_count: (currentKey.rotation_count || 0) + 1
      });

      // Notify admin of rotation
      await this.notifyKeyRotation(keyId, newKey, currentKey);

      return {
        keyId,
        newKey,
        fingerprint,
        rotatedAt: new Date().toISOString()
      };

    } catch (error) {
      await this.logRotationEvent(keyId, 'failed', { error: error.message });
      throw error;
    } finally {
      this.isRotating = false;
    }
  }

  /**
   * Rotate all keys that are due for rotation
   */
  async rotateExpiredKeys() {
    const rotationThreshold = new Date();
    rotationThreshold.setDate(rotationThreshold.getDate() - 90); // 90 days

    const { data: expiredKeys, error } = await supabase
      .from('api_keys')
      .select('id, created_at, rotated_at')
      .eq('is_active', true)
      .or(`rotated_at.is.null,rotated_at.lt.${rotationThreshold.toISOString()}`);

    if (error) {
      console.error('Error fetching expired keys:', error);
      return;
    }

    const rotationResults = [];
    
    for (const key of expiredKeys) {
      try {
        const result = await this.rotateKey(key.id);
        rotationResults.push({ success: true, keyId: key.id, result });
      } catch (error) {
        rotationResults.push({ success: false, keyId: key.id, error: error.message });
      }
    }

    return rotationResults;
  }

  /**
   * Emergency key rotation for security incidents
   * @param {string} keyId - Key to rotate immediately
   * @param {string} reason - Reason for emergency rotation
   */
  async emergencyRotation(keyId, reason = 'security_incident') {
    try {
      const result = await this.rotateKey(keyId);
      
      // Immediately invalidate old key
      await supabase
        .from('api_keys')
        .update({
          is_active: false,
          revoked_at: new Date().toISOString(),
          metadata: {
            revocation_reason: reason,
            emergency_rotation: true
          }
        })
        .eq('id', keyId);

      // Send immediate notification
      await this.sendEmergencyNotification(keyId, reason);

      return result;
    } catch (error) {
      console.error('Emergency rotation failed:', error);
      throw error;
    }
  }

  /**
   * Log key rotation events
   */
  async logRotationEvent(keyId, status, metadata = {}) {
    await supabase
      .from('auth_events')
      .insert([{
        event_type: 'key_rotation',
        metadata: {
          key_id: keyId,
          status,
          timestamp: new Date().toISOString(),
          ...metadata
        }
      }]);
  }

  /**
   * Notify admin of key rotation
   */
  async notifyKeyRotation(keyId, newKey, oldKey) {
    // In a real implementation, send email/webhook notification
    console.log(`Key ${keyId} rotated successfully`);
    
    // Store notification in database
    await supabase
      .from('notifications')
      .insert([{
        type: 'key_rotation',
        recipient_id: oldKey.admin_id,
        title: 'API Key Rotated',
        message: `Your API key "${oldKey.name}" has been rotated for security.`,
        metadata: {
          key_id: keyId,
          new_fingerprint: newKey.slice(-8),
          rotation_time: new Date().toISOString()
        }
      }]);
  }

  /**
   * Send emergency notification
   */
  async sendEmergencyNotification(keyId, reason) {
    // Implement emergency notification logic
    console.error(`EMERGENCY: Key ${keyId} rotated due to ${reason}`);
  }

  /**
   * Clean up rotation schedules
   */
  cleanup() {
    for (const intervalId of this.rotationSchedule.values()) {
      clearInterval(intervalId);
    }
    this.rotationSchedule.clear();
  }
}

// Export singleton instance
export const keyRotationService = new KeyRotationService();
export default keyRotationService;
