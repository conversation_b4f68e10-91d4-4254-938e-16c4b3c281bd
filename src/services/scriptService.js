import axios from 'axios';

const apiClient = axios.create({
  baseURL: '/.netlify/functions',
  headers: {
    'Content-Type': 'application/json',
  },
});

const getScripts = async () => {
  try {
    const response = await apiClient.get('/admin/scripts');
    return response.data;
  } catch (error) {
    console.error('Error fetching scripts:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to fetch scripts');
  }
};

const createScript = async (scriptData) => {
  try {
    const response = await apiClient.post('/admin/scripts', scriptData);
    return response.data;
  } catch (error) {
    console.error('Error creating script:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to create script');
  }
};

const updateScript = async (id, scriptData) => {
  try {
    const response = await apiClient.put(`/admin/scripts/${id}`, scriptData);
    return response.data;
  } catch (error) {
    console.error('Error updating script:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to update script');
  }
};

const deleteScript = async (id) => {
  try {
    await apiClient.delete(`/admin/scripts/${id}`);
  } catch (error) {
    console.error('Error deleting script:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to delete script');
  }
};

const scriptService = {
  getScripts,
  createScript,
  updateScript,
  deleteScript,
};

export default scriptService;
