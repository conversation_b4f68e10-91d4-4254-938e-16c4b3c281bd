import axios from 'axios';

const apiClient = axios.create({
  baseURL: '/.netlify/functions',
  headers: {
    'Content-Type': 'application/json',
  },
});

const getRequests = async (params) => {
  try {
    const response = await apiClient.get('/admin/requests', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching requests:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to fetch requests');
  }
};

const getRequestStats = async () => {
  try {
    const response = await apiClient.get('/admin/requests/stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching request stats:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to fetch request stats');
  }
};

const requestService = {
  getRequests,
  getRequestStats,
};

export default requestService;
