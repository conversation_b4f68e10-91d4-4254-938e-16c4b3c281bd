import axios from 'axios';

const apiClient = axios.create({
  baseURL: '/.netlify/functions',
  headers: {
    'Content-Type': 'application/json',
  },
});

const generateKey = async (userId, serviceType, metadata) => {
  try {
    const response = await apiClient.post('/admin/public-keys/generate', {
      userId,
      serviceType,
      metadata,
    });
    return response.data;
  } catch (error) {
    console.error('Error generating key:', error.response?.data || error.message);
    throw error.response?.data || new Error('Key generation failed');
  }
};

const validateKey = async (key, hwid) => {
  try {
    const response = await apiClient.post('/admin/public-keys/validate', { key, hwid });
    return response.data;
  } catch (error) {
    console.error('Error validating key:', error.response?.data || error.message);
    throw error.response?.data || new Error('Key validation failed');
  }
};

// NEW: Securely fetch a key by ID (for download/complete page)
const getKeyById = async (keyId, fingerprint) => {
  try {
    const response = await apiClient.get(`/admin/public-keys/${keyId}`, {
      headers: {
        'X-Device-Fingerprint': fingerprint,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching key by ID:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to fetch key');
  }
};

const keyService = {
  generateKey,
  validateKey,
  getKeyById,
};

export default keyService;
