import { supabase } from '../lib/supabaseClient';

class MonitoringService {
  constructor() {
    this.metrics = new Map();
    this.alerts = new Map();
    this.thresholds = {
      failedAuthAttempts: { count: 10, window: 300000 }, // 10 attempts in 5 minutes
      rateLimitViolations: { count: 100, window: 3600000 }, // 100 violations in 1 hour
      keyGenerationSpike: { count: 50, window: 600000 }, // 50 keys in 10 minutes
      hwidManipulation: { count: 5, window: 900000 } // 5 attempts in 15 minutes
    };
  }

  /**
   * Track security event
   * @param {string} eventType - Type of security event
   * @param {Object} metadata - Event metadata
   */
  async trackSecurityEvent(eventType, metadata = {}) {
    const event = {
      event_type: eventType,
      timestamp: new Date().toISOString(),
      metadata: {
        ...metadata,
        user_agent: metadata.userAgent || 'unknown',
        ip_address: metadata.ipAddress || 'unknown'
      }
    };

    // Store in database
    await supabase
      .from('security_events')
      .insert([event]);

    // Check for alert conditions
    await this.checkAlertConditions(eventType, metadata);

    // Update real-time metrics
    this.updateMetrics(eventType, metadata);
  }

  /**
   * Get security metrics for dashboard
   * @param {string} timeRange - Time range (1h, 24h, 7d, 30d)
   * @returns {Object} Security metrics
   */
  async getSecurityMetrics(timeRange = '24h') {
    const timeRanges = {
      '1h': 1,
      '24h': 24,
      '7d': 24 * 7,
      '30d': 24 * 30
    };

    const hoursBack = timeRanges[timeRange] || 24;
    const startTime = new Date();
    startTime.setHours(startTime.getHours() - hoursBack);

    // Get event counts by type
    const { data: events, error } = await supabase
      .from('security_events')
      .select('event_type, created_at, metadata')
      .gte('created_at', startTime.toISOString());

    if (error) {
      console.error('Error fetching security metrics:', error);
      return null;
    }

    // Process metrics
    const metrics = {
      totalEvents: events.length,
      eventsByType: {},
      timeline: this.generateTimeline(events, hoursBack),
      topIPs: this.getTopIPs(events),
      alertsTriggered: await this.getActiveAlerts(),
      keyUsageStats: await this.getKeyUsageStats(startTime),
      systemHealth: await this.getSystemHealth()
    };

    // Count events by type
    events.forEach(event => {
      metrics.eventsByType[event.event_type] = 
        (metrics.eventsByType[event.event_type] || 0) + 1;
    });

    return metrics;
  }

  /**
   * Generate timeline data for charts
   */
  generateTimeline(events, hoursBack) {
    const timeline = [];
    const now = new Date();
    
    for (let i = hoursBack; i >= 0; i--) {
      const hour = new Date(now.getTime() - (i * 60 * 60 * 1000));
      const hourStart = new Date(hour);
      hourStart.setMinutes(0, 0, 0);
      const hourEnd = new Date(hourStart.getTime() + 60 * 60 * 1000);
      
      const hourEvents = events.filter(event => {
        const eventTime = new Date(event.created_at);
        return eventTime >= hourStart && eventTime < hourEnd;
      });

      timeline.push({
        time: hourStart.toISOString(),
        events: hourEvents.length,
        failedAuth: hourEvents.filter(e => e.event_type === 'failed_auth').length,
        rateLimitHit: hourEvents.filter(e => e.event_type === 'rate_limit_exceeded').length,
        keyGenerated: hourEvents.filter(e => e.event_type === 'key_generated').length
      });
    }

    return timeline;
  }

  /**
   * Get top IP addresses by event count
   */
  getTopIPs(events) {
    const ipCounts = {};
    
    events.forEach(event => {
      const ip = event.metadata?.ip_address || 'unknown';
      ipCounts[ip] = (ipCounts[ip] || 0) + 1;
    });

    return Object.entries(ipCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([ip, count]) => ({ ip, count }));
  }

  /**
   * Check for alert conditions
   */
  async checkAlertConditions(eventType, metadata) {
    const threshold = this.thresholds[eventType];
    if (!threshold) return;

    const windowStart = new Date(Date.now() - threshold.window);
    
    const { data: recentEvents, error } = await supabase
      .from('security_events')
      .select('id')
      .eq('event_type', eventType)
      .gte('created_at', windowStart.toISOString());

    if (error) return;

    if (recentEvents.length >= threshold.count) {
      await this.triggerAlert(eventType, {
        count: recentEvents.length,
        threshold: threshold.count,
        window: threshold.window,
        metadata
      });
    }
  }

  /**
   * Trigger security alert
   */
  async triggerAlert(alertType, details) {
    const alert = {
      alert_type: alertType,
      severity: this.getAlertSeverity(alertType),
      message: this.generateAlertMessage(alertType, details),
      details,
      triggered_at: new Date().toISOString(),
      status: 'active'
    };

    // Store alert
    await supabase
      .from('security_alerts')
      .insert([alert]);

    // Send notifications
    await this.sendAlertNotification(alert);

    console.warn(`SECURITY ALERT: ${alert.message}`);
  }

  /**
   * Get alert severity level
   */
  getAlertSeverity(alertType) {
    const severityMap = {
      failedAuthAttempts: 'medium',
      rateLimitViolations: 'low',
      keyGenerationSpike: 'high',
      hwidManipulation: 'high'
    };
    
    return severityMap[alertType] || 'medium';
  }

  /**
   * Generate alert message
   */
  generateAlertMessage(alertType, details) {
    const messages = {
      failedAuthAttempts: `${details.count} failed authentication attempts detected`,
      rateLimitViolations: `${details.count} rate limit violations in the last hour`,
      keyGenerationSpike: `Unusual key generation activity: ${details.count} keys generated`,
      hwidManipulation: `${details.count} HWID manipulation attempts detected`
    };
    
    return messages[alertType] || `Security event: ${alertType}`;
  }

  /**
   * Send alert notification
   */
  async sendAlertNotification(alert) {
    // In production, integrate with email/Slack/PagerDuty
    console.log('Alert notification sent:', alert);
  }

  /**
   * Get active alerts
   */
  async getActiveAlerts() {
    const { data: alerts, error } = await supabase
      .from('security_alerts')
      .select('*')
      .eq('status', 'active')
      .order('triggered_at', { ascending: false })
      .limit(10);

    return error ? [] : alerts;
  }

  /**
   * Get key usage statistics
   */
  async getKeyUsageStats(startTime) {
    const { data: keys, error } = await supabase
      .from('api_keys')
      .select('id, use_count, last_used_at, created_at')
      .gte('created_at', startTime.toISOString());

    if (error) return {};

    const totalKeys = keys.length;
    const activeKeys = keys.filter(k => k.last_used_at).length;
    const totalUsage = keys.reduce((sum, k) => sum + (k.use_count || 0), 0);

    return {
      totalKeys,
      activeKeys,
      inactiveKeys: totalKeys - activeKeys,
      totalUsage,
      averageUsage: totalKeys > 0 ? Math.round(totalUsage / totalKeys) : 0
    };
  }

  /**
   * Get system health metrics
   */
  async getSystemHealth() {
    // Check database connectivity
    const dbHealth = await this.checkDatabaseHealth();
    
    // Check rate limiting status
    const rateLimitHealth = await this.checkRateLimitHealth();
    
    // Check key generation performance
    const keyGenHealth = await this.checkKeyGenerationHealth();

    return {
      database: dbHealth,
      rateLimiting: rateLimitHealth,
      keyGeneration: keyGenHealth,
      overall: dbHealth && rateLimitHealth && keyGenHealth ? 'healthy' : 'degraded'
    };
  }

  async checkDatabaseHealth() {
    try {
      const { error } = await supabase
        .from('api_keys')
        .select('id')
        .limit(1);
      
      return !error;
    } catch {
      return false;
    }
  }

  async checkRateLimitHealth() {
    // Check if rate limiting is functioning
    return true; // Implement actual check
  }

  async checkKeyGenerationHealth() {
    // Check key generation performance
    return true; // Implement actual check
  }

  /**
   * Update real-time metrics
   */
  updateMetrics(eventType, metadata) {
    const now = Date.now();
    const key = `${eventType}_${Math.floor(now / 60000)}`; // Per minute
    
    this.metrics.set(key, (this.metrics.get(key) || 0) + 1);
    
    // Clean old metrics (keep last hour)
    const cutoff = now - 3600000;
    for (const [metricKey] of this.metrics) {
      const timestamp = parseInt(metricKey.split('_').pop()) * 60000;
      if (timestamp < cutoff) {
        this.metrics.delete(metricKey);
      }
    }
  }
}

// Export singleton instance
export const monitoringService = new MonitoringService();
export default monitoringService;
