const express = require('express');
const { checkPermissions } = require('../middleware/securityMiddleware');
const { SecurityLog } = require('../models');
const router = express.Router();

// Middleware to ensure user is authenticated and has admin role
const requireAdmin = (req, res, next) => {
  if (!req.user || !req.user.roles.includes('admin')) {
    return res.status(403).json({ error: 'Forbidden' });
  }
  next();
};

/**
 * GET /api/security/logs
 * Get security logs with pagination and filtering
 */
router.get('/logs', requireAdmin, async (req, res, next) => {
  try {
    const { 
      page = 1, 
      limit = 50, 
      type, 
      severity, 
      startDate, 
      endDate,
      search
    } = req.query;

    const query = {};
    
    // Apply filters if provided
    if (type) query.type = type;
    if (severity) query.severity = severity;
    
    // Date range filter
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }
    
    // Text search
    if (search) {
      query.$or = [
        { 'details.message': { $regex: search, $options: 'i' } },
        { 'details.error': { $regex: search, $options: 'i' } },
        { ip: { $regex: search, $options: 'i' } },
        { userAgent: { $regex: search, $options: 'i' } }
      ];
    }

    const options = {
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      sort: { timestamp: -1 }, // Most recent first
      lean: true
    };

    const result = await SecurityLog.paginate(query, options);
    
    res.json({
      logs: result.docs,
      total: result.totalDocs,
      pages: result.totalPages,
      page: result.page,
      limit: result.limit
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/security/stats
 * Get security statistics
 */
router.get('/stats', requireAdmin, async (req, res, next) => {
  try {
    const [
      totalLogs,
      highSeverity,
      mediumSeverity,
      lowSeverity,
      recentLogs,
      topIps,
      topEndpoints,
      activityByHour
    ] = await Promise.all([
      // Total logs
      SecurityLog.countDocuments(),
      
      // Logs by severity
      SecurityLog.countDocuments({ severity: 'high' }),
      SecurityLog.countDocuments({ severity: 'medium' }),
      SecurityLog.countDocuments({ severity: 'low' }),
      
      // Recent logs (last 24 hours)
      SecurityLog.find({
        timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      }).sort({ timestamp: -1 }).limit(100).lean(),
      
      // Top IPs
      SecurityLog.aggregate([
        { $group: { _id: '$ip', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      
      // Top endpoints
      SecurityLog.aggregate([
        { $match: { path: { $exists: true, $ne: '' } } },
        { $group: { _id: { path: '$path', method: '$method' }, count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      
      // Activity by hour
      SecurityLog.aggregate([
        {
          $group: {
            _id: { $hour: '$timestamp' },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ])
    ]);
    
    // Process recent logs to get activity by type
    const activityByType = recentLogs.reduce((acc, log) => {
      acc[log.type] = (acc[log.type] || 0) + 1;
      return acc;
    }, {});
    
    res.json({
      summary: {
        totalLogs,
        highSeverity,
        mediumSeverity,
        lowSeverity
      },
      activity: {
        byType: Object.entries(activityByType).map(([type, count]) => ({ type, count })),
        byHour: activityByHour.map(hour => ({
          hour: hour._id,
          count: hour.count
        }))
      },
      topIps: topIps.map(ip => ({
        ip: ip._id,
        count: ip.count
      })),
      topEndpoints: topEndpoints.map(endpoint => ({
        path: endpoint._id.path,
        method: endpoint._id.method,
        count: endpoint.count
      }))
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/security/block-ip
 * Block an IP address
 */
router.post('/block-ip', requireAdmin, async (req, res, next) => {
  try {
    const { ip, reason = 'Manual block' } = req.body;
    
    if (!ip) {
      return res.status(400).json({ error: 'IP address is required' });
    }
    
    // Add to blocked IPs (you might want to store this in Redis for distributed systems)
    // This is a simplified example
    const blockedIPs = new Set(await req.app.get('blockedIPs') || []);
    blockedIPs.add(ip);
    req.app.set('blockedIPs', Array.from(blockedIPs));
    
    // Log the action
    await new SecurityLog({
      type: 'ip_blocked',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method,
      details: { blockedIp: ip, reason },
      severity: 'high'
    }).save();
    
    res.json({ success: true, message: `IP ${ip} has been blocked` });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/security/unblock-ip
 * Unblock an IP address
 */
router.post('/unblock-ip', requireAdmin, async (req, res, next) => {
  try {
    const { ip } = req.body;
    
    if (!ip) {
      return res.status(400).json({ error: 'IP address is required' });
    }
    
    // Remove from blocked IPs
    const blockedIPs = new Set(await req.app.get('blockedIPs') || []);
    blockedIPs.delete(ip);
    req.app.set('blockedIPs', Array.from(blockedIPs));
    
    // Log the action
    await new SecurityLog({
      type: 'ip_unblocked',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method,
      details: { unblockedIp: ip },
      severity: 'medium'
    }).save();
    
    res.json({ success: true, message: `IP ${ip} has been unblocked` });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/security/blocked-ips
 * Get list of blocked IPs
 */
router.get('/blocked-ips', requireAdmin, async (req, res, next) => {
  try {
    const blockedIPs = await req.app.get('blockedIPs') || [];
    res.json({ blockedIPs });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
