const API_BASE_URL = '/.netlify/functions';

// Helper to handle API responses
async function handleResponse(response) {
  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    const error = new Error(errorData.error || 'API request failed');
    error.status = response.status;
    error.details = errorData.details;
    throw error;
  }
  return response.json();
}

// Get auth headers with Bearer token
export function getAuthHeaders() {
  const headers = {
    'Content-Type': 'application/json',
  };
  
  // Get the Supabase auth token from localStorage
  const supabaseSession = JSON.parse(localStorage.getItem('sb-jsyqjvzqknzqyqkqyqkq-auth-token') || '{}');
  const token = supabaseSession?.access_token;
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

// Generic API request function
const apiRequest = async (endpoint, options = {}) => {
  const { method = 'GET', body, headers = {} } = options;
  
  // In development, prefix with .netlify/functions for direct access to functions
  const isDev = process.env.NODE_ENV === 'development';
  const baseUrl = isDev ? 'http://localhost:9999/.netlify/functions' : '/.netlify/functions';
  
  // Add auth headers
  const authHeaders = getAuthHeaders();
  const allHeaders = {
    'Content-Type': 'application/json',
    ...authHeaders,
    ...headers,
  };

  // Build the full URL
  const url = `${baseUrl}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
  
  console.log(`API Request [${method}]: ${url}`, { body, headers: allHeaders });

  try {
    const response = await fetch(url, {
      method,
      headers: allHeaders,
      credentials: 'include',
      ...(body && { body: typeof body === 'string' ? body : JSON.stringify(body) }),
    });

    const data = await handleResponse(response);
    console.log(`API Response [${method} ${endpoint}]:`, data);
    return data;
  } catch (error) {
    console.error(`API request failed [${method} ${endpoint}]:`, error);
    throw error;
  }
};

// Admin Auth API
export const authAPI = {
  getProfile: async () => {
    return apiRequest('/admin/profile');
  },
  updateProfile: async (updates) => {
    return apiRequest('/admin/profile', {
      method: 'PUT',
      body: updates,
    });
  },
};

// API Keys Management
export const apiKeysAPI = {
  list: async () => {
    return apiRequest('/admin/api-keys');
  },
  create: async (data) => {
    return apiRequest('/admin/api-keys', {
      method: 'POST',
      data,
    });
  },
  delete: async (keyId) => {
    return apiRequest(`/admin/api-keys?id=${keyId}`, {
      method: 'DELETE',
    });
  },
};

// Scripts Management
export const scriptsAPI = {
  // Get all scripts (public)
  list: async (filters = {}) => {
    const query = new URLSearchParams(filters).toString();
    const endpoint = query ? `get-scripts?${query}` : 'get-scripts';
    return apiRequest(endpoint);
  },
  
  // Get a single script by ID (public)
  get: async (idOrSlug) => {
    return apiRequest(`get-scripts/${idOrSlug}`);
  },
  
  // Create a new script (admin only)
  create: async (scriptData) => {
    return apiRequest('create-script', {
      method: 'POST',
      body: scriptData
    });
  },
  
  // Update an existing script (admin only)
  update: async (id, updates) => {
    return apiRequest(`update-script/${id}`, {
      method: 'PUT',
      body: updates
    });
  },
  
  // Delete a script (admin only)
  delete: async (id) => {
    return apiRequest(`delete-script/${id}`, {
      method: 'DELETE'
    });
  },
  
  // Toggle script status (active/inactive) - admin only
  toggleStatus: async (id, isActive) => {
    return apiRequest(`update-script-status/${id}`, {
      method: 'PUT',
      body: { is_active: isActive }
    });
  },
  
  // Get update logs for a script
  getUpdateLogs: async (scriptId) => {
    return apiRequest(`get-update-logs/${scriptId}`);
  },
  
  // Add an update log to a script
  addUpdateLog: async (scriptId, logData) => {
    return apiRequest(`add-update-log/${scriptId}`, {
      method: 'POST',
      body: logData
    });
  }
};

// Activity Logs
export const activityLogsAPI = {
  list: async (filters = {}) => {
    return apiRequest('/admin/activity-logs', {
      params: filters,
    });
  },
  get: async (logId) => {
    return apiRequest(`/admin/activity-logs/${logId}`);
  },
  getStats: async (filters = {}) => {
    return apiRequest('/admin/activity-logs/stats', {
      params: filters,
    });
  },
};

// Site Settings
export const settingsAPI = {
  getAll: async () => {
    return apiRequest('/admin/site-settings');
  },
  get: async (key) => {
    return apiRequest(`/admin/site-settings/${key}`);
  },
  set: async (key, value, options = {}) => {
    return apiRequest(`/admin/site-settings/${key}`, {
      method: 'PUT',
      data: { value, ...options },
    });
  },
  delete: async (key) => {
    return apiRequest(`/admin/site-settings/${key}`, {
      method: 'DELETE',
    });
  },
};

// Export all API modules
export default {
  auth: authAPI,
  apiKeys: apiKeysAPI,
  scripts: scriptsAPI,
  activityLogs: activityLogsAPI,
  settings: settingsAPI,
};
