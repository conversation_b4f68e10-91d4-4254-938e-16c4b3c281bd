import { createClient } from '@supabase/supabase-js';

// Get environment variables - these are now injected by Vite
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// These checks will help catch configuration issues early
if (!supabaseUrl) {
  throw new Error('❌ Missing Supabase URL. Please ensure VITE_SUPABASE_URL is set in your environment variables.');
}

if (!supabaseAnonKey) {
  throw new Error('❌ Missing Supabase Anon Key. Please ensure VITE_SUPABASE_ANON_KEY is set in your environment variables.');
}

console.log('🔑 Initializing Supabase with URL:', supabaseUrl.replace(/\/auth\/v1$/, ''));

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Helper functions for common operations
export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()
  return user
}

export const signIn = async (email, password) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  return { data, error }
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  return { error }
}

// Database tables
export const TABLES = {
  SCRIPTS: 'scripts',
  SCRIPT_REQUESTS: 'script_requests',
  USERS: 'users',
  EXECUTORS: 'executors',
  REQUESTS: 'requests'
}

// Subscribe to real-time changes
export const subscribeToChanges = (table, filter, callback) => {
  return supabase
    .channel('any')
    .on('postgres_changes', 
      { 
        event: '*',
        schema: 'public',
        table,
        ...(filter ? { filter } : {})
      }, 
      payload => callback(payload)
    )
    .subscribe()
}
