import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createServer } from 'http';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { rateLimit } from 'express-rate-limit';
import { slowDown } from 'express-slow-down';
import { securityMiddleware } from '../middleware/securityMiddleware.js';
import securityRoutes from '../routes/securityRoutes.js';

// Initialize Express app
const app = express();
const server = createServer(app);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Trust first proxy (for rate limiting behind proxy)
app.set('trust proxy', 1);

// Apply security middleware
app.use(securityMiddleware());

// Apply rate limiting middleware
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Apply rate limiting to all requests
app.use(limiter);

// Apply speed limiting for API routes
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // Allow 50 requests per 15 minutes, then...
  delayMs: (hits) => hits * 100, // Add 100ms of delay to every request after the 50th
});

// Apply speed limiting to API routes
app.use('/api', speedLimiter);

// Parse JSON bodies (as sent by API clients)
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Enable CORS
app.use(cors({
  origin: [
    'https://projectmadara.com'
  ],
  credentials: true
}));

// Security headers
app.use(helmet());

// Compression
app.use(compression());

// API routes
app.use('/api/security', securityRoutes);

// Serve static files from the React app
app.use(express.static(join(__dirname, '../../dist')));

// The "catchall" handler: for any request that doesn't
// match one above, send back React's index.html file.
app.get('*', (req, res) => {
  res.sendFile(join(__dirname, '../../dist/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

export default server;
