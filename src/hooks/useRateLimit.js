import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { getHWID } from '../utils/hwid';

/**
 * Custom hook to handle rate limiting in React components
 * @param {Object} options - Rate limiting options
 * @param {string} [options.endpoint] - API endpoint to check rate limits for
 * @param {number} [options.windowMs] - Time window in milliseconds
 * @param {number} [options.maxRequests] - Maximum number of requests per window
 * @returns {Object} Rate limit state and methods
 */
export const useRateLimit = (options = {}) => {
  const {
    endpoint = '/api',
    windowMs = 15 * 60 * 1000, // 15 minutes
    maxRequests = 100
  } = options;

  const [rateLimit, setRateLimit] = useState({
    remaining: maxRequests,
    limit: maxRequests,
    resetTime: 0,
    isLimited: false,
    retryAfter: 0,
    lastUpdated: null,
    error: null
  });

  // Function to update rate limit state from headers
  const updateRateLimitFromHeaders = useCallback((headers) => {
    const remaining = parseInt(headers.get('X-RateLimit-Remaining') || maxRequests, 10);
    const limit = parseInt(headers.get('X-RateLimit-Limit') || maxRequests, 10);
    const resetTime = parseInt(headers.get('X-RateLimit-Reset') || '0', 10) * 1000;
    
    setRateLimit(prev => ({
      ...prev,
      remaining,
      limit,
      resetTime,
      isLimited: remaining <= 0,
      retryAfter: Math.max(0, Math.ceil((resetTime - Date.now()) / 1000)),
      lastUpdated: Date.now(),
      error: remaining <= 0 ? 'Rate limit exceeded' : null
    }));
  }, [maxRequests]);

  // Function to check rate limits
  const checkRateLimit = useCallback(async () => {
    try {
      const hwid = await getHWID();
      const response = await fetch(`${endpoint}/rate-limit/check`, {
        method: 'GET',
        headers: {
          'X-HWID': hwid,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      updateRateLimitFromHeaders(response.headers);
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || 'Failed to check rate limit');
      }

      return await response.json();
    } catch (error) {
      console.error('Rate limit check failed:', error);
      setRateLimit(prev => ({
        ...prev,
        error: error.message,
        lastUpdated: Date.now()
      }));
      throw error;
    }
  }, [endpoint, updateRateLimitFromHeaders]);

  // Function to make a rate-limited request
  const makeRequest = useCallback(async (url, options = {}) => {
    try {
      const hwid = await getHWID();
      
      // Add HWID to headers if not already present
      const headers = {
        'X-HWID': hwid,
        'Content-Type': 'application/json',
        ...options.headers
      };

      const response = await fetch(`${endpoint}${url}`, {
        ...options,
        headers
      });

      // Update rate limit state from response headers
      updateRateLimitFromHeaders(response.headers);

      if (!response.ok) {
        const error = await response.json().catch(() => ({}));
        throw new Error(error.message || 'Request failed');
      }

      return await response.json();
    } catch (error) {
      console.error('Rate limited request failed:', error);
      setRateLimit(prev => ({
        ...prev,
        error: error.message,
        lastUpdated: Date.now()
      }));
      throw error;
    }
  }, [endpoint, updateRateLimitFromHeaders]);

  // Effect to update retry after countdown
  useEffect(() => {
    if (!rateLimit.isLimited) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const retryAfter = Math.ceil((rateLimit.resetTime - now) / 1000);
      
      setRateLimit(prev => ({
        ...prev,
        retryAfter: Math.max(0, retryAfter),
        isLimited: retryAfter > 0
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [rateLimit.isLimited, rateLimit.resetTime]);

  // Initial rate limit check
  useEffect(() => {
    checkRateLimit().catch(console.error);
  }, [checkRateLimit]);

  return {
    ...rateLimit,
    checkRateLimit,
    makeRequest,
    isLimited: rateLimit.isLimited,
    resetTime: new Date(rateLimit.resetTime),
    retryAfter: Math.max(0, rateLimit.retryAfter),
    remaining: Math.max(0, rateLimit.remaining),
    limit: rateLimit.limit,
    error: rateLimit.error,
    lastUpdated: rateLimit.lastUpdated ? new Date(rateLimit.lastUpdated) : null
  };
};

/**
 * Higher-order component to provide rate limit context
 * @param {React.Component} Component - Component to wrap with rate limit context
 * @returns {React.Component} Wrapped component with rate limit props
 */
export const withRateLimit = (Component) => {
  return function WrappedComponent(props) {
    const rateLimit = useRateLimit();
    return <Component {...props} rateLimit={rateLimit} />;
  };
};
