import { useState, useCallback, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { SecurityContext } from '../context/SecurityContext';
import { keyService } from '../services/keyService';

/**
 * @typedef {Object} KeyGenerationRequest
 * @property {string} userId - The ID of the user generating the key
 * @property {'linkvertise'|'shrinkme'|'standard'} serviceType - The type of service the key is for
 * @property {Object} [metadata] - Additional metadata for the key
 */

/**
 * @typedef {Object} KeyValidationRequest
 * @property {string} key - The API key to validate
 * @property {string} [hwid] - The hardware ID for validation
 */

/**
 * @typedef {Object} HwidValidationRequest
 * @property {string} keyId - The ID of the key
 * @property {string} hwid - The hardware ID to validate
 */

export const useKeySystem = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [generatedKey, setGeneratedKey] = useState(null);
  const [validationResult, setValidationResult] = useState(null);
  
  const security = useContext(SecurityContext);
  const navigate = useNavigate();

  const generateKey = useCallback(async (request) => {
    setLoading(true);
    setError(null);
    try {
      if (!security.isSecure) {
        throw new Error('Security checks failed. Cannot generate key.');
      }
      const result = await keyService.generateKey({
        ...request,
        fingerprint: security.fingerprint
      });
      setGeneratedKey(result);
      navigate(`/keys/complete?keyId=${result.id}`);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to generate key');
      return null;
    } finally {
      setLoading(false);
    }
  }, [security, navigate]);

  const validateKey = useCallback(async (request) => {
    setLoading(true);
    setError(null);
    try {
      const result = await keyService.validateKey({
        ...request,
        hwid: security.fingerprint?.hwid
      });
      setValidationResult(result);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to validate key');
      return null;
    } finally {
      setLoading(false);
    }
  }, [security]);

  return {
    loading,
    error,
    generatedKey,
    validationResult,
    generateKey,
    validateKey
  };
};
