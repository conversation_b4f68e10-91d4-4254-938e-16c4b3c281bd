import { useState, useCallback } from 'react';
import { message } from 'antd';
import { signRequest } from '../utils/requestSigner';
import { useSecurity } from '../context/SecurityContext';

/**
 * A custom hook for making secure API requests with automatic request signing and security features.
 * @param {Object} options - Configuration options for the hook
 * @param {string} options.baseUrl - The base URL for API requests
 * @param {Object} options.defaultHeaders - Default headers to include with every request
 * @returns {Object} An object containing the secureFetch function and loading state
 */
const useSecureFetch = ({ baseUrl = '', defaultHeaders = {} } = {}) => {
  const [loading, setLoading] = useState(false);
  const { deviceFingerprint, updateRateLimits } = useSecurity();

  /**
   * Make a secure API request with automatic request signing and security headers
   * @param {string} endpoint - The API endpoint (e.g., '/users/me')
   * @param {Object} options - Fetch options (method, headers, body, etc.)
   * @param {boolean} requireAuth - Whether to include authentication headers
   * @returns {Promise<Object>} The parsed JSON response
   */
  const secureFetch = useCallback(
    async (endpoint, options = {}, requireAuth = true) => {
      const { method = 'GET', headers = {}, body, ...restOptions } = options;
      const url = `${baseUrl}${endpoint}`;
      
      // Prepare headers
      const requestHeaders = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...defaultHeaders,
        ...headers,
      };

      // Add device fingerprint if available
      if (deviceFingerprint) {
        requestHeaders['X-Device-Fingerprint'] = deviceFingerprint;
      }

      // Prepare request options
      const requestOptions = {
        method,
        headers: requestHeaders,
        credentials: 'include', // Include cookies for authentication
        ...restOptions,
      };

      // Add body for non-GET/HEAD requests
      if (body && method !== 'GET' && method !== 'HEAD') {
        requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
      }

      // Sign the request if authentication is required
      if (requireAuth) {
        try {
          const signedRequest = signRequest({
            method,
            url: endpoint,
            headers: requestHeaders,
            body: requestOptions.body,
          });

          // Add signed headers to the request
          Object.entries(signedRequest.headers).forEach(([key, value]) => {
            requestOptions.headers[key] = value;
          });
        } catch (error) {
          console.error('Error signing request:', error);
          throw new Error('Failed to sign request');
        }
      }

      setLoading(true);
      
      try {
        const response = await fetch(url, requestOptions);
        
        // Update rate limits from response headers if available
        if (updateRateLimits) {
          updateRateLimits(response.headers);
        }
        
        // Handle rate limiting (429 Too Many Requests)
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After') || 60;
          message.error(`Rate limit exceeded. Please try again in ${retryAfter} seconds.`);
          throw new Error('rate_limit_exceeded');
        }
        
        // Handle unauthorized (401) and forbidden (403) responses
        if (response.status === 401) {
          message.error('Session expired. Please log in again.');
          // Optionally redirect to login
          // navigate('/login', { replace: true, state: { from: window.location.pathname } });
          throw new Error('unauthorized');
        }
        
        if (response.status === 403) {
          message.error('You do not have permission to perform this action.');
          throw new Error('forbidden');
        }
        
        // Parse JSON response
        const data = await response.json().catch(() => ({}));
        
        // Handle non-2xx responses
        if (!response.ok) {
          const error = new Error(data.message || 'An error occurred');
          error.status = response.status;
          error.data = data;
          throw error;
        }
        
        return data;
      } catch (error) {
        console.error('API request failed:', error);
        
        // Don't show the default error message for rate limiting
        if (error.message !== 'rate_limit_exceeded' && 
            error.message !== 'unauthorized' && 
            error.message !== 'forbidden') {
          message.error(error.message || 'An error occurred while processing your request');
        }
        
        throw error;
      } finally {
        setLoading(false);
      }
    },
    [baseUrl, defaultHeaders, deviceFingerprint, updateRateLimits]
  );

  // Convenience methods for common HTTP methods
  const get = useCallback(
    (endpoint, options = {}) => secureFetch(endpoint, { ...options, method: 'GET' }),
    [secureFetch]
  );

  const post = useCallback(
    (endpoint, body, options = {}) =>
      secureFetch(endpoint, { ...options, method: 'POST', body }),
    [secureFetch]
  );

  const put = useCallback(
    (endpoint, body, options = {}) =>
      secureFetch(endpoint, { ...options, method: 'PUT', body }),
    [secureFetch]
  );

  const del = useCallback(
    (endpoint, options = {}) =>
      secureFetch(endpoint, { ...options, method: 'DELETE' }),
    [secureFetch]
  );

  const patch = useCallback(
    (endpoint, body, options = {}) =>
      secureFetch(endpoint, { ...options, method: 'PATCH', body }),
    [secureFetch]
  );

  return {
    secureFetch,
    get,
    post,
    put,
    del,
    patch,
    loading,
  };
};

export default useSecureFetch;
