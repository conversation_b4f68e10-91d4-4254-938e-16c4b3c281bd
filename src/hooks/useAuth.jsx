import { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { authAPI } from '../lib/api';

// Key for storing API key in localStorage
const API_KEY_STORAGE_KEY = 'admin_api_key';

export function useAuth() {
  const [apiKey, setApiKey] = useState(null);
  const [admin, setAdmin] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load API key from localStorage on initial render
  useEffect(() => {
    const storedApiKey = localStorage.getItem(API_KEY_STORAGE_KEY);
    if (storedApiKey) {
      setApiKey(storedApiKey);
      // Try to validate the API key
      validateApiKey(storedApiKey).catch(() => {
        // If validation fails, clear the invalid key
        logout();
      });
    } else {
      setIsLoading(false);
    }
  }, []);

  // Validate API key and get admin profile
  const validateApiKey = useCallback(async (keyToValidate) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Make an authenticated request to get the admin profile
      const { admin } = await authAPI.getProfile(keyToValidate);
      
      // If successful, update state
      setAdmin(admin);
      setApiKey(keyToValidate);
      localStorage.setItem(API_KEY_STORAGE_KEY, keyToValidate);
      
      return { success: true, admin };
    } catch (error) {
      console.error('API key validation failed:', error);
      setError(error.message || 'Failed to validate API key');
      logout();
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Login with API key
  const login = useCallback(async (apiKey) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Try to authenticate with the API key
      const { token, admin } = await authAPI.login(apiKey);
      
      // Store the API key and update state
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey);
      setApiKey(apiKey);
      setAdmin(admin);
      
      return { success: true, admin };
    } catch (error) {
      console.error('Login failed:', error);
      setError(error.message || 'Login failed. Please check your API key and try again.');
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Logout
  const logout = useCallback(() => {
    localStorage.removeItem(API_KEY_STORAGE_KEY);
    setApiKey(null);
    setAdmin(null);
    setError(null);
  }, []);

  // Check if user has specific permission
  const hasPermission = useCallback((permission) => {
    if (!admin?.permissions) return false;
    
    // If admin has all permissions
    if (admin.permissions.includes('*')) return true;
    
    // Check for specific permission
    return admin.permissions.includes(permission);
  }, [admin]);

  return {
    apiKey,
    admin,
    isAuthenticated: !!admin,
    isLoading,
    error,
    login,
    logout,
    validateApiKey,
    hasPermission,
  };
}

// Create an AuthProvider component to wrap the app
export function AuthProvider({ children }) {
  const auth = useAuth();
  
  // You can add global auth state or side effects here
  
  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
}

// Create a context for the auth state
export const AuthContext = createContext(null);

// Custom hook to use the auth context
export function useAuthContext() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}
