import { useState, useCallback } from 'react';
import { message } from 'antd';
import { useAuth } from './useAuth';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';

export function useApi() {
  const { token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Helper to handle API responses
  const handleResponse = async (response) => {
    const data = await response.json();
    
    if (!response.ok) {
      const error = new Error(data.message || 'Something went wrong');
      error.status = response.status;
      error.data = data;
      throw error;
    }
    
    return data;
  };

  // Generic request method
  const request = useCallback(async (endpoint, options = {}) => {
    setLoading(true);
    setError(null);
    
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers,
      });
      
      return await handleResponse(response);
    } catch (error) {
      console.error('API Error:', error);
      setError(error);
      message.error(error.message || 'An error occurred');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [token]);

  // HTTP methods
  const get = useCallback((endpoint, options = {}) => {
    return request(endpoint, {
      ...options,
      method: 'GET',
    });
  }, [request]);

  const post = useCallback((endpoint, data, options = {}) => {
    return request(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
    });
  }, [request]);

  const put = useCallback((endpoint, data, options = {}) => {
    return request(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }, [request]);

  const del = useCallback((endpoint, options = {}) => {
    return request(endpoint, {
      ...options,
      method: 'DELETE',
    });
  }, [request]);

  return {
    loading,
    error,
    get,
    post,
    put,
    delete: del,
  };
}
