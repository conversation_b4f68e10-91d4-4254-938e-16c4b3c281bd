import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'X-HWID, Content-Type');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const { slug } = req.query;
  const action = slug?.[0] || 'check';
  const hwid = req.headers['x-hwid'];

  if (!hwid) {
    return res.status(400).json({ error: 'HWID is required' });
  }

  try {
    switch (action) {
      case 'check':
        return await handleCheckRateLimit(req, res, hwid);
      case 'stats':
        return await handleGetStats(req, res, hwid);
      default:
        return res.status(404).json({ error: 'Not found' });
    }
  } catch (error) {
    console.error('Rate limit API error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function handleCheckRateLimit(req, res, hwid) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Check rate limit using Supabase function
    const { data, error } = await supabase.rpc('check_rate_limit', {
      p_hwid: hwid,
      p_window_seconds: 900, // 15 minutes
      p_max_requests: 100
    });

    if (error) throw error;

    const rateLimit = data[0];
    const resetTime = Math.floor(new Date(rateLimit.reset_time).getTime() / 1000);

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', '100');
    res.setHeader('X-RateLimit-Remaining', rateLimit.remaining);
    res.setHeader('X-RateLimit-Reset', resetTime);

    if (!rateLimit.is_allowed) {
      res.setHeader('Retry-After', rateLimit.retry_after_seconds);
      return res.status(429).json({
        error: 'Too many requests',
        retryAfter: rateLimit.retry_after_seconds
      });
    }

    return res.status(200).json({
      remaining: rateLimit.remaining,
      limit: 100,
      resetTime: resetTime
    });
  } catch (error) {
    console.error('Rate limit check failed:', error);
    return res.status(500).json({ 
      error: 'Failed to check rate limit',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function handleGetStats(req, res, hwid) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Only allow admins to view stats
    const { data: { user } } = await supabase.auth.getUser(req.headers.authorization?.split(' ')[1]);
    
    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Get rate limit stats
    const { data, error } = await supabase.rpc('get_rate_limit_stats', {
      p_hwid: hwid,
      p_days: 7
    });

    if (error) throw error;

    return res.status(200).json(data);
  } catch (error) {
    console.error('Failed to get rate limit stats:', error);
    return res.status(500).json({ 
      error: 'Failed to get rate limit stats',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

// Log rate limit events (called from middleware)
export async function logRateLimitEvent(eventData) {
  try {
    const { data, error } = await supabase.rpc('log_rate_limit_event', {
      p_hwid: eventData.hwid,
      p_ip: eventData.ip,
      p_path: eventData.path,
      p_method: eventData.method,
      p_user_agent: eventData.userAgent,
      p_metadata: eventData.metadata || {}
    });

    if (error) {
      console.error('Failed to log rate limit event:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error logging rate limit event:', error);
    return null;
  }
}
