import { createRouter } from 'next-connect';
import { supabase } from '../../../lib/supabaseClient';
import { v4 as uuidv4 } from 'uuid';
import { createHash } from 'crypto';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};

const router = createRouter();

// Helper function to hash data
const hashData = (data) => {
  return createHash('sha256').update(data).digest('hex');
};

// Generate a new API key
router.post(async (req, res) => {
  try {
    // Verify admin authentication
    const apiKey = req.headers[process.env.API_KEY_HEADER?.toLowerCase() || 'x-api-key'];
    if (!apiKey || !process.env.ADMIN_API_KEYS?.split(',').includes(apiKey)) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { userId, serviceType, expiresInDays } = req.body;
    
    // Validate input
    if (!userId || !serviceType) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Generate key components
    const keyId = uuidv4();
    const secret = uuidv4().replace(/-/g, '');
    const keyPrefix = process.env.KEY_PREFIX || 'PL_';
    const keyValue = `${keyPrefix}${keyId}${secret}`;
    
    // Hash the key for secure storage
    const keyHash = hashData(keyValue);
    
    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + (expiresInDays || parseInt(process.env.KEY_EXPIRY_DAYS) || 30));

    // Store key in database
    const { data, error } = await supabase
      .from('api_keys')
      .insert([
        {
          id: keyId,
          user_id: userId,
          key_hash: keyHash,
          service_type: serviceType,
          expires_at: expiresAt.toISOString(),
          max_uses: parseInt(process.env.KEY_MAX_USES) || 1000,
          uses: 0,
          is_active: true,
          metadata: {}
        }
      ])
      .select();

    if (error) throw error;

    // Return the key (only shown once)
    res.status(201).json({
      id: keyId,
      key: keyValue, // Only time the full key is shown
      serviceType,
      expiresAt: expiresAt.toISOString(),
      maxUses: parseInt(process.env.KEY_MAX_USES) || 1000
    });
  } catch (error) {
    console.error('Error generating API key:', error);
    res.status(500).json({ error: 'Failed to generate API key', details: error.message });
  }
});

// Validate an API key
router.get('/validate/:key', async (req, res) => {
  try {
    const { key } = req.query;
    
    if (!key) {
      return res.status(400).json({ error: 'API key is required' });
    }

    // Hash the provided key for comparison
    const keyHash = hashData(key);
    
    // Look up the key in the database
    const { data: keyData, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('key_hash', keyHash)
      .single();

    if (error || !keyData) {
      return res.status(404).json({ valid: false, error: 'Invalid API key' });
    }

    // Check if key is active and not expired
    const now = new Date();
    const expiresAt = new Date(keyData.expires_at);
    
    if (!keyData.is_active || (expiresAt && now > expiresAt)) {
      return res.status(403).json({ 
        valid: false, 
        error: 'API key is expired or deactivated' 
      });
    }

    // Check usage limits
    if (keyData.max_uses > 0 && keyData.uses >= keyData.max_uses) {
      return res.status(403).json({ 
        valid: false, 
        error: 'API key usage limit reached' 
      });
    }

    // Increment usage counter
    await supabase
      .from('api_keys')
      .update({ uses: keyData.uses + 1 })
      .eq('id', keyData.id);

    // Return validation success with key metadata
    res.json({
      valid: true,
      keyId: keyData.id,
      userId: keyData.user_id,
      serviceType: keyData.service_type,
      uses: keyData.uses + 1,
      maxUses: keyData.max_uses,
      expiresAt: keyData.expires_at,
      createdAt: keyData.created_at
    });
  } catch (error) {
    console.error('Error validating API key:', error);
    res.status(500).json({ valid: false, error: 'Failed to validate API key' });
  }
});

export default router.handler({
  onError: (err, req, res) => {
    console.error(err.stack);
    res.status(500).end('Something broke!');
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Page is not found');
  },
});
