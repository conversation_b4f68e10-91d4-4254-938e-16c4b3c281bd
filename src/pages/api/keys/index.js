import { createRouter } from 'next-connect';
import { supabase } from '../../../lib/supabaseClient';
import { v4 as uuidv4 } from 'uuid';
import { createHash, randomBytes, timingSafeEqual } from 'crypto';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};

const router = createRouter();

// Helper function to hash data with salt
const hashData = (data, salt = process.env.KEY_SALT || 'default-salt') => {
  return createHash('sha256').update(data + salt).digest('hex');
};

// Generate cryptographically secure API key
const generateSecureKey = () => {
  // Generate 32 bytes of cryptographically secure random data
  const randomData = randomBytes(32);
  const timestamp = Date.now().toString(36);
  const keyPrefix = process.env.KEY_PREFIX || 'PM_';

  // Create key with timestamp and random data
  const keyValue = `${keyPrefix}${timestamp}_${randomData.toString('hex')}`;

  return keyValue;
};

// Rate limiting storage (in production, use Redis)
const rateLimitStore = new Map();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5; // 5 requests per minute per IP

// Rate limiting middleware
const checkRateLimit = (req) => {
  const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
  const now = Date.now();
  const windowStart = now - RATE_LIMIT_WINDOW;

  // Clean old entries
  for (const [ip, timestamps] of rateLimitStore.entries()) {
    const validTimestamps = timestamps.filter(ts => ts > windowStart);
    if (validTimestamps.length > 0) {
      rateLimitStore.set(ip, validTimestamps);
    } else {
      rateLimitStore.delete(ip);
    }
  }

  // Check current IP
  const ipRequests = rateLimitStore.get(clientIp) || [];
  if (ipRequests.length >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }

  // Add current request
  ipRequests.push(now);
  rateLimitStore.set(clientIp, ipRequests);
  return true;
};

// Generate a new API key
router.post(async (req, res) => {
  try {
    // Check rate limiting first
    if (!checkRateLimit(req)) {
      return res.status(429).json({
        error: 'Too many requests. Please try again later.',
        retryAfter: Math.ceil(RATE_LIMIT_WINDOW / 1000)
      });
    }

    // Verify admin authentication with timing-safe comparison
    const providedApiKey = req.headers[process.env.API_KEY_HEADER?.toLowerCase() || 'x-api-key'];
    if (!providedApiKey) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const validApiKeys = process.env.ADMIN_API_KEYS?.split(',') || [];
    let isValidKey = false;

    for (const validKey of validApiKeys) {
      if (providedApiKey.length === validKey.length) {
        try {
          const providedBuffer = Buffer.from(providedApiKey, 'utf8');
          const validBuffer = Buffer.from(validKey, 'utf8');
          if (timingSafeEqual(providedBuffer, validBuffer)) {
            isValidKey = true;
            break;
          }
        } catch (e) {
          // Continue to next key
        }
      }
    }

    if (!isValidKey) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { userId, serviceType, expiresInDays, metadata = {} } = req.body;

    // Enhanced input validation
    if (!userId || typeof userId !== 'string' || userId.length < 1 || userId.length > 100) {
      return res.status(400).json({ error: 'Invalid userId' });
    }

    if (!serviceType || !['linkvertise', 'shrinkme', 'standard'].includes(serviceType)) {
      return res.status(400).json({ error: 'Invalid serviceType' });
    }

    if (expiresInDays && (typeof expiresInDays !== 'number' || expiresInDays < 1 || expiresInDays > 365)) {
      return res.status(400).json({ error: 'Invalid expiresInDays (1-365)' });
    }

    // Generate cryptographically secure key
    const keyId = uuidv4();
    const keyValue = generateSecureKey();

    // Hash the key for secure storage
    const keyHash = hashData(keyValue);
    
    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + (expiresInDays || parseInt(process.env.KEY_EXPIRY_DAYS) || 30));

    // Store key in database
    const { data, error } = await supabase
      .from('api_keys')
      .insert([
        {
          id: keyId,
          user_id: userId,
          key_hash: keyHash,
          service_type: serviceType,
          expires_at: expiresAt.toISOString(),
          max_uses: parseInt(process.env.KEY_MAX_USES) || 1000,
          uses: 0,
          is_active: true,
          metadata: {}
        }
      ])
      .select();

    if (error) throw error;

    // Return the key (only shown once)
    res.status(201).json({
      id: keyId,
      key: keyValue, // Only time the full key is shown
      serviceType,
      expiresAt: expiresAt.toISOString(),
      maxUses: parseInt(process.env.KEY_MAX_USES) || 1000
    });
  } catch (error) {
    console.error('Error generating API key:', error);
    res.status(500).json({ error: 'Failed to generate API key', details: error.message });
  }
});

// Validate an API key
router.get('/validate/:key', async (req, res) => {
  try {
    const { key } = req.query;
    
    if (!key) {
      return res.status(400).json({ error: 'API key is required' });
    }

    // Hash the provided key for comparison
    const keyHash = hashData(key);
    
    // Look up the key in the database
    const { data: keyData, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('key_hash', keyHash)
      .single();

    if (error || !keyData) {
      return res.status(404).json({ valid: false, error: 'Invalid API key' });
    }

    // Check if key is active and not expired
    const now = new Date();
    const expiresAt = new Date(keyData.expires_at);
    
    if (!keyData.is_active || (expiresAt && now > expiresAt)) {
      return res.status(403).json({ 
        valid: false, 
        error: 'API key is expired or deactivated' 
      });
    }

    // Check usage limits
    if (keyData.max_uses > 0 && keyData.uses >= keyData.max_uses) {
      return res.status(403).json({ 
        valid: false, 
        error: 'API key usage limit reached' 
      });
    }

    // Increment usage counter
    await supabase
      .from('api_keys')
      .update({ uses: keyData.uses + 1 })
      .eq('id', keyData.id);

    // Return validation success with key metadata
    res.json({
      valid: true,
      keyId: keyData.id,
      userId: keyData.user_id,
      serviceType: keyData.service_type,
      uses: keyData.uses + 1,
      maxUses: keyData.max_uses,
      expiresAt: keyData.expires_at,
      createdAt: keyData.created_at
    });
  } catch (error) {
    console.error('Error validating API key:', error);
    res.status(500).json({ valid: false, error: 'Failed to validate API key' });
  }
});

export default router.handler({
  onError: (err, req, res) => {
    console.error(err.stack);
    res.status(500).end('Something broke!');
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Page is not found');
  },
});
