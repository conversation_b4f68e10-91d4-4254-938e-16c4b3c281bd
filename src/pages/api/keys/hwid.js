import { createRouter } from 'next-connect';
import { supabase } from '../../../lib/supabaseClient';
import { createHash } from 'crypto';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};

const router = createRouter();

// Helper function to hash HWID
const hashHwid = (hwid) => {
  if (!hwid) return null;
  return createHash('sha256').update(hwid).digest('hex');
};

// Reset HWID for a key (admin only)
router.post('/reset', async (req, res) => {
  try {
    // Verify admin authentication
    const apiKey = req.headers[process.env.API_KEY_HEADER?.toLowerCase() || 'x-api-key'];
    if (!apiKey || !process.env.ADMIN_API_KEYS?.split(',').includes(apiKey)) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { keyId, reason } = req.body;
    
    if (!keyId) {
      return res.status(400).json({ error: 'Key ID is required' });
    }

    // Clear the HWID for the key
    const { data, error } = await supabase
      .from('api_keys')
      .update({ 
        hwid: null,
        metadata: supabase.rpc('jsonb_set', {
          jsonb: 'metadata',
          path: '{hwid_reset_history}',
          value: supabase.rpc('jsonb_append', {
            jsonb: 'metadata->hwid_reset_history',
            new_element: {
              timestamp: new Date().toISOString(),
              reason: reason || 'Admin reset',
              reset_by: 'admin'
            }
          })
        })
      })
      .eq('id', keyId)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Key not found' });
    }

    res.json({ 
      success: true, 
      message: 'HWID reset successfully',
      keyId
    });
  } catch (error) {
    console.error('Error resetting HWID:', error);
    res.status(500).json({ error: 'Failed to reset HWID', details: error.message });
  }
});

// Validate HWID for a key
router.post('/validate', async (req, res) => {
  try {
    const { keyId, hwid } = req.body;
    
    if (!keyId || !hwid) {
      return res.status(400).json({ error: 'Key ID and HWID are required' });
    }

    // Look up the key in the database
    const { data: keyData, error } = await supabase
      .from('api_keys')
      .select('id, hwid, metadata')
      .eq('id', keyId)
      .single();

    if (error || !keyData) {
      return res.status(404).json({ 
        valid: false, 
        error: 'Key not found' 
      });
    }

    // If HWID is not set yet, this is the first validation
    if (!keyData.hwid) {
      // Set the HWID for this key
      const hashedHwid = hashHwid(hwid);
      
      const { error: updateError } = await supabase
        .from('api_keys')
        .update({ 
          hwid: hashedHwid,
          metadata: {
            ...keyData.metadata,
            hwid_set_at: new Date().toISOString()
          }
        })
        .eq('id', keyId);

      if (updateError) throw updateError;

      return res.json({ 
        valid: true, 
        message: 'HWID set successfully',
        isNewHwid: true
      });
    }

    // For existing HWID, validate the provided HWID matches
    const hashedHwid = hashHwid(hwid);
    const isValid = keyData.hwid === hashedHwid;

    if (!isValid) {
      // Log failed validation attempt
      await supabase
        .from('security_events')
        .insert([
          {
            event_type: 'hwid_validation_failed',
            key_id: keyId,
            metadata: {
              provided_hwid: hashedHwid,
              expected_hwid: keyData.hwid,
              ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
            }
          }
        ]);
    }

    res.json({ 
      valid: isValid,
      requiresReset: !isValid,
      message: isValid ? 'HWID is valid' : 'HWID does not match'
    });
  } catch (error) {
    console.error('Error validating HWID:', error);
    res.status(500).json({ 
      valid: false, 
      error: 'Failed to validate HWID',
      details: error.message 
    });
  }
});

// Get HWID reset history for a key (admin only)
router.get('/history/:keyId', async (req, res) => {
  try {
    // Verify admin authentication
    const apiKey = req.headers[process.env.API_KEY_HEADER?.toLowerCase() || 'x-api-key'];
    if (!apiKey || !process.env.ADMIN_API_KEYS?.split(',').includes(apiKey)) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { keyId } = req.params;
    
    // Get the key with HWID reset history
    const { data, error } = await supabase
      .from('api_keys')
      .select('metadata->hwid_reset_history')
      .eq('id', keyId)
      .single();

    if (error) throw error;

    res.json({
      keyId,
      resetHistory: data?.metadata?.hwid_reset_history || []
    });
  } catch (error) {
    console.error('Error fetching HWID history:', error);
    res.status(500).json({ 
      error: 'Failed to fetch HWID history',
      details: error.message 
    });
  }
});

export default router.handler({
  onError: (err, req, res) => {
    console.error(err.stack);
    res.status(500).end('Something broke!');
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Page is not found');
  },
});
