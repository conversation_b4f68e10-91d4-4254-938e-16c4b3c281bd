import { createRouter } from 'next-connect';
import { supabase } from '../../../lib/supabaseClient';
import { createHash } from 'crypto';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};

const router = createRouter();

// Helper function to hash HWID with salt and validation
const hashHwid = (hwid) => {
  if (!hwid) return null;

  // Validate HWID format (should be hex string of specific length)
  if (typeof hwid !== 'string' || !/^[a-f0-9]{64}$/i.test(hwid)) {
    throw new Error('Invalid HWID format');
  }

  const salt = process.env.HWID_SALT || 'default-hwid-salt';
  return createHash('sha256').update(hwid + salt).digest('hex');
};

// Validate HWID format and content
const validateHwidFormat = (hwid) => {
  if (!hwid || typeof hwid !== 'string') {
    return { valid: false, error: 'HWID is required and must be a string' };
  }

  if (hwid.length < 32 || hwid.length > 128) {
    return { valid: false, error: 'HWID length must be between 32 and 128 characters' };
  }

  if (!/^[a-f0-9]+$/i.test(hwid)) {
    return { valid: false, error: 'HWID must contain only hexadecimal characters' };
  }

  return { valid: true };
};

// Reset HWID for a key (admin only)
router.post('/reset', async (req, res) => {
  try {
    // Verify admin authentication
    const apiKey = req.headers[process.env.API_KEY_HEADER?.toLowerCase() || 'x-api-key'];
    if (!apiKey || !process.env.ADMIN_API_KEYS?.split(',').includes(apiKey)) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { keyId, reason } = req.body;
    
    if (!keyId) {
      return res.status(400).json({ error: 'Key ID is required' });
    }

    // Clear the HWID for the key
    const { data, error } = await supabase
      .from('api_keys')
      .update({ 
        hwid: null,
        metadata: supabase.rpc('jsonb_set', {
          jsonb: 'metadata',
          path: '{hwid_reset_history}',
          value: supabase.rpc('jsonb_append', {
            jsonb: 'metadata->hwid_reset_history',
            new_element: {
              timestamp: new Date().toISOString(),
              reason: reason || 'Admin reset',
              reset_by: 'admin'
            }
          })
        })
      })
      .eq('id', keyId)
      .select();

    if (error) throw error;

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Key not found' });
    }

    res.json({ 
      success: true, 
      message: 'HWID reset successfully',
      keyId
    });
  } catch (error) {
    console.error('Error resetting HWID:', error);
    res.status(500).json({ error: 'Failed to reset HWID', details: error.message });
  }
});

// Validate HWID for a key
router.post('/validate', async (req, res) => {
  try {
    const { keyId, hwid } = req.body;

    if (!keyId || !hwid) {
      return res.status(400).json({ error: 'Key ID and HWID are required' });
    }

    // Validate HWID format
    const hwidValidation = validateHwidFormat(hwid);
    if (!hwidValidation.valid) {
      return res.status(400).json({ error: hwidValidation.error });
    }

    // Use transaction to prevent race conditions
    const { data: keyData, error } = await supabase
      .from('api_keys')
      .select('id, hwid, metadata, is_active, expires_at')
      .eq('id', keyId)
      .single();

    if (error || !keyData) {
      return res.status(404).json({
        valid: false,
        error: 'Key not found'
      });
    }

    // Check if key is still active and not expired
    if (!keyData.is_active) {
      return res.status(403).json({
        valid: false,
        error: 'Key is deactivated'
      });
    }

    if (keyData.expires_at && new Date(keyData.expires_at) < new Date()) {
      return res.status(403).json({
        valid: false,
        error: 'Key has expired'
      });
    }

    // If HWID is not set yet, this is the first validation
    if (!keyData.hwid) {
      try {
        const hashedHwid = hashHwid(hwid);

        // Use atomic update with condition to prevent race conditions
        const { data: updateResult, error: updateError } = await supabase
          .from('api_keys')
          .update({
            hwid: hashedHwid,
            metadata: {
              ...keyData.metadata,
              hwid_set_at: new Date().toISOString(),
              hwid_set_ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
            }
          })
          .eq('id', keyId)
          .eq('hwid', null) // Only update if HWID is still null
          .select();

        if (updateError) throw updateError;

        if (!updateResult || updateResult.length === 0) {
          // Another request already set the HWID, validate against existing
          const { data: refreshedData } = await supabase
            .from('api_keys')
            .select('hwid')
            .eq('id', keyId)
            .single();

          if (refreshedData && refreshedData.hwid) {
            const isValid = refreshedData.hwid === hashedHwid;
            return res.json({
              valid: isValid,
              requiresReset: !isValid,
              message: isValid ? 'HWID matches existing' : 'HWID conflict detected'
            });
          }
        }

        return res.json({
          valid: true,
          message: 'HWID set successfully',
          isNewHwid: true
        });
      } catch (hwidError) {
        return res.status(400).json({
          valid: false,
          error: 'Invalid HWID format'
        });
      }
    }

    // For existing HWID, validate the provided HWID matches
    const hashedHwid = hashHwid(hwid);
    const isValid = keyData.hwid === hashedHwid;

    if (!isValid) {
      // Log failed validation attempt
      await supabase
        .from('security_events')
        .insert([
          {
            event_type: 'hwid_validation_failed',
            key_id: keyId,
            metadata: {
              provided_hwid: hashedHwid,
              expected_hwid: keyData.hwid,
              ip: req.headers['x-forwarded-for'] || req.socket.remoteAddress
            }
          }
        ]);
    }

    res.json({ 
      valid: isValid,
      requiresReset: !isValid,
      message: isValid ? 'HWID is valid' : 'HWID does not match'
    });
  } catch (error) {
    console.error('Error validating HWID:', error);
    res.status(500).json({ 
      valid: false, 
      error: 'Failed to validate HWID',
      details: error.message 
    });
  }
});

// Get HWID reset history for a key (admin only)
router.get('/history/:keyId', async (req, res) => {
  try {
    // Verify admin authentication
    const apiKey = req.headers[process.env.API_KEY_HEADER?.toLowerCase() || 'x-api-key'];
    if (!apiKey || !process.env.ADMIN_API_KEYS?.split(',').includes(apiKey)) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { keyId } = req.params;
    
    // Get the key with HWID reset history
    const { data, error } = await supabase
      .from('api_keys')
      .select('metadata->hwid_reset_history')
      .eq('id', keyId)
      .single();

    if (error) throw error;

    res.json({
      keyId,
      resetHistory: data?.metadata?.hwid_reset_history || []
    });
  } catch (error) {
    console.error('Error fetching HWID history:', error);
    res.status(500).json({ 
      error: 'Failed to fetch HWID history',
      details: error.message 
    });
  }
});

export default router.handler({
  onError: (err, req, res) => {
    console.error(err.stack);
    res.status(500).end('Something broke!');
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Page is not found');
  },
});
