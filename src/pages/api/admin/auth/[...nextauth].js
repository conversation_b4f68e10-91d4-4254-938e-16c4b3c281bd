import NextAuth from 'next-auth';
import { createClient } from '@supabase/supabase-js';
import { compare } from 'bcryptjs';
import { getClientIp } from '@/utils/request-utils';
import { getHWID } from '@/utils/hwid';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export default NextAuth({
  session: {
    strategy: 'jwt',  // Use JSON Web Tokens for session management
    maxAge: 30 * 60,  // 30 minutes session duration
  },
  providers: [
    {
      id: 'credentials',
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        try {
          const { email, password } = credentials;
          const ip = getClientIp(req);
          const userAgent = req.headers['user-agent'] || '';
          
          // Get admin by email
          const { data: admin, error } = await supabase
            .from('admin_profile')
            .select('*')
            .eq('email', email.toLowerCase())
            .single();

          if (error || !admin) {
            console.error('Admin not found:', error?.message);
            return null;
          }

          // Verify password
          const isValid = await compare(password, admin.password_hash);
          if (!isValid) {
            console.error('Invalid password for admin:', email);
            return null;
          }

          // Generate HWID for this session
          const hwid = await getHWID(ip, userAgent);
          
          // Update last login info
          const { error: updateError } = await supabase
            .from('admin_profile')
            .update({
              last_login_at: new Date().toISOString(),
              last_login_ip: ip,
              login_count: (admin.login_count || 0) + 1,
              hwid,
              metadata: {
                ...admin.metadata,
                last_user_agent: userAgent,
                last_login_at: new Date().toISOString()
              }
            })
            .eq('id', admin.id);

          if (updateError) {
            console.error('Failed to update admin login info:', updateError.message);
          }

          // Log the login activity
          const { error: logError } = await supabase.rpc('log_activity', {
            action: 'admin.login',
            entity_type: 'admin_profile',
            entity_id: admin.id,
            ip_address: ip,
            user_agent: userAgent,
            hwid,
            details: JSON.stringify({
              login_count: (admin.login_count || 0) + 1,
              user_agent: userAgent
            })
          });

          if (logError) {
            console.error('Failed to log admin login:', logError.message);
          }

          // Return user object that will be saved in session
          return {
            id: admin.id,
            email: admin.email,
            name: admin.full_name || admin.username,
            role: 'admin',
            image: admin.avatar_url
          };
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      }
    }
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Initial sign in
      if (user) {
        token.id = user.id;
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      // Add custom session properties
      if (session?.user) {
        session.user.id = token.id;
        session.user.role = token.role;
      }
      return session;
    },
  },
  pages: {
    signIn: '/admin/login',  // Custom sign-in page
    error: '/admin/error',   // Error page
  },
  debug: process.env.NODE_ENV === 'development',
  secret: process.env.NEXTAUTH_SECRET,
});
