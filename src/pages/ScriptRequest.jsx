import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button } from '../components/ui/Button';
import { FiAlertCircle, FiCheckCircle, FiInfo, FiHome } from 'react-icons/fi';

const ScriptRequest = () => {
  const navigate = useNavigate();

  
  const [formData, setFormData] = useState({
    gameName: '',
    discordUsername: '',
    robloxUsername: '',
    gameLink: '',
    description: '',
    captcha: '',
    agreeToTerms: false,
  });
  
  const [status, setStatus] = useState({ type: '', message: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [requestId, setRequestId] = useState(null);
  
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    if (status.message) {
      setStatus({ type: '', message: '' });
    }
  };
  
  const validateForm = () => {
    if (!formData.gameName.trim()) {
      setStatus({ type: 'error', message: 'Please enter the Roblox game name' });
      return false;
    }
    
    if (!formData.robloxUsername.trim()) {
      setStatus({ type: 'error', message: 'Please provide your Roblox username' });
      return false;
    }
    
    if (!formData.description.trim() || formData.description.trim().length < 30) {
      setStatus({ type: 'error', message: 'Please provide a detailed description (at least 30 characters)' });
      return false;
    }
    
    if (!formData.agreeToTerms) {
      setStatus({ type: 'error', message: 'You must agree to the terms and conditions' });
      return false;
    }
    
    return true;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setStatus({ type: 'loading', message: 'Submitting your request...' });
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/.netlify/functions/submit-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          script_name: formData.gameName,
          requested_by: formData.robloxUsername, // Or Discord, depending on what you want to track
          service: 'roblox', // This can be dynamic if you have multiple services
          notes: formData.description,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit request.');
      }

      setRequestId(result.requestId);
      setStatus({ type: 'success', message: `Your request has been submitted successfully! Your tracking ID is: ${result.requestId}` });

      // Reset form
      setFormData({
        gameName: '',
        discordUsername: '',
        robloxUsername: '',
        gameLink: '',
        description: '',
        captcha: '',
        agreeToTerms: false,
      });

    } catch (error) {
      console.error('Error submitting request:', error);
      setStatus({ type: 'error', message: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleTrackRequest = () => {
    if (requestId) {
      navigate(`/request-status?id=${requestId}`);
    } else {
      setStatus({ type: 'error', message: 'Please submit your request first to get a tracking ID.' });
    }
  };
  
  return (
    <div className="min-h-screen pt-16">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-sm p-6 md:p-8">
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                Request a Roblox Script
              </h1>
              <p className="text-foreground/70">
                Can't find the script you need? Request a custom Roblox script and our team will consider adding it to our collection.
              </p>
            </div>
            
            {status.message && (
              <div className={`mb-6 p-4 rounded-md ${
                status.type === 'error' ? 'bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200' :
                status.type === 'success' ? 'bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200' :
                'bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
              }`}>
                <div className="flex items-center">
                  {status.type === 'error' && <FiAlertCircle className="h-5 w-5 mr-2" />}
                  {status.type === 'success' && <FiCheckCircle className="h-5 w-5 mr-2" />}
                  {status.type === 'loading' && (
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  <span>{status.message}</span>
                </div>
                {status.type === 'success' && requestId && (
                  <div className="mt-2">
                    <p className="text-sm">Your request ID: <span className="font-mono font-bold">{requestId}</span></p>
                    <button
                      type="button"
                      onClick={handleTrackRequest}
                      className="mt-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Track your request status
                    </button>
                  </div>
                )}
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-6">
                <div>
                  <label htmlFor="gameName" className="block text-sm font-medium mb-1">
                    Roblox Game Name *
                  </label>
                  <input
                    type="text"
                    id="gameName"
                    name="gameName"
                    value={formData.gameName}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                    placeholder="Enter the name of the Roblox game"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="discordUsername" className="block text-sm font-medium mb-1">
                      Discord Username (Optional)
                    </label>
                    <input
                      type="text"
                      id="discordUsername"
                      name="discordUsername"
                      value={formData.discordUsername}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                      placeholder="YourDiscord#0000"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="robloxUsername" className="block text-sm font-medium mb-1">
                      Roblox Username *
                    </label>
                    <input
                      type="text"
                      id="robloxUsername"
                      name="robloxUsername"
                      value={formData.robloxUsername}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                      placeholder="Your Roblox username"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="gameLink" className="block text-sm font-medium mb-1">
                    Game Link (Optional)
                  </label>
                  <input
                    type="url"
                    id="gameLink"
                    name="gameLink"
                    value={formData.gameLink}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                    placeholder="https://www.roblox.com/games/..."
                  />
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium mb-1">
                    Detailed Description *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={5}
                    value={formData.description}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                    placeholder="Please describe in detail what you want the script to do..."
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Minimum 30 characters. Be as detailed as possible.
                  </p>
                </div>
                
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <FiInfo className="h-5 w-5 text-blue-500" aria-hidden="true" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Important Information
                      </h3>
                      <div className="mt-2 text-sm text-blue-700 dark:text-blue-300 space-y-2">
                        <p>• Please be patient as our team reviews your request.</p>
                        <p>• We prioritize popular game scripts that benefit the community.</p>
                        <p>• You'll be notified via Discord when your request is reviewed.</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="agreeToTerms"
                      name="agreeToTerms"
                      type="checkbox"
                      checked={formData.agreeToTerms}
                      onChange={handleChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:bg-gray-800 dark:border-gray-700"
                      required
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="agreeToTerms" className="font-medium text-foreground">
                      I agree to the{' '}
                      <a href="/terms" className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a href="/privacy" className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                        Privacy Policy
                      </a>
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Need help? Join our{' '}
                  <a 
                    href="https://discord.gg/scriptmaster" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline dark:text-blue-400"
                  >
                    Discord community
                  </a>
                </p>
                <div className="flex gap-3 w-full sm:w-auto">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/')}
                    className="w-full sm:w-auto"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full sm:w-auto"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Request'}
                  </Button>
                </div>
              </div>
              
              <div className="text-center mt-8">
                <Button
                  variant="ghost"
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  className="text-sm text-muted-foreground hover:text-foreground"
                >
                  Back to Top
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScriptRequest;
