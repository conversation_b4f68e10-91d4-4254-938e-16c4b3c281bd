import { motion } from 'framer-motion';
import { <PERSON> } from 'react-router-dom';
import { Fi<PERSON>rrowRight, FiCode, FiZap, FiShield, FiGithub, FiDownload, FiMail } from 'react-icons/fi';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import Team from '../components/Team';
import ExecutorsList from '../components/ExecutorsList';

const Home = () => {
  const features = [
    {
      icon: <FiCode className="h-8 w-8 text-primary" />,
      title: 'Roblox Scripts',
      description: 'High-performance Roblox scripts with advanced features.',
    },
    {
      icon: <FiZap className="h-8 w-8 text-primary" />,
      title: 'Optimized Performance',
      description: 'Our Roblox scripts are optimized for speed and efficiency, ensuring smooth gameplay.',
    },
    {
      icon: <FiShield className="h-8 w-8 text-primary" />,
      title: 'Safe & Secure',
      description: 'All our scripts are carefully tested to not get you banned.',
    },
  ];

  return (
    <div className="space-y-24 md:space-y-32">
      <section className="relative overflow-hidden pt-24 pb-16 md:pt-32 md:pb-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1 
              className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Elevate Your Roblox
              </span>
              <br />
              <span className="text-foreground">With Project Madara</span>
            </motion.h1>
            
            <motion.p 
              className="mt-6 text-lg text-foreground/80 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Discover premium Roblox scripts that take your gaming to the next level.
              Join thousands of Roblox players who trust Project Madara for the best scripting experience.
            </motion.p>
            
            <motion.div 
              className="mt-8 flex flex-col sm:flex-row justify-center gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Button 
                asChild 
                size="lg" 
                className="group px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5"
              >
                <Link to="/scripts" className="flex items-center">
                  Explore Roblox Scripts
                  <FiArrowRight className="ml-2 h-5 w-5 transition-all duration-300 group-hover:translate-x-1 group-hover:scale-110" />
                </Link>
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 md:py-20 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose Project Madara?</h2>
            <p className="text-foreground/70">
              We provide the most reliable and powerful Roblox scripts, with regular updates and dedicated support for the Roblox community.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full p-6 hover:shadow-md transition-shadow">
                  <div className="h-16 w-16 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-foreground/70">{feature.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <Team />

      {/* Executors List Section */}
      <ExecutorsList />

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-primary/5 to-secondary/5">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to enhance your Roblox experience?</h2>
            <p className="text-foreground/70 mb-8 max-w-2xl mx-auto">
              Join thousands of Roblox players who trust Project Madara for the best scripting solutions and gaming enhancements.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg" variant="outline" className="group px-6">
                <Link to="/faq" className="flex items-center">
                  Learn More
                  <FiArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="px-6 border-2 border-indigo-600/20 hover:border-indigo-600/40 bg-indigo-600/5 hover:bg-indigo-600/10 transition-all duration-300 group/github"
              >
                <a 
                  href="https://github.com/your-username/your-repo" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center"
                >
                  <FiGithub className="mr-2 h-5 w-5 text-indigo-600 group-hover/github:text-indigo-700 transition-all duration-300 group-hover/github:scale-110" />
                  <span className="text-indigo-600 group-hover/github:text-indigo-700 transition-colors duration-300 font-medium">
                    View on GitHub
                  </span>
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
