import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiArrowLeft, FiDownload, FiStar, FiClock, FiCode, FiUsers, FiAlertTriangle, FiCheckCircle } from 'react-icons/fi';
import { Badge } from '../components/ui/Badge';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';

const ScriptDetail = () => {
  const { id } = useParams();
  
  // In a real app, this would be fetched from an API
  const script = {
    id: 1,
    title: 'Drift Master Pro',
    description: 'Enhance your drifting experience with powerful features like auto drift, speed hacks, and teleportation.',
    longDescription: `This Drift Master Pro script provides a comprehensive set of features to enhance your racing experience. From auto drifting to teleportation, this script has everything you need to dominate the track.
          Key Features:
          - Auto Drift: Automatically maintain perfect drifts around corners
          - Speed Hack: Boost your top speed beyond normal limits
          - Teleport: Instantly teleport to any location on the map
          - Auto Win: Automatically win races with perfect runs
          - Nitro Hack: Unlimited nitro for continuous boosting
          - User-friendly interface with easy-to-use controls
          - Regular updates to ensure compatibility with the latest game version`,
    category: 'drift',
    views: '85,421',
    rating: 4.9,
    ratingCount: 5245,
    updated: '3 days ago',
    version: '2.1.0',
    fileSize: '1.8 MB',
    tags: ['Auto Drift', 'Speed Hack', 'Teleport', 'Racing', 'Script'],
    requirements: ['Windows 7/10/11', 'Roblox installed', 'Executor (Synapse X, KRNL recommended)'],
    features: [
      'Auto Drift: Automatically maintain perfect drifts around corners',
      'Speed Hack: Boost your top speed beyond normal limits',
      'Teleport: Instantly teleport to any location on the map',
      'Auto Win: Automatically win races with perfect runs',
      'Nitro Hack: Unlimited nitro for continuous boosting',
      'User-friendly interface with easy-to-use controls',
      'Regular updates to ensure compatibility with the latest game version'
    ],
    instructions: [
      'Download the script file using the button below',
      'Launch your preferred Roblox executor (Synapse X, KRNL, etc.)',
      'Join a Drift Racing game',
      'Inject the executor and paste the script',
      'Press Execute and configure the settings to your preference',
      'Enjoy enhanced gameplay!'
    ],
    compatibility: [
      { name: 'Synapse X', status: 'fully' },
      { name: 'KRNL', status: 'fully' },
      { name: 'JJsploit', status: 'partial' },
      { name: 'Fluxus', status: 'fully' },
      { name: 'Electron', status: 'fully' },
    ],
    changelog: [
      {
        version: '2.1.0',
        date: '2023-11-20',
        changes: [
          'Added support for the latest game update',
          'Improved auto drift efficiency by 25%',
          'Fixed teleportation glitches',
          'Added new track locations',
        ],
      },
      {
        version: '2.0.5',
        date: '2023-11-10',
        changes: [
          'Added auto win feature',
          'Improved UI/UX',
          'Fixed minor bugs',
          'Optimized performance',
        ],
      },
    ],
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Back Button */}
        <Link to="/scripts" className="inline-flex items-center text-primary hover:underline mb-6">
          <FiArrowLeft className="mr-2" />
          Back to Scripts
        </Link>

        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div>
              <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">{script.title}</h1>
            <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200">
              Drift Racing
            </span>
          </div>
              <p className="text-foreground/70">{script.description}</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button className="gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white">
                <FiDownload className="h-4 w-4" />
                Download Script
              </Button>
              <Button variant="outline" className="gap-2">
                <FiStar className="h-4 w-4" />
                Rate this script
              </Button>
              <Button className="gap-2">
                <FiDownload className="h-4 w-4" />
                Download (v{script.version})
              </Button>
            </div>
          </div>
          
          <div className="flex flex-wrap items-center gap-2 text-sm text-foreground/70">
            <div className="flex items-center">
              <FiStar className="h-4 w-4 text-yellow-500 mr-1" />
              <span className="font-medium">{script.rating}</span>
              <span className="ml-1">({script.ratingCount} ratings)</span>
            </div>
            <span>•</span>
            <div className="flex items-center">
              <FiDownload className="h-4 w-4 mr-1" />
              {script.downloads} downloads
            </div>
            <span>•</span>
            <div className="flex items-center">
              <FiClock className="h-4 w-4 mr-1" />
              Updated {script.updated}
            </div>
            <span>•</span>
            <div>{script.fileSize}</div>
          </div>
          
          <div className="flex flex-wrap gap-2 mt-4">
            {script.tags.map((tag) => (
              <Badge key={tag} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Description</h2>
              <div className="prose prose-sm dark:prose-invert max-w-none">
                {script.longDescription.split('\n\n').map((paragraph, i) => (
                  <p key={i} className="mb-4">{paragraph}</p>
                ))}
              </div>
            </Card>

            {/* Features */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {script.features.map((feature, i) => (
                  <div key={i} className="flex items-start">
                    <FiCheckCircle className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
            </Card>

            {/* Instructions */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">How to Install</h2>
              <ol className="space-y-3">
                {script.instructions.map((step, i) => (
                  <li key={i} className="flex">
                    <span className="flex items-center justify-center h-6 w-6 rounded-full bg-primary/10 text-primary font-medium text-sm mr-3 flex-shrink-0">
                      {i + 1}
                    </span>
                    <span>{step}</span>
                  </li>
                ))}
              </ol>
            </Card>

            {/* Changelog */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">Changelog</h2>
              <div className="space-y-6">
                {script.changelog.map((update, i) => (
                  <div key={i} className="border-l-2 border-primary/20 pl-4">
                    <div className="flex justify-between items-baseline">
                      <h3 className="font-medium">Version {update.version}</h3>
                      <span className="text-sm text-foreground/60">{update.date}</span>
                    </div>
                    <ul className="mt-2 space-y-1 text-sm">
                      {update.changes.map((change, j) => (
                        <li key={j} className="flex items-start">
                          <span className="text-primary mr-2">•</span>
                          {change}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Download Card */}
            <Card className="p-6">
              <h3 className="font-medium mb-4">Download</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-foreground/70">Version</span>
                  <span className="font-medium">{script.version}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-foreground/70">File Size</span>
                  <span className="font-medium">{script.fileSize}</span>
                </div>
                <Button className="w-full mt-4 gap-2">
                  <FiDownload className="h-4 w-4" />
                  Download Now
                </Button>
              </div>
            </Card>

            {/* Requirements */}
            <Card className="p-6">
              <h3 className="font-medium mb-4">System Requirements</h3>
              <ul className="space-y-2">
                {script.requirements.map((req, i) => (
                  <li key={i} className="flex items-start">
                    <span className="text-primary mr-2">•</span>
                    {req}
                  </li>
                ))}
              </ul>
            </Card>

            {/* Compatibility */}
            <Card className="p-6">
              <h3 className="font-medium mb-4">Executor Compatibility</h3>
              <div className="space-y-3">
                {script.compatibility.map((exec, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <span>{exec.name}</span>
                    <Badge variant={exec.status === 'fully' ? 'success' : 'warning'}>
                      {exec.status === 'fully' ? 'Fully Supported' : 'Limited Support'}
                    </Badge>
                  </div>
                ))}
              </div>
            </Card>

            {/* Report Issue */}
            <Button variant="outline" className="w-full">
              <FiAlertTriangle className="h-4 w-4 mr-2" />
              Report an Issue
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScriptDetail;
