import { useState } from 'react';
import { PageLayout, Section } from '../components/pages/PageLayout';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';

const FAQ = () => {
  const faqCategories = [
    {
      id: 'general',
      title: 'General Questions',
      items: [
        {
          question: 'What is Project Madara?',
          answer: 'Project Madara is a premium platform for Roblox enthusiasts, offering high-performance scripts and utilities to enhance your Roblox gaming experience. We focus on providing reliable and powerful tools for the Roblox community.'
        },
        {
          question: 'Is Project Madara free to use?',
          answer: 'Project Madara offers both free and premium tiers. While basic features are available at no cost, our premium subscription unlocks advanced Roblox scripts, priority updates, and exclusive features for serious gamers and developers.'
        },
        {
          question: 'How do I get started with Roblox scripts?',
          answer: 'Getting started is easy! First, ensure you have a Roblox account and a compatible script executor. Then, browse our collection of Roblox scripts, read the documentation for each script, and follow the provided instructions for implementation.'
        }
      ]
    },
    {
      id: 'scripts',
      title: 'Scripts & Usage',
      items: [
        {
          question: 'How do I use these scripts?',
          answer: 'Each script comes with its own instructions. Generally, you\'ll need a script executor compatible with Roblox. Copy the script and run it using your preferred executor.'
        },
        {
          question: 'Are these scripts safe?',
          answer: 'We verify all scripts before publishing, but we recommend using them at your own risk. Always scan scripts and only download from trusted sources.'
        },
        {
          question: 'Why isn\'t my script working?',
          answer: 'Make sure you\'re using a compatible executor, the script is up to date, and you\'ve followed all instructions. If issues persist, check our troubleshooting guide or contact support.'
        }
      ]
    },
  ];

  const [openItems, setOpenItems] = useState({});

  const toggleItem = (categoryId, itemIndex) => {
    setOpenItems(prev => ({
      ...prev,
      [`${categoryId}-${itemIndex}`]: !prev[`${categoryId}-${itemIndex}`]
    }));
  };

  return (
    <PageLayout 
      title="Frequently Asked Questions"
      description="Find answers to common questions about Project Madara and our Roblox scripts"
    >
      <div className="space-y-8">
        {faqCategories.map((category) => (
          <Section key={category.id} title={category.title}>
            <div className="space-y-4">
              {category.items.map((item, index) => (
                <div 
                  key={index}
                  className="border border-border rounded-lg overflow-hidden"
                >
                  <button
                    onClick={() => toggleItem(category.id, index)}
                    className="w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
                    aria-expanded={openItems[`${category.id}-${index}`]}
                    aria-controls={`faq-${category.id}-${index}`}
                  >
                    <span className="font-medium">{item.question}</span>
                    {openItems[`${category.id}-${index}`] ? (
                      <FiChevronUp className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <FiChevronDown className="h-5 w-5 text-muted-foreground" />
                    )}
                  </button>
                  <div 
                    id={`faq-${category.id}-${index}`}
                    className={`px-4 pb-4 pt-0 overflow-hidden transition-all duration-300 ${
                      openItems[`${category.id}-${index}`] ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                    }`}
                  >
                    <div className="text-muted-foreground">
                      {item.answer}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Section>
        ))}

        <Section title="Still have questions?">
          <div className="text-center py-6">
            <p className="mb-6 text-muted-foreground">
              Can't find what you're looking for? Our support team is here to help.
            </p>
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors"
            >
              Contact Support
            </a>
          </div>
        </Section>
      </div>
    </PageLayout>
  );
};

export default FAQ;
