import { useState, useEffect } from 'react';
import { useSearchPara<PERSON>, Link } from 'react-router-dom';

import { PageLayout, Section } from '../components/pages/PageLayout';
import { Button } from '../components/ui/Button';
import { FiAlertCircle, FiCheckCircle, FiClock, FiXCircle, FiSearch } from 'react-icons/fi';

const statusConfig = {
  pending: {
    icon: <FiClock className="h-5 w-5 text-yellow-500" />,
    color: 'bg-yellow-500/10 text-yellow-600 dark:text-yellow-400 border-yellow-500/20',
    text: 'Pending Review',
    description: 'Your request has been received and is awaiting review by our team.'
  },
  in_progress: {
    icon: <div className="h-5 w-5 flex items-center justify-center"><div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div></div>,
    color: 'bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/20',
    text: 'In Progress',
    description: 'Our team is currently working on your script request.'
  },
  approved: {
    icon: <FiCheckCircle className="h-5 w-5 text-green-500" />,
    color: 'bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/20',
    text: 'Approved',
    description: 'Your request has been approved and is in the development queue.'
  },
  completed: {
    icon: <FiCheckCircle className="h-5 w-5 text-green-500" />,
    color: 'bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/20',
    text: 'Completed',
    description: 'Your script request has been completed and is now available.'
  },
  rejected: {
    icon: <FiXCircle className="h-5 w-5 text-red-500" />,
    color: 'bg-red-500/10 text-red-600 dark:text-red-400 border-red-500/20',
    text: 'Rejected',
    description: 'Your request could not be fulfilled at this time.'
  }
};

const RequestStatus = () => {
  const [searchParams] = useSearchParams();
  const [requestId, setRequestId] = useState('');
  const [request, setRequest] = useState(null);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleCheckStatus = async (id) => {
    const checkId = id || requestId.trim();
    if (!checkId) {
      setError('Please enter a request ID');
      return;
    }
    
    setIsLoading(true);
    setError('');
    setRequest(null);

    try {
      const response = await fetch(`/.netlify/functions/get-script-request-by-id?id=${checkId}`);
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Request not found.');
      }
      const data = await response.json();
      setRequest(data);
    } catch (err) {
      console.error('Error checking status:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Check for ID in URL params on component mount
  useEffect(() => {
    const idFromUrl = searchParams.get('id');
    if (idFromUrl) {
      setRequestId(idFromUrl);
      handleCheckStatus(idFromUrl);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);
  
  const handleSubmit = (e) => {
    e.preventDefault();
    handleCheckStatus();
  };
  
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  const getStatusConfig = (status) => {
    return statusConfig[status] || statusConfig.pending;
  };
  
  return (
    <PageLayout 
      title="Request Status" 
      description="Check the status of your script request"
    >
      <div className="max-w-3xl mx-auto">
        <Section>
          <h2 className="text-2xl font-bold mb-6">Check Your Request</h2>
          
          <div className="bg-card p-6 rounded-lg border border-border shadow-sm">
            <p className="text-muted-foreground mb-6">
              Enter the ID you received when you submitted your request.
            </p>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="requestId" className="block text-sm font-medium mb-2">
                  Request ID
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    id="requestId"
                    value={requestId}
                    onChange={(e) => setRequestId(e.target.value)}
                    placeholder="e.g., 48a20f19-6c48-473f-92f1-d127dc03348d"
                    className="flex-1 px-4 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-background"
                    disabled={isLoading}
                  />
                  <Button 
                    type="submit" 
                    disabled={isLoading || !requestId.trim()}
                    className="flex items-center gap-2"
                  >
                    {isLoading ? 'Checking...' : (
                      <>
                        <FiSearch className="h-4 w-4" />
                        <span>Check Status</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {error && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-200 rounded-lg flex items-start">
                  <FiAlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              )}
            </form>
            
            {!request && !error && !isLoading && (
              <div className="mt-8 text-center py-8 border-t border-border">
                <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                  <FiSearch className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">No request selected</h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  Enter a request ID above to check the status of your submission.
                </p>
                <div className="mt-6">
                  <Button asChild variant="outline">
                    <Link to="/request-script">Submit a New Request</Link>
                  </Button>
                </div>
              </div>
            )}
            
            {isLoading && !request && (
              <div className="mt-8 py-12 flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </div>
            )}
            
            {request && (
              <div className="mt-8 border-t border-border pt-6">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                  <div>
                    <h3 className="text-lg font-medium">Request Details</h3>
                    <p className="text-sm text-muted-foreground">
                      ID: {request.id}
                    </p>
                  </div>
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusConfig(request.status).color}`}>
                    {getStatusConfig(request.status).icon}
                    <span className="ml-2">{getStatusConfig(request.status).text}</span>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-muted-foreground mb-1">Script Name</h4>
                      <p className="font-medium">{request.scriptName}</p>
                    </div>
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-muted-foreground mb-1">Submitted</h4>
                      <p className="font-medium">{formatDate(request.createdAt)}</p>
                    </div>
                  </div>
                  
                  <div className="bg-muted/30 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-muted-foreground mb-1">Description</h4>
                    <p className="whitespace-pre-line break-words overflow-hidden">{request.notes}</p>
                  </div>
                  
                  {request.discordUsername && (
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-muted-foreground mb-1">Contact</h4>
                      <p>{request.discordUsername}</p>
                    </div>
                  )}
                  
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-500">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Status Information</h4>
                    <p className="text-blue-700 dark:text-blue-300 text-sm">
                      {getStatusConfig(request.status).description}
                    </p>
                    
                    {request.status === 'rejected' && request.adminNotes && (
                      <div className="mt-3 p-3 bg-white dark:bg-blue-900/30 rounded border border-blue-200 dark:border-blue-800">
                        <h5 className="font-medium text-sm mb-1">Notes from our team:</h5>
                        <p className="text-sm break-words overflow-hidden">{request.adminNotes}</p>
                      </div>
                    )}
                    
                    {request.status === 'completed' && request.downloadLink && (
                      <div className="mt-3">
                        <a 
                          href={request.downloadLink} 
                          className="inline-flex items-center text-blue-700 dark:text-blue-300 hover:underline font-medium"
                        >
                          Download Script <span className="ml-1">→</span>
                        </a>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-3 pt-2">
                    <Button asChild>
                      <Link to="/scripts">Browse Scripts</Link>
                    </Button>
                    <Button asChild variant="outline">
                      <Link to="/request-script">Submit Another Request</Link>
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Section>
        
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>Need help? <a href="/contact" className="text-primary hover:underline">Contact our support team</a></p>
        </div>
      </div>
    </PageLayout>
  );
};

export default RequestStatus;
