import { Outlet, useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import { SecurityProvider } from '../../context/SecurityContext';
import { useEffect, useState } from 'react';
import { FaExclamationTriangle, FaSync } from 'react-icons/fa';

// Error Boundary Component
const ErrorFallback = ({ error, resetErrorBoundary }) => {
  const navigate = useNavigate();
  
  const handleReset = () => {
    resetErrorBoundary();
    navigate('/keysystem');
  };
  
  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
          <FaExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Something went wrong
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-6">
          {error.message || 'An unexpected error occurred. Please try again.'}
        </p>
        <div className="space-x-3">
          <button
            onClick={handleReset}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FaSync className="mr-2 -ml-1" />
            Start Over
          </button>
        </div>
      </div>
    </div>
  );
};

// Main Layout Component
function KeySystemContent() {
  const { resolvedTheme } = useTheme();
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState(null);

  // Apply theme
  useEffect(() => {
    const bodyClasses = ['font-sans', 'bg-gray-50', 'dark:bg-gray-900'];
    bodyClasses.forEach(cls => document.body.classList.add(cls));
    
    if (resolvedTheme) {
      document.documentElement.classList.add(resolvedTheme);
      document.documentElement.setAttribute('data-theme', resolvedTheme);
    }
    
    return () => {
      bodyClasses.forEach(cls => document.body.classList.remove(cls));
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.removeAttribute('data-theme');
    };
  }, [resolvedTheme]);

  // Error boundary effect
  const errorHandler = (error, errorInfo) => {
    console.error('Key System Error:', error, errorInfo);
    setError(error);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md">
          <ErrorFallback 
            error={error || new Error('An unknown error occurred')} 
            resetErrorBoundary={() => window.location.href = '/keysystem'}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <div className="w-full max-w-md">
        <Outlet />
      </div>
    </div>
  );
}

// Main Layout Wrapper with Error Boundary
export default function KeySystemLayout() {
  return (
    <SecurityProvider>
      <KeySystemContent />
    </SecurityProvider>
  );
}
