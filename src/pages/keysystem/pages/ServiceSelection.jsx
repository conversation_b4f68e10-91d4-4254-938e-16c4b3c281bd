import { useNavigate, useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useSecurity } from '../../../context/SecurityContext';
import { FaArrowLeft, FaInfoCircle, FaLock, FaShieldAlt, FaLink, FaExclamationTriangle } from 'react-icons/fa';

const serviceOptions = [
  {
    id: 'linkvertise',
    name: 'Linkvertise',
    description: 'Monetize your links with our premium link shortener',
    color: 'blue',
    icon: <FaLink className="w-5 h-5" />
  },
  {
    id: 'shrinkme',
    name: 'ShrinkMe',
    description: 'Simple and fast link shortening service',
    color: 'purple',
    icon: <FaLink className="w-5 h-5" />
  },
];

export default function ServiceSelection() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { securityService } = useSecurity();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedService, setSelectedService] = useState(null);
  
  // Check for redirect from another step
  useEffect(() => {
    const serviceParam = searchParams.get('service');
    if (serviceParam && serviceOptions.some(s => s.id === serviceParam)) {
      setSelectedService(serviceParam);
    }
  }, [searchParams]);

  const selectService = async (serviceId) => {
    if (isLoading) return;
    
    try {
      setIsLoading(true);
      setError('');
      

      
      // Store the selected service
      localStorage.setItem('selected_service', serviceId);
      
      // Navigate to the first step
      navigate(`step1?service=${encodeURIComponent(serviceId)}`);
    } catch (err) {
      console.error('Error selecting service:', err);
      setError('Failed to select service. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8 text-center border border-gray-100 dark:border-gray-700">
        <div className="mb-6">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 dark:bg-indigo-900/30 mb-3">
            <FaShieldAlt className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">Project L | Key System</h1>
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300">Choose Your Access Level</h2>
        </div>
        
        <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
          Select your preferred service to generate a secure access key. Each service offers different features and benefits.
        </p>
        
        {error && (
          <div className="mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-700 dark:text-red-300 flex items-center justify-center">
              <FaExclamationTriangle className="mr-2 flex-shrink-0" />
              {error}
            </p>
          </div>
        )}
        
        <div className="space-y-4 mb-8">
          {serviceOptions.map((service) => {
            const colorMap = {
              blue: 'bg-blue-600 hover:bg-blue-700',
              purple: 'bg-purple-600 hover:bg-purple-700',
              gray: 'bg-gray-600 hover:bg-gray-700'
            };
            
            return (
              <button
                key={service.id}
                onClick={() => selectService(service.id)}
                disabled={isLoading}
                className={`w-full flex items-center justify-between ${
                  colorMap[service.color] || 'bg-indigo-600 hover:bg-indigo-700'
                } text-white font-medium py-4 px-6 rounded-lg transition-all hover:shadow-lg transform hover:-translate-y-0.5 disabled:opacity-70 disabled:cursor-not-allowed`}
              >
                <div className="flex items-center">
                  <span className="mr-3">{service.icon}</span>
                  <div className="text-left">
                    <div className="font-semibold">{service.name}</div>
                    <div className="text-xs opacity-80 font-normal">{service.description}</div>
                  </div>
                </div>
                <span className="text-sm opacity-80">→</span>
              </button>
            );
          })}
        </div>
        
        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
          <div className="flex items-center justify-center">
            <FaInfoCircle className="mr-1.5 flex-shrink-0" />
            <span>Your selection will determine the key generation process</span>
          </div>
          <div className="text-xs">
            &copy; {new Date().getFullYear()} Project L. All Rights Reserved.
          </div>
        </div>
      </div>
    </div>
  );
}
