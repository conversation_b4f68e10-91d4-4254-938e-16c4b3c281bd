import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { FaShieldAlt, FaArrowLeft, FaLock, FaExclamationTriangle, FaClock, FaCheckCircle } from 'react-icons/fa';
import { useSecurity } from '../../../context/SecurityContext';

export default function Step2() {
  // Prevent bypass: require step1_complete
  if (typeof window !== 'undefined' && localStorage.getItem('step1_complete') !== 'true') {
    window.location.href = '/keysystem/step1';
    return null;
  }
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { securityService } = useSecurity();
  
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeSpent, setTimeSpent] = useState(0);
  const [progress, setProgress] = useState(0);
  const [visitStartTime, setVisitStartTime] = useState(0);
  
  const serviceType = searchParams.get('service') || 'standard';

  // Track time spent on the page and load visit start time
  useEffect(() => {
    // Get the visit start time from localStorage or use current time
    const startTime = parseInt(localStorage.getItem('link_visit_start') || Date.now().toString(), 10);
    setVisitStartTime(startTime);
    
    const timer = setInterval(() => {
      const seconds = Math.floor((Date.now() - startTime) / 1000);
      setTimeSpent(seconds);
      
      // Update progress (max 90% until verification)
      const minTime = 30; // Minimum time in seconds before verification is allowed
      const maxTime = 300; // Time in seconds to reach 90% progress
      
      if (seconds < minTime) {
        setProgress(Math.floor((seconds / minTime) * 30)); // First 30% for minimum time
      } else if (seconds < maxTime) {
        setProgress(30 + Math.floor(((seconds - minTime) / (maxTime - minTime)) * 60));
      } else if (!isVerified) {
        setProgress(90); // Cap at 90% until verification
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [isVerified]);

  useEffect(() => {
    // Initialize the security service and load reCAPTCHA
    const initializeSecurity = async () => {
      try {
        setIsLoading(true);
        await securityService.initializeRecaptcha('final_verification');
        
        // Check if we have a valid CAPTCHA token from the previous step
        const existingToken = securityService.getCurrentCaptchaToken();
        if (existingToken) {
          setIsVerified(true);
          setProgress(100);
        }
      } catch (err) {
        console.error('Failed to initialize security service:', err);
        setError('Failed to load security verification. Please refresh the page.');
      } finally {
        setIsLoading(false);
      }
    };

    initializeSecurity();
    
    // Clean up on unmount
    return () => {
      securityService.cleanupRecaptcha();
    };
  }, [securityService]);

  const handleCaptchaVerify = async (token) => {
    if (!token) {
      setError('Please complete the verification');
      setIsVerified(false);
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      // Verify the CAPTCHA token with the server
      const isValid = await securityService.verifyCaptcha(token, 'final_verification');
      
      if (isValid) {
        setIsVerified(true);
        setProgress(100);
        // Store the token for the next step
        securityService.setCaptchaToken(token);
        // Log the verification event
        await securityService.logEvent('final_verification_completed', { 
          service: serviceType,
          timeSpent: Math.floor((Date.now() - visitStartTime) / 1000)
        });
      } else {
        setError('Verification failed. Please try again.');
        setIsVerified(false);
      }
    } catch (err) {
      console.error('Error verifying CAPTCHA:', err);
      setError('An error occurred during verification. Please try again.');
      setIsVerified(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!isVerified) {
      setError('Please complete the verification to continue');
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      // Get the CAPTCHA token
      const captchaToken = securityService.getCurrentCaptchaToken();
      
      if (!captchaToken) {
        throw new Error('Missing verification. Please complete the CAPTCHA again.');
      }
      
      // Log the step completion
      await securityService.logEvent('step2_completed', { 
        service: serviceType,
        totalTimeSpent: Math.floor((Date.now() - visitStartTime) / 1000)
      });
      
      // Store verification completion
      localStorage.setItem('verification_complete', 'true');
      
      // Navigate to the complete page with the service type
      navigate(`complete?service=${encodeURIComponent(serviceType)}`);
    } catch (err) {
      console.error('Error completing step 2:', err);
      setError(err.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">Project Madara | Key System</h1>
          <h2 className="text-xl font-semibold text-indigo-600 dark:text-indigo-400">Security Verification</h2>
          <p className="text-gray-600 dark:text-gray-300 mt-2">Complete the verification to continue</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-start space-x-4 mb-6">
            <div className="flex-shrink-0 mt-1">
              <div className="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center">
                <FaLock className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              </div>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 dark:text-white">Complete Verification</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                This helps us ensure you're not a robot.
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-6">
            <div 
              className={`h-2.5 rounded-full transition-all duration-300 ${
                progress < 30 
                  ? 'bg-red-500' 
                  : progress < 70 
                    ? 'bg-yellow-500' 
                    : 'bg-green-500'
              }`} 
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          
          {progress >= 90 && !isVerified && (
            <div className="mb-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg text-sm text-yellow-700 dark:text-yellow-300">
              <div className="flex items-center">
                <FaExclamationTriangle className="mr-2 flex-shrink-0" />
                <span>Verification required to continue</span>
              </div>
            </div>
          )}
          
          {/* reCAPTCHA */}
          <div className="mb-6">
            <div 
              id="recaptcha-container"
              className="flex justify-center"
              data-callback={handleCaptchaVerify}
              data-expired-callback={() => {
                setIsVerified(false);
                setProgress(90);
                setError('Verification expired. Please verify again.');
                securityService.clearCaptchaToken();
              }}
              data-error-callback={() => {
                setIsVerified(false);
                setProgress(90);
                setError('Verification error. Please try again.');
                securityService.clearCaptchaToken();
              }}
            />
            {error && (
              <p className="mt-2 text-sm text-red-500 dark:text-red-400 flex items-center">
                <FaExclamationTriangle className="mr-1" /> {error}
              </p>
            )}
          </div>
          
          {/* Timer */}
          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-6">
            <div className="flex items-center">
              <FaClock className="mr-1.5" />
              <span>Time spent: {Math.floor(timeSpent / 60)}m {timeSpent % 60}s</span>
            </div>
            {isVerified && (
              <div className="flex items-center text-green-500 dark:text-green-400">
                <FaCheckCircle className="mr-1.5" />
                <span>Verified</span>
              </div>
            )}
          </div>
          
          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleSubmit}
              disabled={!isVerified || isLoading}
              className={`w-full px-4 py-2 rounded font-medium text-white transition-colors ${
                !isVerified || isLoading
                  ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
                  : 'bg-indigo-600 hover:bg-indigo-700'
              }`}
            >
              {isLoading ? 'Verifying...' : 'Complete Verification'}
            </button>
            
            <button
              type="button"
              onClick={() => {
                localStorage.removeItem('step1_complete');
                localStorage.removeItem('verification_complete');
                navigate(-1);
              }}
              className="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded font-medium transition-colors"
              disabled={isLoading}
            >
              Go Back
            </button>
          </div>
          
          {/* Security Info */}
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-100 dark:border-blue-800/50">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 flex items-center">
              <FaShieldAlt className="mr-2" /> Why is this required?
            </h3>
            <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
              This security check helps prevent automated requests and ensures fair access to our services.
            </p>
          </div>
        </div>
        
        <p className="text-center text-sm text-gray-500 dark:text-gray-400">
          &copy; 2024-{new Date().getFullYear()} Project Madara. All Rights Reserved.
        </p>
      </div>
    </div>
  );
}
