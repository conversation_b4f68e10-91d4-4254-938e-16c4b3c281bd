
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { FaCopy, FaCheck, FaCheckCircle, FaDownload, FaHome, FaExclamationTriangle } from 'react-icons/fa';
import keyService from '../../../services/keyService';
import { useSecurity } from '../../../context/SecurityContext';
import { toast } from 'react-hot-toast';

const Complete = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { isSecure, fingerprint } = useSecurity();

  const [keyDetails, setKeyDetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    // Prevent bypass: require verification_complete and step1_complete
    if (typeof window !== 'undefined') {
      if (localStorage.getItem('verification_complete') !== 'true' || localStorage.getItem('step1_complete') !== 'true') {
        window.location.href = '/keysystem/step1';
        return;
      }
    }
    const keyId = searchParams.get('keyId');
    if (!keyId || !isSecure) {
      setError('Invalid request or security check failed.');
      setLoading(false);
      return;
    }

    const fetchKeyDetails = async () => {
      try {
        // Fetch the key securely from the backend using keyId and fingerprint
        const response = await keyService.getKeyById(keyId, fingerprint);
        if (!response || !response.key) {
          setError('Key not found or session expired.');
          setLoading(false);
          return;
        }
        setKeyDetails({ key: response.key, expires_at: response.expires_at });
      } catch (err) {
        setError('Failed to retrieve key details.');
      } finally {
        setLoading(false);
      }
    };

    fetchKeyDetails();
  }, [searchParams, isSecure, fingerprint]);

  const handleCopy = () => {
    if (!keyDetails || !keyDetails.key) return;
    navigator.clipboard.writeText(keyDetails.key);
    setCopied(true);
    toast.success('Key copied to clipboard!');
    logEvent('key_copied');
    setTimeout(() => setCopied(false), 2000);
  };

  const handleDownload = () => {
    if (!keyDetails || !keyDetails.key) return;
    toast.success('Key downloaded! You will not be able to view it again.');
    logEvent('key_downloaded');
    // Clear step flags after download (one-time view)
    localStorage.removeItem('step1_complete');
    localStorage.removeItem('verification_complete');
    const element = document.createElement('a');
    const file = new Blob([keyDetails.key], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = 'generated_key.txt';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  if (loading) {
    return <div className="text-center p-8">Loading...</div>;
  }

  if (error) {
    return (
      <div className="text-center p-8 bg-red-100 border border-red-400 text-red-700 rounded-lg">
        <FaExclamationTriangle className="mx-auto text-4xl mb-4" />
        <h2 className="text-2xl font-bold mb-2">Error</h2>
        <p>{error}</p>
        <button onClick={() => {
            localStorage.removeItem('step1_complete');
            localStorage.removeItem('verification_complete');
            navigate('/');
          }}
  className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
          <FaHome className="inline-block mr-2" /> Go Home
        </button>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 rounded-lg shadow-md">
      <div className="text-center">
        <FaCheckCircle className="mx-auto text-5xl text-green-500 mb-4" />
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Key Generation Complete!</h1>
        <p className="text-gray-600 mb-6">Your key has been successfully generated. <b>This key will expire in 24 hours.</b> Please copy and use it in Roblox. <span className='text-red-500 font-semibold'>You will not be able to view this key again!</span></p>
      </div>

      <div className="relative p-4 bg-gray-900 text-white font-mono rounded-md mb-4">
        <span className="break-all text-lg">{keyDetails?.key}</span>
        <div className="mt-2 text-xs text-gray-300">Expires at: {keyDetails?.expires_at ? new Date(keyDetails.expires_at).toLocaleString() : 'Unknown'}</div>
        <button onClick={handleCopy} disabled={!keyDetails || !keyDetails.key} className={`absolute top-2 right-2 p-2 bg-gray-700 rounded hover:bg-gray-600 `}>
          {copied ? <FaCheck className="text-green-400" /> : <FaCopy />}
        </button>
      </div>

      <div className="flex justify-center space-x-4">
        <button onClick={handleDownload} disabled={!keyDetails || !keyDetails.key} className={`px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center `}>
          <FaDownload className="mr-2" /> Download Key
        </button>
        <button onClick={() => {
            localStorage.removeItem('step1_complete');
            localStorage.removeItem('verification_complete');
            navigate('/');
          }}
  className="px-6 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 flex items-center">
          <FaHome className="mr-2" /> Go Home
        </button>
      </div>
    </div>
  );
};

export default Complete;
