import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { FaShieldAlt, FaArrowLeft, FaRobot, FaExclamationTriangle } from 'react-icons/fa';
import { useSecurity } from '../../../context/SecurityContext';

export default function Step1() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { securityService } = useSecurity();
  
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const serviceType = searchParams.get('service') || 'standard';
  const serviceNames = {
    linkvertise: 'Linkvertise',
    shrinkme: 'ShrinkMe',
    standard: 'Standard'
  };

  useEffect(() => {
    // Initialize the security service and load reCAPTCHA
    const initializeSecurity = async () => {
      try {
        setIsLoading(true);
        await securityService.initializeRecaptcha('verify_human');
      } catch (err) {
        console.error('Failed to initialize security service:', err);
        setError('Failed to load security verification. Please refresh the page.');
      } finally {
        setIsLoading(false);
      }
    };

    initializeSecurity();
    
    // Clean up on unmount
    return () => {
      securityService.cleanupRecaptcha();
    };
  }, [securityService]);

  const handleCaptchaVerify = async (token) => {
    if (!token) {
      setError('Please complete the verification');
      setIsVerified(false);
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      // Verify the CAPTCHA token with the server
      const isValid = await securityService.verifyCaptcha(token, 'verify_human');
      
      if (isValid) {
        setIsVerified(true);
        // Store the token for the next step
        securityService.setCaptchaToken(token);
        // Log the verification event
        await securityService.logEvent('captcha_verified', { step: 'step1' });
      } else {
        setError('Verification failed. Please try again.');
        setIsVerified(false);
      }
    } catch (err) {
      console.error('Error verifying CAPTCHA:', err);
      setError('An error occurred during verification. Please try again.');
      setIsVerified(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!isVerified) {
      setError('Please complete the verification to continue');
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      // Log the step completion
      await securityService.logEvent('step1_completed', { service: serviceType });
      
      // Store the visit start time for the next step
      localStorage.setItem('link_visit_start', Date.now().toString());
      
      // Navigate to the next step
      navigate('step2');
      // Set a flag to prevent bypass
      localStorage.setItem('step1_complete', 'true');
    } catch (err) {
      console.error('Error completing step 1:', err);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">Project Madara | Key System</h1>
          <h2 className="text-xl font-semibold text-indigo-600 dark:text-indigo-400">Human Verification</h2>
          <p className="text-gray-600 dark:text-gray-300 mt-2">Complete the security check to continue</p>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          {/* Service Badge */}
          <div className="inline-flex items-center px-4 py-1.5 bg-indigo-100 dark:bg-indigo-900/30 rounded-full text-sm font-medium text-indigo-700 dark:text-indigo-300 mb-6">
            {serviceNames[serviceType] || 'Standard'} Access
          </div>
          
          <div className="flex items-start space-x-4 mb-6">
            <div className="flex-shrink-0 mt-1">
              <div className="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center">
                <FaRobot className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              </div>
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 dark:text-white">Security Verification</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Complete the verification to prove you're not a robot
              </p>
            </div>
          </div>
          
          {/* reCAPTCHA */}
          <div className="mb-6">
            <div 
              id="recaptcha-container"
              className="flex justify-center"
              data-callback={handleCaptchaVerify}
              data-expired-callback={() => {
                setIsVerified(false);
                setError('Verification expired. Please verify again.');
              }}
              data-error-callback={() => {
                setIsVerified(false);
                setError('Verification error. Please try again.');
              }}
            ></div>
            
            {error && (
              <p className="mt-2 text-sm text-red-500 dark:text-red-400 text-center">{error}</p>
            )}
            
            {isVerified && (
              <p className="mt-2 text-sm text-green-500 dark:text-green-400 text-center">
                Verification complete! You can now continue.
              </p>
            )}
          </div>
          
          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleSubmit}
              disabled={!isVerified || isLoading}
              className={`w-full px-4 py-2 rounded font-medium text-white transition-colors ${
                !isVerified || isLoading
                  ? 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
                  : 'bg-indigo-600 hover:bg-indigo-700'
              }`}
            >
              {isLoading ? 'Verifying...' : 'Continue to Next Step'}
            </button>
            
            <button
              type="button"
              onClick={() => {
                localStorage.removeItem('step1_complete');
                localStorage.removeItem('verification_complete');
                navigate(-1);
              }}
              className="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded font-medium transition-colors"
              disabled={isLoading}
            >
              Go Back
            </button>
          </div>
          
          {/* Help Text */}
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs text-center text-gray-500 dark:text-gray-400">
              Having issues? Try refreshing the page or contact support
            </p>
          </div>
        </div>
        
        <p className="text-center text-sm text-gray-500 dark:text-gray-400">
          &copy; 2024-{new Date().getFullYear()} Project Madara. All Rights Reserved.
        </p>
      </div>
    </div>
  );
}
