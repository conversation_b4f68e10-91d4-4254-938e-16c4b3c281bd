import { Routes, Route, Navigate } from 'react-router-dom';
import KeySystemLayout from './KeySystemLayout';
import ServiceSelection from './pages/ServiceSelection';
import Step1 from './pages/Step1';
import Step2 from './pages/Step2';
import Complete from './pages/Complete';

export default function KeySystem() {
  return (
    <Routes>
      <Route element={<KeySystemLayout />}>
        <Route index element={<ServiceSelection />} />
        <Route path="step1" element={<Step1 />} />
        <Route path="step2" element={<Step2 />} />
        <Route path="complete" element={<Complete />} />
        <Route path="*" element={<Navigate to="/keysystem" replace />} />
      </Route>
    </Routes>
  );
}
