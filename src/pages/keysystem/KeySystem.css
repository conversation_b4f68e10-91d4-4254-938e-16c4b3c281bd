/* KeySystem - Modern Design */
.keysystem {
  --primary: #6366f1;
  --primary-hover: #4f46e5;
  --background: #f8fafc;
  --card-bg: #ffffff;
  --text: #0f172a;
  --text-secondary: #475569;
  --border: #e2e8f0;
  --success: #10b981;
  --error: #ef4444;
  
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  background: var(--background);
  min-height: 100vh;
  color: var(--text);
  transition: all 0.3s ease;
}

/* Dark mode variables */
.dark .keysystem {
  --primary: #818cf8;
  --primary-hover: #6366f1;
  --background: #0f172a;
  --card-bg: #1e293b;
  --text: #f8fafc;
  --text-secondary: #94a3b8;
  --border: #334155;
}

/* Card styles */
.keysystem .card {
  background: var(--card-bg);
  border-radius: 1rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.3s ease;
  max-width: 100%;
  width: 100%;
}

/* Header styles */
.keysystem .header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border);
  text-align: center;
}

.keysystem .header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text);
  margin: 0;
  line-height: 1.2;
}

.keysystem .header p {
  color: var(--text-secondary);
  margin: 0.5rem 0 0;
  font-size: 0.95rem;
}

/* Content styles */
.keysystem .content {
  padding: 2rem;
}

/* Key display styles */
.keysystem .key-container {
  margin: 1.5rem 0;
  position: relative;
}

.keysystem .key-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.keysystem .key-box {
  background: var(--background);
  padding: 1rem 1.25rem;
  border-radius: 0.75rem;
  font-family: 'Fira Code', 'JetBrains Mono', 'Cascadia Code', monospace;
  word-break: break-all;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text);
  border: 2px dashed var(--border);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.keysystem .key-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.keysystem .key-box:hover::before {
  transform: translateX(100%);
}

/* Button styles */
.keysystem .button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  background: var(--primary);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.keysystem .button:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  background: var(--primary-hover);
}

.keysystem .button:active {
  transform: translateY(0);
}

.keysystem .button.outline {
  background: transparent;
  border: 2px solid var(--border);
  color: var(--text);
}

.keysystem .button.outline:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Service selection */
.keysystem .service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.keysystem .service-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  border-radius: 0.75rem;
  background: var(--card-bg);
  border: 2px solid var(--border);
  transition: all 0.2s ease;
  cursor: pointer;
  text-align: center;
  color: var(--text);
}

.keysystem .service-button:hover {
  transform: translateY(-2px);
  border-color: var(--primary);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.keysystem .service-button i {
  font-size: 1.75rem;
  margin-bottom: 0.75rem;
  color: var(--primary);
}

/* Divider */
.keysystem .divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.keysystem .divider::before,
.keysystem .divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--border);
  margin: 0 1rem;
}

/* Footer */
.keysystem .footer {
  text-align: center;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--border);
  color: var(--text-secondary);
  font-size: 0.825rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .keysystem .service-grid {
    grid-template-columns: 1fr;
  }
  
  .keysystem .button {
    width: 100%;
  }
  
  .keysystem .header {
    padding: 1.25rem 1.5rem;
  }
  
  .keysystem .content {
    padding: 1.5rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.keysystem .fade-in {
  animation: fadeIn 0.4s ease-out forwards;
}

/* Loading state */
.keysystem .loading {
  position: relative;
  pointer-events: none;
  color: transparent !important;
}

.keysystem .loading::after {
  content: '';
  position: absolute;
  width: 1.25rem;
  height: 1.25rem;
  top: 50%;
  left: 50%;
  margin: -0.625rem 0 0 -0.625rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
