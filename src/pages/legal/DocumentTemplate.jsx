import { PageLayout, Section } from '../../components/pages/PageLayout';
import { FiChevronRight } from 'react-icons/fi';
import { Link } from 'react-router-dom';

const DocumentTemplate = ({ 
  title, 
  lastUpdated, 
  children,
  tableOfContents = []
}) => {
  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-2">{title}</h1>
        <p className="text-muted-foreground">Last updated: {lastUpdated}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Table of Contents */}
        {tableOfContents.length > 0 && (
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <div className="p-4 bg-muted/30 rounded-lg">
                <h2 className="font-semibold mb-3 text-lg">Table of Contents</h2>
                <nav>
                  <ul className="space-y-2">
                    {tableOfContents.map((item, index) => (
                      <li key={index}>
                        <a 
                          href={`#${item.id}`}
                          className="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors"
                        >
                          <FiChevronRight className="h-4 w-4 mr-1" />
                          {item.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className={`${tableOfContents.length > 0 ? 'lg:col-span-3' : 'col-span-full'}`}>
          <div className="prose dark:prose-invert max-w-none">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentTemplate;
