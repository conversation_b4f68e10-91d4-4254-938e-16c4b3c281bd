import { motion } from 'framer-motion';

const About = () => {
  return (
    <div className="container mx-auto px-4 py-16">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-8 text-center">
          About <span className="text-blue-600">Me</span>
        </h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-12">
          <div className="prose dark:prose-invert max-w-none">
            <h2>Who am I?</h2>
            <p>
              I'm a passionate developer with expertise in modern web technologies. 
              I love creating beautiful, responsive, and user-friendly applications 
              that solve real-world problems.
            </p>
            
            <h3>My Skills</h3>
            <div className="flex flex-wrap gap-2 mb-6">
              {['React', 'JavaScript', 'TypeScript', 'Node.js', 'Tailwind CSS',
                'Git', 'RESTful APIs', 'GraphQL', 'Next.js'].map((skill, index) => (
                <span 
                  key={skill}
                  className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                >
                  {skill}
                </span>
              ))}
            </div>
            
            <h3>Experience</h3>
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold text-lg">Frontend Developer</h4>
                <p className="text-gray-600 dark:text-gray-400">Company Name • 2022 - Present</p>
                <p className="mt-1">
                  Building amazing web applications with React and modern JavaScript frameworks.
                </p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default About;
