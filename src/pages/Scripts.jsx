import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiSearch, 
  FiFilter, 
  FiDownload, 
  FiStar, 
  FiClock, 
  FiCopy, 
  FiCheck,
  FiCode,
  FiInfo,
  FiExternalLink,
  FiLoader,
  FiAlertCircle,
  FiFileText
} from 'react-icons/fi';
import { scriptsAPI } from '../lib/api';
import { Badge } from '../components/ui/Badge';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import UpdateLogModal from '../components/scripts/UpdateLogModal';
import ExecutorSupport from '../components/scripts/ExecutorSupport';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/Tabs';

// Categories for filtering
const CATEGORIES = [
  { id: 'all', name: 'All Scripts' },
  { id: 'game', name: 'Games' },
  { id: 'utility', name: 'Utilities' },
  { id: 'other', name: 'Other' },
];

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};
// Sample script data structure for reference
// Actual scripts will be loaded from the API
const SAMPLE_SCRIPT = {
  id: 'sample-id',
  name: 'Sample Script',
  description: 'This is a sample script',
  category: 'game',
  views: '1K+',
  rating: 4.5,
  updated_at: new Date().toISOString(),
  tags: JSON.stringify(['Feature 1', 'Feature 2']),
  executor: 'Synapse X',
  version: '1.0.0',
  content: '-- Sample script content'
};

const Scripts = () => {
  const [scripts, setScripts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedScript, setSelectedScript] = useState(null);
  const [showUpdateLog, setShowUpdateLog] = useState(false);
  const [copied, setCopied] = useState(false);
  const [copiedId, setCopiedId] = useState(null);
  const [selectedExecutor, setSelectedExecutor] = useState('all');
  const [showExecutorSupport, setShowExecutorSupport] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Fetch scripts from API
  useEffect(() => {
    const fetchScripts = async () => {
      try {
        setLoading(true);
        setError(null);
        
        console.log('Fetching scripts from API...');
        const data = await scriptsAPI.list();
        console.log('Fetched scripts:', data);
        
        // Ensure scripts is always an array
        setScripts(Array.isArray(data) ? data : []);
      } catch (err) {
        console.error('Error fetching scripts:', err);
        setError(err.message || 'Failed to load scripts');
      } finally {
        setLoading(false);
      }
    };

    fetchScripts();
  }, []);

  const filteredScripts = useMemo(() => {
    try {
      if (!scripts || !Array.isArray(scripts)) return [];

      return scripts.filter(script => {
        if (!script || typeof script !== 'object') return false;

        // Ensure script has required properties
        const name = typeof script.name === 'string' ? script.name : '';
        const description = typeof script.description === 'string' ? script.description : '';
        const category = typeof script.category === 'string' ? script.category : '';
        const executor = typeof script.executor === 'string' ? script.executor : '';

        const matchesSearch = searchQuery === '' || 
          name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          description.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesCategory = selectedCategory === 'all' || 
          category.toLowerCase() === selectedCategory.toLowerCase();

        const matchesExecutor = selectedExecutor === 'all' || 
          executor.toLowerCase().includes(selectedExecutor.toLowerCase());

        return matchesSearch && matchesCategory && matchesExecutor;
      });
    } catch (error) {
      console.error('Error filtering scripts:', error);
      return [];
    }
  }, [scripts, searchQuery, selectedCategory, selectedExecutor]);

  const copyToClipboard = useCallback((text, id) => {
    if (typeof text !== 'string') {
      console.error('Invalid text to copy');
      return;
    }

    if (!navigator.clipboard) {
      console.error('Clipboard API not available');
      return;
    }

    const copyText = text.trim();
    if (!copyText) {
      console.error('No text to copy');
      return;
    }

    navigator.clipboard.writeText(copyText)
      .then(() => {
        setCopied(true);
        setCopiedId(id);
        const timer = setTimeout(() => {
          setCopied(false);
          setCopiedId(null);
        }, 2000);
        return () => clearTimeout(timer);
      })
      .catch(err => {
        console.error('Failed to copy text:', err);
      });
  }, []);

  const handleViewUpdateLog = useCallback((script) => {
    setSelectedScript(script);
    setShowUpdateLog(true);
  }, []);

  const handleCloseUpdateLog = useCallback(() => {
    setShowUpdateLog(false);
  }, []);

  const handleFilterChange = useCallback((key, value) => {
    if (key === 'category') {
      setSelectedCategory(value);
    } else if (key === 'executor') {
      setSelectedExecutor(value);
    }
  }, []);

  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[60vh]">
        <FiLoader className="animate-spin h-12 w-12 text-blue-500 mb-4" />
        <p className="text-gray-600 dark:text-gray-400">Loading scripts...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[60vh] text-center">
        <FiAlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Error Loading Scripts</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
          className="mt-2"
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Scripts</h1>
          <p className="text-gray-600 dark:text-gray-400">
            {filteredScripts.length} {filteredScripts.length === 1 ? 'script' : 'scripts'} available
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => setShowExecutorSupport(true)}>
            <FiInfo className="mr-2 h-4 w-4" />
            Executor Support
          </Button>
        </div>
      </div>
      <div className="flex flex-col md:flex-row gap-4 mb-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FiSearch className="h-5 w-5 text-foreground/50" />
          </div>
          <input
            type="text"
            placeholder="Search scripts..."
            className="pl-10 w-full py-2 px-4 bg-background border border-border rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-colors"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </div>
        <Button 
          variant="outline" 
          onClick={() => handleFilterChange('category', 'all')}
          className="flex items-center gap-2"
        >
          <FiFilter className="h-4 w-4" />
          {selectedCategory === 'all' ? 'Show Filters' : 'Hide Filters'}
        </Button>
      </div>
      {selectedCategory !== 'all' && (
        <Card className="p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">Category</h3>
              <div className="flex flex-wrap gap-2">
                {CATEGORIES.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleFilterChange('category', category.id)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-2">Executor</h3>
              <div className="flex flex-wrap gap-2">
                <Button
                  key="all"
                  variant={selectedExecutor === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleFilterChange('executor', 'all')}
                >
                  All
                </Button>
                <Button
                  key="synapse"
                  variant={selectedExecutor === 'synapse' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleFilterChange('executor', 'synapse')}
                >
                  Synapse
                </Button>
                <Button
                  key="krnl"
                  variant={selectedExecutor === 'krnl' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleFilterChange('executor', 'krnl')}
                >
                  KRNL
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}
      <Tabs 
        value={activeTab} 
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2 mb-6 max-w-md">
          <TabsTrigger value="script">Script Library</TabsTrigger>
          <TabsTrigger value="executor">Executor Support</TabsTrigger>
        </TabsList>
        
        <TabsContent value="script" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredScripts.length > 0 ? (
              filteredScripts.map((script) => (
                <Card key={script.id} className="overflow-hidden hover:shadow-lg transition-shadow flex flex-col h-full">
                  <div className="p-6 flex-grow">
                    <div className="flex items-center justify-between mb-2">
                      <h2 className="text-xl font-semibold">{script.name || 'Untitled Script'}</h2>
                      <div className="flex items-center space-x-1">
                        <FiStar className="text-yellow-500 fill-yellow-500" />
                        <span className="text-sm font-medium">{script.rating || 0}</span>
                      </div>
                    </div>
                    
                    <p className="text-muted-foreground mb-4">{script.description || 'No description available'}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-4">
                      {script.tags && JSON.parse(script.tags).map((tag, index) => (
                        <Badge key={index} variant="secondary">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="text-sm space-y-1.5 mb-4">
                      <div className="flex items-center text-muted-foreground">
                        <FiDownload className="h-4 w-4 mr-1.5 flex-shrink-0" />
                        <span>{script.views || '0'} views</span>
                      </div>
                      <div className="flex items-center text-muted-foreground">
                        <FiClock className="h-4 w-4 mr-1.5 flex-shrink-0" />
                        <span>v{script.version || '1.0.0'} • Updated {formatDate(script.updated_at || 'N/A')}</span>
                      </div>
                      <div className="flex items-center text-muted-foreground">
                        <FiCode className="h-4 w-4 mr-1.5 flex-shrink-0" />
                        <span>Works with: {script.executor || 'Various'}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="border-t border-border p-4 bg-muted/10">
                    <div className="flex space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1"
                        onClick={() => handleViewUpdateLog(script)}
                      >
                        <FiInfo className="h-4 w-4 mr-1.5" />
                        Update Log
                      </Button>
                      <Button 
                        variant="default" 
                        size="sm" 
                        className="flex-1"
                        onClick={() => copyToClipboard(script.content || 'No content available', script.id)}
                      >
                        {copied && copiedId === script.id ? (
                          <>
                            <FiCheck className="h-4 w-4 mr-1.5" />
                            Copied!
                          </>
                        ) : (
                          <>
                            <FiCopy className="h-4 w-4 mr-1.5" />
                            Copy Script
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No scripts found</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {scripts.length === 0 
                    ? 'No scripts have been added yet. Please check back later.' 
                    : 'Try adjusting your search or filter to find what you\'re looking for.'}
                </p>
              </div>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="executor" className="mt-0">
          <ExecutorSupport />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default Scripts;
