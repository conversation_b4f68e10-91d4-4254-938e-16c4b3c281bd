import { motion } from 'framer-motion';
import { FiMessageCircle, FiHelpCircle } from 'react-icons/fi';
import { Card } from '../components/ui/Card';
import { Link } from 'react-router-dom';

const Contact = () => {
  const contactMethods = [
    {
      icon: <FiMessageCircle className="h-8 w-8 text-primary" />,
      title: 'Discord',
      description: 'Join our community for help and support',
      value: 'Join Our Discord',
      action: 'https://discord.gg/uh',
      isExternal: true,
    },
    {
      icon: <FiHelpCircle className="h-8 w-8 text-primary" />,
      title: 'FAQs',
      description: 'Find answers to common questions',
      value: 'View FAQ',
      action: '/faq',
      isExternal: false,
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <motion.h1 
              className="text-4xl font-bold mb-4 text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-600"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Contact Us
            </motion.h1>
            <motion.p 
              className="text-lg text-foreground/70 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Have questions or need support? Our team is here to help you with any inquiries.
            </motion.p>
          </div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {contactMethods.map((method, index) => (
              <Card key={index} className="p-8 hover:shadow-md transition-shadow text-center">
                <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4 mx-auto">
                  {method.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2">{method.title}</h3>
                <p className="text-foreground/70 mb-6">{method.description}</p>
                {method.isExternal ? (
                  <a 
                    href={method.action} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center px-6 py-2 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary/90 transition-colors duration-200"
                  >
                    {method.value}
                  </a>
                ) : (
                  <Link 
                    to={method.action}
                    className="inline-flex items-center justify-center px-6 py-2 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary/90 transition-colors duration-200"
                  >
                    {method.value}
                  </Link>
                )}
              </Card>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
