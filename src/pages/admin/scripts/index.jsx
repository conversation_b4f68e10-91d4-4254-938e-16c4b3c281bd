import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../../components/ui/Tabs';
import { Button } from '../../../components/ui/Button';
import {
  FiPlus,
  FiUpload,
  FiEdit,
  FiList,
  FiClock,
  FiTrash2,
  FiCheck,
  FiX,
  FiLoader,
  FiAlertCircle,
  FiFileText
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { scriptsAPI } from '../../../lib/api';
import { Input } from '../../../components/ui/Input';
import { Badge } from '../../../components/ui/Badge';
import UpdateLogManager from '../../../components/scripts/UpdateLogManager';

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

export default function ScriptsManagement() {
  const [activeTab, setActiveTab] = useState('active');
  const [scripts, setScripts] = useState([]);
  const [selectedScript, setSelectedScript] = useState(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch scripts from API
  useEffect(() => {
    const fetchScripts = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await scriptsAPI.list();
        setScripts(Array.isArray(data) ? data : []);
      } catch (err) {
        setError(err.message);
        toast.error(`Failed to load scripts: ${err.message}`);
      } finally {
        setIsLoading(false);
      }
    };
    fetchScripts();
  }, []);

  const handleUploadScript = async (newScript) => {
    try {
      const savedScript = await scriptsAPI.create(newScript);
      setScripts([...scripts, savedScript]);
      setIsUploadModalOpen(false);
      toast.success('Script uploaded successfully');
    } catch (err) {
      toast.error(err.message || 'Failed to upload script');
    }
  };

  const handleUpdateScript = async (updatedScript) => {
    try {
      const { id, ...updates } = updatedScript;
      const savedScript = await scriptsAPI.update(id, updates);
      setScripts(scripts.map(s => s.id === savedScript.id ? savedScript : s));
      setSelectedScript(null);
      toast.success('Script updated successfully');
    } catch (err) {
      toast.error(err.message || 'Failed to update script');
    }
  };

  const handleDeleteScript = async (scriptId) => {
    if (!window.confirm('Are you sure you want to delete this script? This action cannot be undone.')) {
      return;
    }
    try {
      await scriptsAPI.delete(scriptId);
      setScripts(scripts.filter(s => s.id !== scriptId));
      if (selectedScript?.id === scriptId) {
        setSelectedScript(null);
      }
      toast.success('Script deleted successfully');
    } catch (err) {
      toast.error(err.message || 'Failed to delete script');
    }
  };

  const toggleScriptStatus = async (scriptId, currentStatus) => {
    try {
      const updatedStatus = !currentStatus;
      await scriptsAPI.toggleStatus(scriptId, updatedStatus);
      setScripts(scripts.map(s =>
        s.id === scriptId ? { ...s, is_active: updatedStatus } : s
      ));
      toast.success(`Script ${updatedStatus ? 'activated' : 'deactivated'} successfully`);
    } catch (err) {
      toast.error(err.message || 'Failed to update script status');
    }
  };

  const handleAddUpdateLog = async (scriptId, updateLog) => {
    try {
      await scriptsAPI.addUpdateLog(scriptId, updateLog);
      // Optionally, refetch or update local state
      toast.success('Update log added');
    } catch (error) {
      toast.error('Failed to add update log');
    }
  };

  // Filter scripts based on active tab
  const filteredScripts = React.useMemo(() => {
    switch (activeTab) {
      case 'active':
        return scripts.filter(script => script.is_active);
      case 'inactive':
        return scripts.filter(script => !script.is_active);
      case 'all':
      default:
        return scripts;
    }
  }, [scripts, activeTab]);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Scripts Management</h1>
          <p className="text-muted-foreground">Manage and upload scripts for your application</p>
        </div>
        <Button onClick={() => setIsUploadModalOpen(true)}>
          <FiUpload className="mr-2 h-4 w-4" />
          Upload Script
        </Button>
      </div>
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
          <TabsTrigger value="all">All Scripts</TabsTrigger>
        </TabsList>
        <TabsContent value={activeTab} className="mt-6">
          {selectedScript ? (
            <ScriptEditForm
              script={selectedScript}
              onSave={handleUpdateScript}
              onCancel={() => setSelectedScript(null)}
              onAddUpdateLog={handleAddUpdateLog}
            />
          ) : (
            <ScriptsList
              scripts={filteredScripts}
              onEdit={setSelectedScript}
              onAddUpdateLog={handleAddUpdateLog}
              onDelete={handleDeleteScript}
              onToggleStatus={toggleScriptStatus}
              isLoading={isLoading}
            />
          )}
        </TabsContent>
      </Tabs>
      {isUploadModalOpen && (
        <ScriptUploadModal
          onClose={() => setIsUploadModalOpen(false)}
          onUpload={handleUploadScript}
        />
      )}
    </div>
  );
}

// ScriptsList, ScriptEditForm, UpdateLogs, ScriptUploadModal remain as before, but all CRUD operations now use scriptsAPI and real backend data.
