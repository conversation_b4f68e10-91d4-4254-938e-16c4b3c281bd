import React, { useState, useEffect } from 'react';
import { FiU<PERSON>s, FiKey, FiActivity, FiFileText, FiTrendingUp, FiClock, FiRefreshCw } from 'react-icons/fi';
import { AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { useSecurity } from '../../context/SecurityContext';
import { formatDistanceToNow } from 'date-fns';

export default function Dashboard() {
  const { securityService } = useSecurity();
  const [timeRange, setTimeRange] = useState('7d');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [stats, setStats] = useState([
    { id: 1, name: 'Total Users', value: 0, icon: FiUsers, change: 0, changeType: 'neutral' },
    { id: 2, name: 'Active Keys', value: 0, icon: FiKey, change: 0, changeType: 'neutral' },
    { id: 3, name: 'Script Requests', value: 0, icon: FiFileText, change: 0, changeType: 'neutral' },
    { id: 4, name: 'Avg. Session', value: '0s', icon: FiClock, change: 0, changeType: 'neutral' },
  ]);
  const [activityData, setActivityData] = useState([]);
  const [chartData, setChartData] = useState([]);
  const [pieData, setPieData] = useState([]);
  const [loading, setLoading] = useState(false); // Start with false since we're using mock data
  const [error, setError] = useState(null);

  // Mock data for dashboard
  const mockStats = {
    totalUsers: 1242,
    userChange: 12.5,
    activeKeys: 843,
    keyChange: 5.2,
    scriptRequests: 1567,
    requestChange: 23.1,
    avgSession: '2m 45s',
    sessionChange: -3.4
  };

  // Mock activity data
  const mockActivity = [
    { id: 1, action: 'User login', user: 'admin', timestamp: new Date(Date.now() - 1000 * 60 * 5) },
    { id: 2, action: 'Script updated', user: 'developer', timestamp: new Date(Date.now() - 1000 * 60 * 30) },
    { id: 3, action: 'New API key generated', user: 'admin', timestamp: new Date(Date.now() - 1000 * 60 * 60) },
    { id: 4, action: 'Security scan completed', user: 'system', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2) },
    { id: 5, action: 'User registered', user: 'newuser', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5) },
  ];

  // Mock chart data
  const mockChartData = [
    { name: 'Jan', users: 400, requests: 2400 },
    { name: 'Feb', users: 300, requests: 1398 },
    { name: 'Mar', users: 200, requests: 9800 },
    { name: 'Apr', users: 278, requests: 3908 },
    { name: 'May', users: 189, requests: 4800 },
    { name: 'Jun', users: 239, requests: 3800 },
    { name: 'Jul', users: 349, requests: 4300 },
  ];

  // Mock pie data
  const mockPieData = [
    { name: 'Games', value: 400 },
    { name: 'Utilities', value: 300 },
    { name: 'Other', value: 200 },
  ];

  // Initialize with mock data
  const updateDashboardData = () => {
    try {
      setLoading(true);
      setError(null);
      
      // Use mock data directly
      setStats([
        { id: 1, name: 'Total Users', value: mockStats.totalUsers, icon: FiUsers, change: mockStats.userChange, changeType: mockStats.userChange >= 0 ? 'increase' : 'decrease' },
        { id: 2, name: 'Active Keys', value: mockStats.activeKeys, icon: FiKey, change: mockStats.keyChange, changeType: mockStats.keyChange >= 0 ? 'increase' : 'decrease' },
        { id: 3, name: 'Script Requests', value: mockStats.scriptRequests, icon: FiFileText, change: mockStats.requestChange, changeType: mockStats.requestChange >= 0 ? 'increase' : 'decrease' },
        { id: 4, name: 'Avg. Session', value: mockStats.avgSession, icon: FiClock, change: mockStats.sessionChange, changeType: mockStats.sessionChange >= 0 ? 'increase' : 'decrease' },
      ]);

      // Format activity data with relative timestamps
      const formattedActivity = mockActivity.map(activity => ({
        ...activity,
        time: formatDistanceToNow(activity.timestamp, { addSuffix: true })
      }));
      
      setActivityData(formattedActivity);
      setChartData(mockChartData);
      setPieData(mockPieData);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initialize and update data when timeRange changes
  useEffect(() => {
    updateDashboardData();
  }, [timeRange]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    updateDashboardData();
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's what's happening with your system.</p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-card border rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-primary/50"
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <button
            onClick={handleRefresh}
            className="p-2 rounded-md hover:bg-muted transition-colors"
            disabled={isRefreshing}
          >
            <FiRefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
          <button 
            onClick={handleRefresh}
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
          >
            <FiRefreshCw className={`h-5 w-5 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      ) : loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Stats Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat) => (
              <div key={stat.id} className="bg-card p-6 rounded-lg border shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{stat.name}</p>
                    <p className="text-2xl font-bold mt-1">{stat.value}</p>
                    <p className={`text-sm mt-1 ${
                      stat.changeType === 'increase' ? 'text-green-500' : 'text-red-500'
                    }`}>
                      {stat.change >= 0 ? '+' : ''}{stat.change}% from last period
                    </p>
                  </div>
                  <div className="p-3 rounded-lg bg-primary/10 text-primary">
                    <stat.icon className="h-6 w-6" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Activity and Key Status Row */}
          <div className="grid gap-6 md:grid-cols-3">
            {/* Key Metrics */}
            <div className="bg-card p-6 rounded-lg border shadow-sm">
              <h3 className="text-lg font-semibold mb-4">Key Metrics</h3>
              <div className="space-y-4">
                {[
                  { label: 'Active Sessions', value: '24', change: '+2', changeType: 'increase' },
                  { label: 'API Success Rate', value: '99.8%', change: '+0.2%', changeType: 'increase' },
                  { label: 'Response Time', value: '128ms', change: '-12ms', changeType: 'decrease' },
                  { label: 'Error Rate', value: '0.2%', change: '-0.1%', changeType: 'decrease' },
                ].map((metric, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">{metric.label}</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{metric.value}</span>
                      <span className={`text-xs ${
                        metric.changeType === 'increase' ? 'text-green-500' : 'text-red-500'
                      }`}>
                        {metric.change}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Charts */}
            <div className="bg-card p-6 rounded-lg border shadow-sm">
              <h3 className="text-lg font-semibold mb-4">Usage Overview</h3>
              {chartData.length > 0 ? (
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={chartData}>
                      <defs>
                        <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#8884d8" stopOpacity={0.1}/>
                        </linearGradient>
                        <linearGradient id="colorRequests" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#82ca9d" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis dataKey="name" />
                      <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                      <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                      <Tooltip />
                      <Area yAxisId="left" type="monotone" dataKey="users" name="Users" stroke="#8884d8" fillOpacity={1} fill="url(#colorUsers)" />
                      <Area yAxisId="right" type="monotone" dataKey="requests" name="Requests" stroke="#82ca9d" fillOpacity={1} fill="url(#colorRequests)" />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="h-80 flex items-center justify-center text-muted-foreground">
                  No chart data available
                </div>
              )}
            </div>

            {/* Key Status */}
            <div className="bg-card p-6 rounded-lg border shadow-sm">
              <h3 className="text-lg font-semibold mb-4">Key Status</h3>
              {pieData.length > 0 ? (
                <>
                  <div className="h-80 flex items-center justify-center">
                    <PieChart width={300} height={300}>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </div>
                  <div className="flex flex-wrap justify-center gap-4 mt-4">
                    {pieData.map((item, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: item.color }} />
                        <span className="text-sm">{item.name}: {item.value}%</span>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="h-80 flex items-center justify-center text-muted-foreground">
                  No key status data available
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
