import { useState, useEffect } from 'react';
import { FiSave, FiGlobe, FiShield, FiKey, FiCode, FiMail, FiLock } from 'react-icons/fi';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Switch } from '../../components/ui/Switch';
import { Label } from '../../components/ui/Label';
import { toast } from 'react-hot-toast';
import axios from 'axios';

export default function Settings() {
  const [formData, setFormData] = useState({
    siteName: '',
    siteUrl: '',
    adminEmail: '',
    maintenanceMode: false,
    enableKeyGeneration: true,
    maxKeysPerUser: 3,
    scriptAutoApprove: false,
    sessionTimeout: 60,
    enable2FA: false,
    enableEmailNotifications: true
  });

  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(true);

  // Fetch settings from backend on mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/.netlify/functions/admin/site-settings');
        setFormData(response.data);
      } catch (error) {
        toast.error('Failed to load settings');
      } finally {
        setLoading(false);
      }
    };
    fetchSettings();
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    try {
      await axios.put('/.netlify/functions/admin/site-settings', formData);
      toast.success('Settings saved successfully!');
    } catch (error) {
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return <div className="p-8 text-center">Loading settings...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Configure essential system settings
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* General Settings */}
        <div className="space-y-4 rounded-lg border p-6">
          <div className="flex items-center space-x-2">
            <FiGlobe className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-medium">General</h2>
          </div>
          <div className="space-y-4 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="siteName">Site Name</Label>
                <Input
                  id="siteName"
                  name="siteName"
                  value={formData.siteName}
                  onChange={handleChange}
                  placeholder="Enter site name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="siteUrl">Site URL</Label>
                <Input
                  id="siteUrl"
                  name="siteUrl"
                  type="url"
                  value={formData.siteUrl}
                  onChange={handleChange}
                  placeholder="https://example.com"
                />
              </div>
            </div>
            <div className="flex items-center justify-between pt-2">
              <div className="space-y-0.5">
                <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                <p className="text-sm text-muted-foreground">
                  Take the site offline for maintenance
                </p>
              </div>
              <Switch
                id="maintenanceMode"
                name="maintenanceMode"
                checked={formData.maintenanceMode}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, maintenanceMode: checked }))
                }
              />
            </div>
          </div>
        </div>
        {/* Key Management */}
        <div className="space-y-4 rounded-lg border p-6">
          <div className="flex items-center space-x-2">
            <FiKey className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-medium">Key Management</h2>
          </div>
          <div className="space-y-4 pt-2">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="enableKeyGeneration">Enable Key Generation</Label>
                <p className="text-sm text-muted-foreground">
                  Allow users to generate new keys
                </p>
              </div>
              <Switch
                id="enableKeyGeneration"
                name="enableKeyGeneration"
                checked={formData.enableKeyGeneration}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, enableKeyGeneration: checked }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="maxKeysPerUser">Max Keys Per User</Label>
              <Input
                id="maxKeysPerUser"
                name="maxKeysPerUser"
                type="number"
                min="1"
                value={formData.maxKeysPerUser}
                onChange={handleChange}
                className="w-24"
                disabled={!formData.enableKeyGeneration}
              />
              <p className="text-sm text-muted-foreground">
                Maximum number of keys a single user can generate
              </p>
            </div>
          </div>
        </div>
        {/* Script Settings */}
        <div className="space-y-4 rounded-lg border p-6">
          <div className="flex items-center space-x-2">
            <FiCode className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-medium">Script Settings</h2>
          </div>
          <div className="space-y-4 pt-2">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="scriptAutoApprove">Auto-Approve Scripts</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically approve new script submissions
                </p>
              </div>
              <Switch
                id="scriptAutoApprove"
                name="scriptAutoApprove"
                checked={formData.scriptAutoApprove}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, scriptAutoApprove: checked }))
                }
              />
            </div>
          </div>
        </div>
        {/* Security */}
        <div className="space-y-4 rounded-lg border p-6">
          <div className="flex items-center space-x-2">
            <FiShield className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-medium">Security</h2>
          </div>
          <div className="space-y-4 pt-2">
            <div className="space-y-2">
              <Label>Admin Session Timeout</Label>
              <div className="flex space-x-2">
                <select
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={formData.sessionTimeout}
                  onChange={(e) => setFormData(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
                >
                  <option value="15">15 minutes</option>
                  <option value="30">30 minutes</option>
                  <option value="60">1 hour</option>
                  <option value="1440">24 hours</option>
                </select>
              </div>
              <p className="text-sm text-muted-foreground">
                Time before admin session expires due to inactivity
              </p>
            </div>
            <div className="flex items-center justify-between pt-2">
              <div className="space-y-0.5">
                <Label htmlFor="enable2FA">Two-Factor Authentication</Label>
                <p className="text-sm text-muted-foreground">
                  Add an extra layer of security to your account
                </p>
              </div>
              <Switch
                id="enable2FA"
                name="enable2FA"
                checked={formData.enable2FA}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, enable2FA: checked }))
                }
              />
            </div>
          </div>
        </div>
        {/* Notifications */}
        <div className="space-y-4 rounded-lg border p-6">
          <div className="flex items-center space-x-2">
            <FiMail className="h-5 w-5 text-primary" />
            <h2 className="text-lg font-medium">Notifications</h2>
          </div>
          <div className="space-y-4 pt-2">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="enableEmailNotifications">Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive important notifications via email
                </p>
              </div>
              <Switch
                id="enableEmailNotifications"
                name="enableEmailNotifications"
                checked={formData.enableEmailNotifications}
                onCheckedChange={(checked) =>
                  setFormData(prev => ({ ...prev, enableEmailNotifications: checked }))
                }
              />
            </div>
          </div>
        </div>
        <div className="flex justify-end pt-4">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <FiSave className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
