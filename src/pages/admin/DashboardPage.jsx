import { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  Flex, 
  Grid, 
  Heading, 
  Text, 
  Badge, 
  Button,
  Table,
  Spinner
} from '@radix-ui/themes';
import { 
  ActivityLogIcon, 
  CodeIcon, 
  FileTextIcon, 
  UsersIcon,
  ArrowRightIcon,
  ClockIcon,
  CheckCircledIcon,
  CrossCircledIcon
} from '@radix-ui/react-icons';
import { useAuthContext } from '@/hooks/useAuth';
import { 
  scriptsAPI, 
  activityLogsAPI 
} from '@/lib/api';
import { formatDistanceToNow } from 'date-fns';

// Stat card component
function StatCard({ title, value, icon, trend, trendType = 'neutral' }) {
  const trendColors = {
    positive: 'green',
    negative: 'red',
    neutral: 'gray'
  };
  
  return (
    <Card className="h-full">
      <Flex direction="column" gap="2">
        <Flex justify="between" align="center">
          <Text size="2" color="gray">{title}</Text>
          <Box 
            className={`p-2 rounded-md ${
              trendType === 'positive' ? 'bg-green-50 text-green-600' :
              trendType === 'negative' ? 'bg-red-50 text-red-600' :
              'bg-gray-50 text-gray-600'
            }`}
          >
            {icon}
          </Box>
        </Flex>
        <Flex align="baseline" gap="2">
          <Heading size="6">{value}</Heading>
          {trend && (
            <Badge 
              color={trendColors[trendType]}
              variant="soft"
              className="text-xs"
            >
              {trend}
            </Badge>
          )}
        </Flex>
      </Flex>
    </Card>
  );
}

// Recent activity item component
function ActivityItem({ activity }) {
  const getActivityIcon = () => {
    switch(activity.action) {
      case 'script.created':
        return <CodeIcon className="h-4 w-4 text-green-500" />;
      case 'script.updated':
        return <CodeIcon className="h-4 w-4 text-blue-500" />;
      case 'script.deleted':
        return <CodeIcon className="h-4 w-4 text-red-500" />;
      case 'api_key.created':
        return <FileTextIcon className="h-4 w-4 text-amber-500" />;
      case 'api_key.revoked':
        return <FileTextIcon className="h-4 w-4 text-red-500" />;
      case 'login':
        return <CheckCircledIcon className="h-4 w-4 text-green-500" />;
      default:
        return <ActivityLogIcon className="h-4 w-4 text-gray-500" />;
    }
  };
  
  const getActivityMessage = () => {
    const details = activity.details || {};
    
    switch(activity.action) {
      case 'script.created':
        return `Created script "${details.title || 'Untitled'}"`;
      case 'script.updated':
        return `Updated script "${details.title || 'Untitled'}"`;
      case 'script.deleted':
        return `Deleted script "${details.title || 'Untitled'}"`;
      case 'api_key.created':
        return `Created new API key "${details.name || 'Untitled'}"`;
      case 'api_key.revoked':
        return `Revoked API key "${details.name || 'Untitled'}"`;
      case 'login':
        return 'Successfully logged in';
      default:
        return activity.action;
    }
  };
  
  return (
    <tr>
      <td className="py-3">
        <Flex align="center" gap="3">
          <div className="p-1.5 rounded-md bg-gray-50">
            {getActivityIcon()}
          </div>
          <div>
            <Text as="div" size="2" weight="medium">
              {getActivityMessage()}
            </Text>
            <Text as="div" size="1" color="gray">
              {activity.admin?.username || 'System'}
            </Text>
          </div>
        </Flex>
      </td>
      <td className="text-right">
        <Text as="div" size="1" color="gray">
          {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
        </Text>
      </td>
    </tr>
  );
}

export default function DashboardPage() {
  const [stats, setStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const [scriptsData, publicScriptsData, activityData] = await Promise.all([
          scriptsAPI.list({ limit: 1 }),
          scriptsAPI.list({ is_public: true, limit: 1 }),
          activityLogsAPI.list({ limit: 5, sort_by: 'created_at', sort_order: 'desc' })
        ]);

        setStats({
          totalScripts: scriptsData.pagination?.total || 0,
          publicScripts: publicScriptsData.pagination?.total || 0,
          activeUsers: 0, // Placeholder
          recentActivity: activityData.data || []
        });
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (isLoading) {
    return (
      <Flex align="center" justify="center" className="h-64">
        <Spinner size="3" />
      </Flex>
    );
  }

  if (error) {
    return (
      <Card variant="surface">
        <Flex direction="column" align="center" gap="4" py="6">
          <Box className="p-3 rounded-full bg-red-50">
            <CrossCircledIcon className="h-6 w-6 text-red-500" />
          </Box>
          <Text color="red">{error}</Text>
          <Button 
            variant="soft" 
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </Flex>
      </Card>
    );
  }

  return (
    <Box className="space-y-6">
      {/* Welcome header */}
      <Flex direction="column" gap="2">
        <Heading size="7">Dashboard</Heading>
        <Text color="gray">
          Welcome back! Here's what's happening with your admin dashboard.
        </Text>
      </Flex>
      
      {/* Stats grid */}
      <Grid columns={{ initial: '1', sm: '2', lg: '4' }} gap="4">
        <StatCard 
          title="Total Scripts" 
          value={stats.totalScripts} 
          icon={<CodeIcon className="h-4 w-4" />}
          trend="+12%"
          trendType="positive"
        />
        <StatCard 
          title="Public Scripts" 
          value={stats.publicScripts} 
          icon={<FileTextIcon className="h-4 w-4" />}
          trend="+5%"
          trendType="positive"
        />
        <StatCard 
          title="Active Users" 
          value={stats.activeUsers || 'N/A'} 
          icon={<UsersIcon className="h-4 w-4" />}
          trend="+24%"
          trendType="positive"
        />
        <StatCard 
          title="Uptime" 
          value="99.9%" 
          icon={<ClockIcon className="h-4 w-4" />}
          trend="Stable"
          trendType="neutral"
        />
      </Grid>
      
      {/* Recent activity */}
      <Card>
        <Flex justify="between" align="center" mb="4">
          <Heading size="4">Recent Activity</Heading>
          <Button 
            variant="ghost" 
            size="1" 
            asChild
          >
            <a href="/admin/activity-logs">
              View all <ArrowRightIcon className="ml-1 h-3 w-3" />
            </a>
          </Button>
        </Flex>
        
        {stats.recentActivity.length > 0 ? (
          <Table.Root>
            <Table.Body>
              {stats.recentActivity.map((activity) => (
                <ActivityItem 
                  key={activity.id} 
                  activity={activity} 
                />
              ))}
            </Table.Body>
          </Table.Root>
        ) : (
          <Box className="py-8 text-center">
            <Text color="gray">No recent activity found</Text>
          </Box>
        )}
      </Card>
      
      {/* Quick actions */}
      <Grid columns={{ initial: '1', md: '2' }} gap="4">
        <Card>
          <Flex direction="column" gap="4">
            <Heading size="4">Quick Actions</Heading>
            <Grid columns="2" gap="3">
              <Button variant="surface" asChild>
                <a href="/admin/scripts/new">
                  <CodeIcon className="mr-2 h-4 w-4" />
                  New Script
                </a>
              </Button>
              <Button variant="surface" asChild>
                <a href="/admin/api-keys/new">
                  <FileTextIcon className="mr-2 h-4 w-4" />
                  Create API Key
                </a>
              </Button>
              <Button variant="surface" asChild>
                <a href="/admin/settings">
                  <GearIcon className="mr-2 h-4 w-4" />
                  Settings
                </a>
              </Button>
              <Button variant="surface" asChild>
                <a href="/admin/activity-logs">
                  <ActivityLogIcon className="mr-2 h-4 w-4" />
                  View Logs
                </a>
              </Button>
            </Grid>
          </Flex>
        </Card>
        
        <Card>
          <Flex direction="column" gap="4">
            <Heading size="4">System Status</Heading>
            <Box className="space-y-3">
              <Flex justify="between" align="center">
                <Text>API Status</Text>
                <Badge color="green" variant="soft">
                  Operational
                </Badge>
              </Flex>
              <Flex justify="between" align="center">
                <Text>Database</Text>
                <Badge color="green" variant="soft">
                  Connected
                </Badge>
              </Flex>
              <Flex justify="between" align="center">
                <Text>Storage</Text>
                <Text>2.4 GB / 10 GB</Text>
              </Flex>
              <Box className="h-2 bg-gray-100 rounded-full overflow-hidden">
                <Box 
                  className="h-full bg-green-500" 
                  style={{ width: '24%' }} 
                />
              </Box>
            </Box>
          </Flex>
        </Card>
      </Grid>
    </Box>
  );
}
