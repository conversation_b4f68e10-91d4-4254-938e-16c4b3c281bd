import { useState, useEffect } from 'react';
import { FiFileText, FiCheck, FiX, FiClock, FiSearch, FiFilter, FiDownload, FiUser } from 'react-icons/fi';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Input } from '../../components/ui/Input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/ui/Dropdown';

const statuses = {
  pending: { label: 'Pending', color: 'bg-amber-500/20 text-amber-600 dark:text-amber-400 border-amber-500/30' },
  approved: { label: 'Approved', color: 'bg-green-500/20 text-green-600 dark:text-green-400 border-green-500/30' },
  denied: { label: 'Denied', color: 'bg-red-500/20 text-red-600 dark:text-red-400 border-red-500/30' },
  completed: { label: 'Completed', color: 'bg-blue-500/20 text-blue-600 dark:text-blue-400 border-blue-500/30' },
};

export default function ScriptRequests() {
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState(null);

  useEffect(() => {
    const fetchRequests = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch('/.netlify/functions/get-script-requests');
        if (!response.ok) {
          const errData = await response.json();
          throw new Error(errData.error || 'Failed to fetch requests');
        }
        const data = await response.json();
        setRequests(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchRequests();
  }, []);

  const handleStatusUpdate = async (requestId, newStatus) => {
    const originalRequests = [...requests];
    
    // Optimistically update UI
    const updatedRequests = requests.map(req => 
      req.id === requestId ? { ...req, status: newStatus } : req
    );
    setRequests(updatedRequests);
    if (selectedRequest && selectedRequest.id === requestId) {
      setSelectedRequest(prev => ({ ...prev, status: newStatus }));
    }

    try {
      const response = await fetch('/.netlify/functions/update-request-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ requestId, status: newStatus }),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Failed to update status');
      }
      // Success, the optimistic update is now confirmed
    } catch (err) {
      setError(`Failed to update status: ${err.message}`);
      // Revert to original state on error
      setRequests(originalRequests);
      if (selectedRequest && selectedRequest.id === requestId) {
        const originalRequest = originalRequests.find(r => r.id === requestId);
        setSelectedRequest(originalRequest);
      }
    }
  };

  const filteredRequests = requests.filter(request => {
    const matchesSearch = (request.script_name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (request.requested_by || '').toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Script Requests</h1>
          <p className="text-muted-foreground">
            Manage and review script requests from users
          </p>
        </div>
        <Button>
          <FiFileText className="mr-2 h-4 w-4" />
          New Request
        </Button>
      </div>

      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div className="flex-1">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search requests..."
              className="pl-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full md:w-auto">
              <FiFilter className="mr-2 h-4 w-4" />
              {statusFilter === 'all' ? 'All Statuses' : statuses[statusFilter]?.label || 'Filter'}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setStatusFilter('all')}>
              All Statuses
            </DropdownMenuItem>
            {Object.entries(statuses).map(([key, { label }]) => (
              <DropdownMenuItem key={key} onClick={() => setStatusFilter(key)}>
                {label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="overflow-hidden rounded-lg border bg-background shadow-sm">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted/50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                  Script Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground max-w-[200px]">
                  Description
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                  Date
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {loading ? (
                <tr>
                  <td colSpan="5" className="py-12 text-center text-muted-foreground">
                    Loading requests...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan="5" className="py-12 text-center text-red-500">
                    Error: {error}
                  </td>
                </tr>
              ) : filteredRequests.length > 0 ? (
                filteredRequests.map((request) => (
                  <tr key={request.id} className="cursor-pointer hover:bg-muted/50" onClick={() => setSelectedRequest(request)}>
                    <td className="px-6 py-4 max-w-xs truncate" title={request.script_name}>
                      <span className="text-sm font-medium text-foreground line-clamp-1">
                        {request.script_name}
                      </span>
                    </td>
                    <td className="px-6 py-4 max-w-[200px] truncate" title={request.description}>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {request.description || 'No description provided'}
                      </p>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      <Badge className={statuses[request.status]?.color}>
                        {statuses[request.status]?.label}
                      </Badge>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-muted-foreground">
                      {new Date(request.created_at).toLocaleDateString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end">
                        <Button variant="ghost" size="sm">View</Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="5" className="py-12 text-center text-muted-foreground">
                    <p>
                      {searchQuery || statusFilter !== 'all'
                        ? 'No requests match your filters.'
                        : 'No script requests have been submitted yet.'}
                    </p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {selectedRequest && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4" onClick={() => setSelectedRequest(null)}>
          <div className="w-full max-w-2xl rounded-lg bg-background p-6 shadow-lg" onClick={e => e.stopPropagation()}>
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-xl font-bold">{selectedRequest.script_name}</h2>
                <div className="mt-1 flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">
                    Requested by {selectedRequest.requested_by}
                  </span>
                  <span className="text-muted-foreground">•</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(selectedRequest.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setSelectedRequest(null)}
              >
                <FiX className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="mt-6 space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Description</h3>
                <p className="mt-1 rounded-md bg-muted/50 p-3 break-words">
                  {selectedRequest.notes}
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                <div className="mt-1">
                  <Badge className={statuses[selectedRequest.status]?.color}>
                    {statuses[selectedRequest.status]?.label}
                  </Badge>
                </div>
              </div>
              
              <div className="pt-4">
                <h3 className="text-sm font-medium text-muted-foreground">Actions</h3>
                <div className="mt-2 flex flex-wrap gap-2">
                  
                  {selectedRequest.status === 'pending' && (
                    <>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-green-600 hover:bg-green-500/10 hover:text-green-600 dark:text-green-400"
                        onClick={() => handleStatusUpdate(selectedRequest.id, 'approved')}
                      >
                        <FiCheck className="mr-2 h-4 w-4" />
                        Approve
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-red-600 hover:bg-red-500/10 hover:text-red-600 dark:text-red-400"
                        onClick={() => handleStatusUpdate(selectedRequest.id, 'denied')}
                      >
                        <FiX className="mr-2 h-4 w-4" />
                        Deny
                      </Button>
                    </>
                  )}
                  {selectedRequest.status === 'approved' && (
                    <Button 
                      variant="default" 
                      size="sm" 
                      onClick={() => handleStatusUpdate(selectedRequest.id, 'completed')}
                    >
                      Mark as Completed
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
