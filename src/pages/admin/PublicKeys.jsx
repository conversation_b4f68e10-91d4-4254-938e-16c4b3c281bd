import React, { useState, useEffect } from 'react';
import { useApi } from '../../hooks/useApi';
import { useAuth } from '../../context/AuthContext';
import { Button, Card, Table, Modal, message, Space, Tag, Form, DatePicker, Alert, Input } from 'antd';
import { PlusOutlined, DeleteOutlined, CopyOutlined, KeyOutlined } from '@ant-design/icons';

import dayjs from 'dayjs';

const { TextArea } = Input;

const PublicKeys = () => {
  const { user } = useAuth();
  const [keys, setKeys] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newKey, setNewKey] = useState(null);
  const [form] = Form.useForm();
  const api = useApi();

  const fetchKeys = async () => {
    try {
      setLoading(true);
      const response = await api.get('/admin/public-keys');
      setKeys(response.data);
    } catch (error) {
      console.error('Error fetching public keys:', error);
      message.error('Failed to load public keys');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchKeys();
    }
  }, [user]);

  const handleCreateKey = async (values) => {
    try {
      const response = await api.post('/admin/public-keys', {
        name: values.name,
        description: values.description,
        expires_in_days: values.expires_in_days || 30
      });
      
      setNewKey(response.data);
      form.resetFields();
      fetchKeys();
      message.success('New key pair generated successfully');
    } catch (error) {
      console.error('Error creating key:', error);
      message.error('Failed to generate key pair');
    }
  };

  const handleRevokeKey = async (keyId) => {
    try {
      await api.delete(`/admin/public-keys?id=${keyId}`);
      message.success('Key revoked successfully');
      fetchKeys();
    } catch (error) {
      console.error('Error revoking key:', error);
      message.error('Failed to revoke key');
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    message.success('Copied to clipboard');
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.key_id}</div>
        </div>
      ),
    },
    {
      title: 'Fingerprint',
      dataIndex: 'fingerprint',
      key: 'fingerprint',
      render: (text) => (
        <div className="font-mono text-xs">
          <Tag color="blue" icon={<KeyOutlined />}>
            {text}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'status',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Revoked'}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (date) => (
        <span className={dayjs(date).isBefore(dayjs()) ? 'text-red-500' : ''}>
          {dayjs(date).format('YYYY-MM-DD')}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<CopyOutlined />}
            onClick={() => copyToClipboard(record.public_key)}
            disabled={!record.is_active}
          >
            Copy Public Key
          </Button>
          {record.is_active && (
            <Button
              danger
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => handleRevokeKey(record.id)}
            >
              Revoke
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Public Key Management</h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          Generate New Key
        </Button>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={keys}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      <Modal
        title="Generate New Key Pair"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setNewKey(null);
        }}
        footer={null}
        width={800}
      >
        {newKey ? (
          <div className="space-y-4">
            <Alert
              message="Important: Save your private key"
              description="This is the only time the private key will be shown. Make sure to save it in a secure location."
              type="warning"
              showIcon
              className="mb-4"
            />
            
            <div className="space-y-2">
              <div>
                <div className="font-medium mb-1">Key Name</div>
                <div className="p-2 bg-gray-50 rounded">{newKey.name}</div>
              </div>
              
              <div>
                <div className="font-medium mb-1">Key ID</div>
                <div className="p-2 bg-gray-50 rounded font-mono text-sm">
                  {newKey.key_id}
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<CopyOutlined />} 
                    onClick={() => copyToClipboard(newKey.key_id)}
                  />
                </div>
              </div>
              
              <div>
                <div className="font-medium mb-1">Fingerprint</div>
                <div className="p-2 bg-gray-50 rounded font-mono text-sm">
                  {newKey.fingerprint}
                  <Button 
                    type="text" 
                    size="small" 
                    icon={<CopyOutlined />} 
                    onClick={() => copyToClipboard(newKey.fingerprint)}
                  />
                </div>
              </div>
              
              <div>
                <div className="font-medium mb-1">Public Key</div>
                <div className="relative">
                  <TextArea
                    value={newKey.public_key}
                    readOnly
                    autoSize={{ minRows: 4, maxRows: 8 }}
                    className="font-mono text-xs"
                  />
                  <Button 
                    type="text" 
                    icon={<CopyOutlined />} 
                    onClick={() => copyToClipboard(newKey.public_key)}
                    className="absolute top-2 right-2"
                  />
                </div>
              </div>
              
              <div>
                <div className="font-medium mb-1">Private Key</div>
                <div className="relative">
                  <TextArea
                    value={newKey.private_key}
                    readOnly
                    autoSize={{ minRows: 4, maxRows: 8 }}
                    className="font-mono text-xs"
                  />
                  <Button 
                    type="text" 
                    icon={<CopyOutlined />} 
                    onClick={() => copyToClipboard(newKey.private_key)}
                    className="absolute top-2 right-2"
                  />
                </div>
              </div>
              
              <div className="pt-4">
                <Button 
                  type="primary" 
                  onClick={() => {
                    setIsModalVisible(false);
                    setNewKey(null);
                  }}
                >
                  Done
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateKey}
            className="space-y-4"
          >
            <Form.Item
              name="name"
              label="Key Name"
              rules={[
                { required: true, message: 'Please enter a name for this key' },
                { min: 3, message: 'Name must be at least 3 characters' }
              ]}
            >
              <Input placeholder="e.g., Production Server" />
            </Form.Item>
            
            <Form.Item
              name="description"
              label="Description (Optional)"
            >
              <TextArea rows={2} placeholder="Add a description for this key" />
            </Form.Item>
            
            <Form.Item
              name="expires_in_days"
              label="Expires In (Days)"
              initialValue={30}
              rules={[
                { required: true, message: 'Please enter expiration period' },
                { type: 'number', min: 1, message: 'Must be at least 1 day' }
              ]}
            >
              <Input type="number" min={1} />
            </Form.Item>
            
            <div className="flex justify-end space-x-2 pt-2">
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                Generate Key Pair
              </Button>
            </div>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default PublicKeys;
