import { useState } from 'react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '../../components/ui/Table';
import { <PERSON>a<PERSON>ey, FaTrash, FaSearch, FaPlus } from 'react-icons/fa';

// Mock data - replace with actual API calls
const mockKeys = [
  { id: 1, key: 'ABC123-XYZ456-789', hwid: 'HWID-12345', createdAt: '2023-06-15' },
  { id: 2, key: 'DEF456-UVW789-012', hwid: 'HWID-67890', createdAt: '2023-06-16' },
  { id: 3, key: null, hwid: null, createdAt: '2023-06-17' },
];

export default function Users() {
  const [keys, setKeys] = useState(mockKeys);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Filter keys based on search term
  const filteredKeys = keys.filter(item => 
    (item.key && item.key.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (item.hwid && item.hwid.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Generate a new key
  const generateNewKey = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      const newKey = `KEY-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      const newKeyObj = {
        id: Date.now(),
        key: newKey,
        hwid: `HWID-${Math.floor(100000 + Math.random() * 900000)}`,
        createdAt: new Date().toISOString()
      };
      setKeys([newKeyObj, ...keys]);
      setIsLoading(false);
    }, 500);
  };

  // Remove a key
  const removeKey = (keyId) => {
    setKeys(keys.map(item => 
      item.id === keyId 
        ? { ...item, key: null, hwid: null } 
        : item
    ));
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl font-bold">Key Management</h1>
          <p className="text-muted-foreground">Manage license keys and HWIDs</p>
        </div>
        <div className="w-full sm:max-w-md flex space-x-2">
          <div className="flex-1">
            <Input
              type="text"
              placeholder="Search keys or HWIDs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              icon={FaSearch}
            />
          </div>
          <Button 
            onClick={generateNewKey}
            disabled={isLoading}
            className="whitespace-nowrap"
          >
            <FaPlus className="mr-1" /> Generate Key
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div className="bg-card rounded-lg border shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Key</TableHead>
                <TableHead>HWID</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredKeys.length > 0 ? (
                filteredKeys.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-mono text-sm">
                      {item.key || 'No key'}
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {item.hwid || 'N/A'}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {formatDate(item.createdAt)}
                    </TableCell>
                    <TableCell className="text-right">
                      {item.key ? (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => removeKey(item.id)}
                          disabled={isLoading}
                        >
                          <FaTrash className="mr-1" /> Remove
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const newKey = `KEY-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
                            setKeys(keys.map(k => 
                              k.id === item.id 
                                ? { ...k, key: newKey, hwid: `HWID-${Math.floor(100000 + Math.random() * 900000)}` }
                                : k
                            ));
                          }}
                          disabled={isLoading}
                        >
                          <FaKey className="mr-1" /> Generate
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                    No keys found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
