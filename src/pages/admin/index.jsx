import { Navigate, Route, Routes, Outlet } from 'react-router-dom';
import AdminLayout from '../../components/layout/AdminLayout';
import Dashboard from './Dashboard';
import ScriptRequests from './ScriptRequests';
import Settings from './Settings';
import ScriptsManagement from './scripts';
import Users from './Users';
import PublicKeys from './PublicKeys';
import SecurityDashboard from '../../components/security/SecurityDashboard';

// This component will be rendered inside AdminLayout
function AdminContent() {
  return (
    <Routes>
      <Route index element={<Dashboard />} />
      <Route path="requests" element={<ScriptRequests />} />
      <Route path="scripts" element={<ScriptsManagement />} />
      <Route path="settings" element={<Settings />} />
      <Route path="users" element={<Users />} />
      <Route path="public-keys" element={<PublicKeys />} />
      <Route path="security" element={<SecurityDashboard />} />
      <Route path="*" element={<Navigate to="/admin" replace />} />
    </Routes>
  );
}

import { useAuth } from '../../context/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { useEffect } from 'react';

function AdminRoutes() {
  const { isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!loading && (!isAuthenticated || !isAuthenticated())) {
      navigate('/login', { replace: true, state: { from: location.pathname, message: 'Please login to access the admin dashboard.' } });
    }
  }, [isAuthenticated, loading, navigate, location]);

  if (loading) {
    return <div className="p-8 text-center">Loading admin dashboard...</div>;
  }

  if (!isAuthenticated || !isAuthenticated()) {
    return null;
  }

  return (
    <AdminLayout>
      <AdminContent />
    </AdminLayout>
  );
}

export default AdminRoutes;
