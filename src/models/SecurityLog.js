import mongoose from 'mongoose';

const securityLogSchema = new mongoose.Schema({
  // Type of security event (e.g., 'login', 'rate_limit_exceeded', 'ip_blocked')
  type: {
    type: String,
    required: true,
    index: true
  },
  
  // IP address of the client
  ip: {
    type: String,
    required: true,
    index: true
  },
  
  // User agent string
  userAgent: String,
  
  // Request path
  path: {
    type: String,
    index: true
  },
  
  // HTTP method
  method: {
    type: String,
    index: true
  },
  
  // HTTP status code
  statusCode: {
    type: Number,
    index: true
  },
  
  // Response time in milliseconds
  responseTime: Number,
  
  // Severity level (low, medium, high)
  severity: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium',
    index: true
  },
  
  // Additional details about the event
  details: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // Timestamp of the event
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  }
}, {
  // Add timestamps for createdAt and updatedAt
  timestamps: true,
  // Enable autoIndex in development
  autoIndex: process.env.NODE_ENV !== 'production'
});

// Add indexes for common query patterns
securityLogSchema.index({ type: 1, timestamp: -1 });
securityLogSchema.index({ ip: 1, timestamp: -1 });
securityLogSchema.index({ severity: 1, timestamp: -1 });

// Add a static method to get stats
securityLogSchema.statics.getStats = async function() {
  const [
    totalLogs,
    highSeverity,
    mediumSeverity,
    lowSeverity,
    recentLogs
  ] = await Promise.all([
    this.countDocuments(),
    this.countDocuments({ severity: 'high' }),
    this.countDocuments({ severity: 'medium' }),
    this.countDocuments({ severity: 'low' }),
    this.find()
      .sort({ timestamp: -1 })
      .limit(100)
      .lean()
  ]);
  
  // Calculate activity by type
  const activityByType = recentLogs.reduce((acc, log) => {
    acc[log.type] = (acc[log.type] || 0) + 1;
    return acc;
  }, {});
  
  return {
    summary: {
      totalLogs,
      highSeverity,
      mediumSeverity,
      lowSeverity
    },
    activity: {
      byType: Object.entries(activityByType).map(([type, count]) => ({
        type,
        count
      }))
    }
  };
};

// Create the model
const SecurityLog = mongoose.model('SecurityLog', securityLogSchema);

export default SecurityLog;
