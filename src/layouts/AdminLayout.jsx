import { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { 
  Box, 
  Flex, 
  Container, 
  Text, 
  Button, 
  Avatar, 
  DropdownMenu, 
  IconButton,
  Separator,
  Badge,
  Tooltip
} from '@radix-ui/themes';
import { 
  DashboardIcon, 
  FileTextIcon, 
  GearIcon, 
  LockClosedIcon, 
  ExitIcon, 
  HamburgerMenuIcon,
  Cross2Icon,
  CodeIcon,
  ActivityLogIcon,
  PersonIcon,
  KeyIcon
} from '@radix-ui/react-icons';
import { useAuthContext } from '@/hooks/useAuth';

// Sidebar navigation items
const navItems = [
  { 
    title: 'Dashboard', 
    icon: <DashboardIcon width={18} height={18} />, 
    path: '/admin/dashboard',
    permission: 'view_dashboard'
  },
  { 
    title: 'Scripts', 
    icon: <CodeIcon width={18} height={18} />, 
    path: '/admin/scripts',
    permission: 'view_scripts'
  },
  { 
    title: 'Activity Logs', 
    icon: <ActivityLogIcon width={18} height={18} />, 
    path: '/admin/activity-logs',
    permission: 'view_activity_logs'
  },
  { 
    title: 'API Keys', 
    icon: <KeyIcon width={18} height={18} />, 
    path: '/admin/api-keys',
    permission: 'manage_api_keys'
  },
  { 
    title: 'Settings', 
    icon: <GearIcon width={18} height={18} />, 
    path: '/admin/settings',
    permission: 'manage_settings'
  }
];

/**
 * Admin layout component with sidebar navigation and header
 */
export default function AdminLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { admin, logout } = useAuthContext();
  const location = useLocation();
  const navigate = useNavigate();

  // Close sidebar when route changes
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  // Handle scroll for header shadow
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  // Filter nav items based on permissions
  const filteredNavItems = navItems.filter(item => {
    if (!admin?.permissions) return false;
    return admin.permissions.includes('*') || admin.permissions.includes(item.permission);
  });

  return (
    <Flex className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <aside 
        className={`fixed inset-y-0 left-0 z-50 w-64 transform bg-white shadow-lg transition-transform duration-300 ease-in-out lg:translate-x-0 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex h-full flex-col overflow-y-auto">
          {/* Logo/Brand */}
          <div className="flex h-16 items-center justify-between border-b border-gray-200 px-6">
            <Link to="/admin/dashboard" className="flex items-center space-x-2">
              <span className="text-xl font-bold text-indigo-600">Admin</span>
            </Link>
            <button 
              className="rounded-md p-1 text-gray-500 hover:bg-gray-100 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <Cross2Icon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 space-y-1 p-4">
            {filteredNavItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center rounded-lg px-4 py-3 text-sm font-medium transition-colors ${
                  location.pathname.startsWith(item.path)
                    ? 'bg-indigo-50 text-indigo-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <span className="mr-3">{item.icon}</span>
                <span>{item.title}</span>
              </Link>
            ))}
          </nav>
          
          {/* User profile */}
          <div className="border-t border-gray-200 p-4">
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <button className="flex w-full items-center rounded-lg p-2 text-left hover:bg-gray-100">
                  <Avatar
                    src={admin?.avatar_url}
                    fallback={admin?.username?.[0]?.toUpperCase() || 'A'}
                    size="2"
                    radius="full"
                    className="mr-3"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="truncate text-sm font-medium text-gray-900">
                      {admin?.username || 'Admin'}
                    </p>
                    <p className="truncate text-xs text-gray-500">
                      {admin?.email || '<EMAIL>'}
                    </p>
                  </div>
                </button>
              </DropdownMenu.Trigger>
              
              <DropdownMenu.Content className="w-56" align="end">
                <DropdownMenu.Item>
                  <Link to="/admin/profile" className="flex w-full items-center">
                    <PersonIcon className="mr-2 h-4 w-4" />
                    Profile
                  </Link>
                </DropdownMenu.Item>
                <DropdownMenu.Separator />
                <DropdownMenu.Item color="red" onClick={handleLogout}>
                  <ExitIcon className="mr-2 h-4 w-4" />
                  Logout
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          </div>
        </div>
      </aside>
      
      {/* Main content */}
      <div className="flex flex-1 flex-col lg:pl-64">
        {/* Header */}
        <header 
          className={`sticky top-0 z-30 flex h-16 items-center justify-between border-b bg-white px-4 transition-shadow ${
            isScrolled ? 'shadow-sm' : ''
          }`}
        >
          <div className="flex items-center">
            <button 
              className="mr-2 rounded-md p-1 text-gray-500 hover:bg-gray-100 lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <HamburgerMenuIcon className="h-6 w-6" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">
              {navItems.find(item => location.pathname.startsWith(item.path))?.title || 'Dashboard'}
            </h1>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Notifications, etc. can go here */}
            <Tooltip content="Admin">
              <Badge color="indigo" variant="soft" className="hidden md:flex">
                <PersonIcon className="mr-1 h-3 w-3" />
                {admin?.role || 'Admin'}
              </Badge>
            </Tooltip>
          </div>
        </header>
        
        {/* Page content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <Container size="3" className="h-full">
            <Outlet />
          </Container>
        </main>
      </div>
    </Flex>
  );
}
